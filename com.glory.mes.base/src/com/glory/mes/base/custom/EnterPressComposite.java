package com.glory.mes.base.custom;

import java.util.List;

import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.FocusEvent;
import org.eclipse.swt.events.FocusListener;
import org.eclipse.swt.events.KeyAdapter;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.ui.forms.HeaderText;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.core.util.StringUtil;

public class EnterPressComposite extends CustomCompsite {
	
	private static final Logger logger = Logger.getLogger(EnterPressComposite.class);
	
	/**
	 * label����Ӣ��message key
	 */
	private final static String ATTR_LABEL = "Label";
	
	/**
	 * �س���ѯ��������
	 */
	private final static String ATTR_CLASS = "Class";
	
	/**
	 * �Ƿ��Զ��л�Ϊ��д
	 */
	private final static String ATTR_CASESENSITIVE = "IsCaseSensitive";
	
	/**
	 * ��ѯ��������� key�����س���text�е������Ƕ�Ӧ��ʲô�������ԣ�
	 */
	private final static String ATTR_QUERY_PROPERTY = "QueryProperty";
	
	/**
	 * �Ƿ��ѯʵ����
	 */
	private final static String ATTR_SEARCHOBJECT = "IsSearchObject";
	
	/**
	 * ͼƬ
	 */
	private final static String ATTR_IMAGE = "Image";
	
	/**
	 * �Ƿ��ѯ�ɹ�֮�������ı�
	 */
	private final static String ATTR_ISCLEARTEXT = "IsClearText";
	
	private String labelMessageKey;
	private String objectClassName;
	private String queryProperty;
	private boolean isCaseSensitive = false;
	private boolean isSearchObject = true;
	private String imageKey;
	private boolean isClearText = false;
	
	private Object value;

	private HeaderText txtLot;
	
	public EnterPressComposite() {
		super();
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		parent = super.createForm(toolkit, parent);
		
		Composite body = toolkit.createComposite(parent, SWT.NONE);
		GridLayout layout = new GridLayout(2, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
		
		
		Label label = toolkit.createLabel(body, Message.getString(labelMessageKey) + " ");
		label.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_TITLE));
		
		Image customImage = null;
		if (!StringUtil.isEmpty(imageKey)) {
			customImage = SWTResourceCache.getImage(imageKey);
		}
		
		if (customImage != null) {
			txtLot = new HeaderText(body, customImage);
		} else {
			txtLot = new HeaderText(body);
		}
		
		txtLot.setTextLimit(32);
		txtLot.setBackground(SWTResourceCache.getColor(SWTResourceCache.COLOR_WHITE));
		txtLot.addKeyListener(new KeyAdapter() {
			@Override
			public void keyPressed(KeyEvent event) {
				Text tLotId = ((Text) event.widget);
				tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_BLACK));
				switch (event.keyCode) {
				case SWT.CR:
				case SWT.KEYPAD_CR:
					ADBase adBase = null;
					String objectId = tLotId.getText();
					if (isCaseSensitive) {
						objectId = objectId.toUpperCase();
					}
					tLotId.setText(objectId);
					if(isSearchObject) {
						adBase = search(objectId);
						tLotId.selectAll();
						if (adBase == null) {
							tLotId.setForeground(SWTResourceCache.getColor(SWTResourceCache.COLOR_RED));
							setValue(null);	
							txtLot.warning();
						} else {
							setValue(adBase);
							txtLot.focusing();
							field.getParent().postEvent(GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(adBase));
							if (isClearText) {
								setValue(null); //���ɨ����֤�ɹ�������ı���
								txtLot.setText("");
							}
						}
						refresh();
						break;
					} else {
						txtLot.focusing();
						field.getParent().postEvent(GlcEvent.EVENT_ENTERPRESSED, GlcEvent.buildEventData(objectId));
					}
				}
			}
			
		});
		txtLot.addFocusListener(new FocusListener() {
			public void focusGained(FocusEvent e) {
			}

			public void focusLost(FocusEvent e) {
				Text tLotId = ((Text) e.widget);
				String lotId = tLotId.getText();
				if (isCaseSensitive) {
					lotId = lotId.toUpperCase();
				}
				tLotId.setText(lotId);
			}
		});
		
		return body;
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	protected ADBase search(String objectId) {
		try {
			Class adBase = Class.forName(objectClassName);
			String whereClause = queryProperty + " = '" + objectId + "'";
			List<ADBase> adBases = getADManger().getEntityList(
					Env.getOrgRrn(), adBase, Env.getMaxResult(), whereClause, "objectRrn");
			
			if (CollectionUtils.isNotEmpty(adBases)) {
				return adBases.get(0);
			}
			
		} catch (Exception e) {
			logger.error("error at search Method!", e);
		}
		return null;
	}

	@Override
	public void refresh() {
		if (getValue() != null && txtLot != null) {
			String text = "";
			try {
				text = DBUtil.toString(PropertyUtils.getProperty(getValue(), queryProperty));
			} catch (Exception e) {
			}
			if (isCaseSensitive) {
				text = text.toUpperCase();
			}
			
			txtLot.setText(text);
		}
	}

	public void setText(String text, Boolean isEnabled) {
		txtLot.setEnabled(isEnabled);
		txtLot.setText(text);
	}
	
	public String getTextValue() {
		if (txtLot != null) {
			return txtLot.getText();
		}
		
		return "";
	}
	
	@Override
	public void setValue(Object value) {
		this.value = value;
	}

	@Override
	public Object getValue() {
		return value;
	}
	
	public HeaderText getTxtLot() {
		return txtLot;
	}

	public void setTxtLot(HeaderText txtLot) {
		this.txtLot = txtLot;
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		for (ADFormAttribute formAttribute : attributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTR_LABEL:
				labelMessageKey = formAttribute.getStringValue();
				break;
			case ATTR_CLASS:
				objectClassName = formAttribute.getStringValue();
				break;
			case ATTR_CASESENSITIVE:
				isCaseSensitive = formAttribute.getBooleanValue();
				break;
			case ATTR_QUERY_PROPERTY:
				queryProperty = formAttribute.getStringValue();
				break;
			case ATTR_SEARCHOBJECT:
				isSearchObject = formAttribute.getBooleanValue();
				break;
			case ATTR_IMAGE:
				imageKey = formAttribute.getStringValue();
				break;
			case ATTR_ISCLEARTEXT:
				isClearText = formAttribute.getBooleanValue();
				break;
			}
		}
	}

}
