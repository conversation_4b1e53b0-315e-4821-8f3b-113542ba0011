package com.glory.mes.base.custom;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.RCPUtil;
import com.glory.framework.core.util.DBUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class CombineExpressionCustomComposite extends CustomCompsite {
	
	public static final String ATTRIBUTE_VAIRABLE_REFTABLENAME = "MergeRuleRefTableName";
	public static final String ATTRIBUTE_VAIRABLE_FORMULAHEIGHT = "FormulaHeight";
	public static final String ATTRIBUTE_VAIRABLE_FORMULAWIDTH = "FormulaWidth";
	
	protected RefTableField ruleLineField;
	protected Text formulaField;
	
	private Button btnLeftBracket;
	private Button btnRightBracket;
	private Button btnAnd;
	private Button btnOr;
	private Button btnClear;
	
	protected String mergeRuleRefTableName = "BASMergeRuleLine";
	protected Integer formulaHeight = 125;
	protected Integer formulaWidth = 20;

	public CombineExpressionCustomComposite() {
		super();
	}
	
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try {
			configureBody(parent);
			parent.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
			Composite formFormla = toolkit.createComposite(parent);
			configureBody(formFormla);
			formFormla.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
			
			Composite formulaTextBody = toolkit.createComposite(formFormla);
			formulaTextBody.setLayout(new GridLayout(9, false));
			GridData gdFormulaText = new GridData(GridData.FILL_HORIZONTAL);
			gdFormulaText.heightHint = formulaHeight;
			formulaTextBody.setLayoutData(gdFormulaText);
			createFormulaText(toolkit, formulaTextBody);
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}
	
	public void createTextArea(FormToolkit toolkit, Composite formFormla){
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.horizontalSpan = formulaWidth;
		formulaField = toolkit.createText(formFormla, "", SWT.WRAP | SWT.MULTI | SWT.V_SCROLL);
		formulaField.setLayoutData(gd);
		formulaField.setText("");
	}
	
	public void createFormulaText(FormToolkit toolkit, Composite parent) {
		// ---------------------- ��ʽ
		Composite formFormla = toolkit.createComposite(parent);
		GridLayout flayout2F = new GridLayout(20, false);
		formFormla.setLayout(flayout2F);
		GridData gdForm2F = new GridData(GridData.FILL_BOTH);
		gdForm2F.heightHint = 100;
		formFormla.setLayoutData(gdForm2F);
		//${A1} and ${A2}
		ruleLineField = RCPUtil.createRefTableField(formFormla, "RuleLine", "BASMergeRuleLine", "RuleLine", true);
		ruleLineField.setADManager(getADManger());
		ruleLineField.redraw(false);
		ruleLineField.addValueChangeListener(new IValueChangeListener() {
			
			@Override
			public void valueChanged(Object sender, Object newValue) {
				if (newValue != null) {
					formulaField.setText(formulaField.getText() + "${" + newValue + "}");
				}
				
				ruleLineField.setValue(null);
			}
		});

		btnLeftBracket = toolkit.createButton(formFormla, "(", SWT.NONE);
		btnRightBracket = toolkit.createButton(formFormla, ")", SWT.NONE);

		toolkit.createLabel(formFormla, "  ");
		btnAnd = toolkit.createButton(formFormla, "&&&&", SWT.NONE);
		btnOr = toolkit.createButton(formFormla, "||", SWT.NONE);

		toolkit.createLabel(formFormla, "  ");
		btnClear = toolkit.createButton(formFormla, "CLEAR", SWT.NONE);

		createTextArea(toolkit, formFormla);
		
		btnClear.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				formulaField.setText("");
			}
		});

		btnLeftBracket.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				formulaField.setText(formulaField.getText() + "(");
			}
		});

		btnRightBracket.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				formulaField.setText(formulaField.getText() + ")");
			}
		});

		btnAnd.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				formulaField.setText(formulaField.getText() + " && ");
			}
		});

		btnOr.addSelectionListener(new SelectionListener() {
			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				widgetSelected(e);
			}

			@Override
			public void widgetSelected(SelectionEvent e) {
				formulaField.setText(formulaField.getText() + " || ");
			}
		});
	}
	
	@Override
	public void setAttributes(List<ADFormAttribute> formAttributes) {
		for (ADFormAttribute formAttribute : formAttributes) {
			switch (formAttribute.getAttributeName()) {
			case ATTRIBUTE_VAIRABLE_REFTABLENAME:
				mergeRuleRefTableName = formAttribute.getStringValue() != null ? formAttribute.getStringValue() : mergeRuleRefTableName;
				break;
			case ATTRIBUTE_VAIRABLE_FORMULAHEIGHT:
				formulaHeight = formAttribute.getIntValue() != null ? formAttribute.getIntValue() : formulaHeight;
				break;
			case ATTRIBUTE_VAIRABLE_FORMULAWIDTH:
				formulaWidth = formAttribute.getIntValue() != null ? formAttribute.getIntValue() : formulaWidth;
				break;
			}
		}
	}
	
	@Override
	public void refresh() {
		formulaField.setText("");
		ruleLineField.setValue(null);
	}

	@Override
	public void setValue(Object var1) {
		formulaField.setText(DBUtil.toString(var1));
	}

	@Override
	public Object getValue() {
		return formulaField.getText();
	}

	public RefTableField getRuleLineField() {
		return ruleLineField;
	}
	
	public Text getFormulaField() {
		return formulaField;
	}

	public void setFormulaField(Text formulaField) {
		this.formulaField = formulaField;
	}
	
	public ADManager getADManger() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			return adManager;
		} catch (Exception e) {
		}
		return null;
	}
	
}
