package com.glory.mes.base.util;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;

public class WipUtil {
	  
	private static final Logger logger = Logger.getLogger(WipUtil.class);

	public static XCombo getWipCodeCombo(Composite parent, String referenceName, long orgRrn) {
		ADTable adTable;
		FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
		try {
			ADManager adManager = Framework.getService(ADManager.class);
    		adTable  = adManager.getADTable(Env.getOrgRrn(), "WIPCodeListTextAndDes");  
    		ListTableManager tableManager = new ListTableManager(adTable);

//    		ListTableManager tableManager = new ListTableManager(adTable);
//			TableViewer viewer = (TableViewer)tableManager.createViewer(Display.getCurrent().getActiveShell(), toolkit);
//
			if(referenceName != null && !"".equals(referenceName.trim())) {
				String whereClause = " referenceName = '" + referenceName + "'";
				List<ADBase> list = adManager.getEntityList(orgRrn, adTable.getObjectRrn(),
						Env.getMaxResult(), whereClause, null);
				tableManager.setInput(list);
			}
			
			 XCombo combo = new XCombo(parent, tableManager, "key", "text", SWT.BORDER, true);        

	        toolkit.adapt(combo);
	        toolkit.paintBordersFor(combo);
	        return combo;
		} catch(Exception e) {
			logger.error("Error at WipUtil : getWipCodeCombo() : " + e);
		}
		return null;
	}
	
	public static void refreshWipCodeCombo(XCombo combo, String referenceName) {
		if(referenceName != null && !"".equals(referenceName.trim())) {
			ADTable adTable;
			try {
				ADManager adManager = Framework.getService(ADManager.class);
	    		adTable  = adManager.getADTable(Env.getOrgRrn(), "WIPCodeListTextAndDes");
	    		
	    		String whereClause = " referenceName = '" + referenceName + "'";
				List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(),
						Env.getMaxResult(), whereClause, null);
				combo.setInput(list);
			} catch(Exception e) {
				logger.error("Error at WipUtil : refreshWipCodeCombo() : " + e);
			}
		}
	}
	
    public static RefTableField createWipCodeFieldCombo(String id, String label, String referenceName, boolean autoUpper, int limit){	
    	try{
    		ADManager adManager = (ADManager)Framework.getService(ADManager.class);
            ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADURefList");
            ADRefTable refTable = new ADRefTable();
            refTable.setTableRrn(adTable.getObjectRrn());
            refTable.setKeyField("key");
            refTable.setTextField("text");
//    		FormToolkit toolkit = new FormToolkit(Display.getCurrent().getActiveShell().getDisplay());
    		ListTableManager tableManager = new ListTableManager(adTable);
//    		TableViewer viewer = (TableViewer)tableManager.createViewer(Display.getCurrent().getActiveShell(),toolkit);	
			if(referenceName != null && !"".equals(referenceName.trim())) {
				String whereClause = " referenceName = '" + referenceName + "'";
				List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(),
						Env.getMaxResult(), whereClause, null);
				tableManager.setInput(list);
			}	
			int mStyle = SWT.BORDER | SWT.READ_ONLY;
            RefTableField fe = new RefTableField(id, tableManager, refTable, mStyle, autoUpper);
            fe.setLabel(label);
            fe.setWidth(limit);
            return fe;
    	}catch(Exception e) {
    		logger.error("Error at WipUtil : createRefTableFieldCombo() : " + e);
    	} 
    	return null;
    }
}
