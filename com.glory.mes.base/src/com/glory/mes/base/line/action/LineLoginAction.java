package com.glory.mes.base.line.action;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.base.security.extensionpoints.ILoginAction;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.base.model.LineUser;

public class LineLoginAction implements ILoginAction {

	private static final Logger logger = Logger.getLogger(LineLoginAction.class);

	@Override
	public void doAction(long orgRrn, long userRrn) {
		try {
			SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
	        if (MesCfMod.isUseLine(orgRrn, sysParamManager)) {
	        	ADManager manager = Framework.getService(ADManager.class);
				String whereClause = "userRrn = " + userRrn;
				List<LineUser> lineUsers = manager.getEntityList(orgRrn, LineUser.class, Integer.MAX_VALUE, whereClause, "");
				
				if (lineUsers.size() > 0) {
					//ֻ�е��û��������߱��ʱ��,�ż���߱�
					//����û�û�������߱�,��Ĭ��Ϊ�û�����Ҫ����߱�
					Env.setUseLine(true);
					List<String> lines = new ArrayList<String>();
					String lineClause = null;
					for (LineUser lineUser : lineUsers) {
						lines.add(lineUser.getLineId());
						if (lineClause == null) {
							lineClause = "'" + lineUser.getLineId() + "'";
						} else {
							lineClause += ", " + "'" + lineUser.getLineId() + "'";
						}
					}
					lineClause = " IN (" + lineClause + ")";
					Env.setLines(lines);
					Env.setLineClause(lineClause);
					
					logger.info("Use line, line is [" + lineClause + "]");
				} else {
					Env.setLineClause("");
					Env.setLines(new ArrayList<String>());
					Env.setUseLine(false);
				}
	        } else {
				Env.setLineClause("");
				Env.setLines(new ArrayList<String>());
				Env.setUseLine(false);
			}
		} catch (Exception e) {
			logger.error(e);
		}
	}

}
