package com.glory.mes.base.shifttime;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.entitymanager.forms.ExtendTableForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.CalendarDay;
import com.glory.mes.base.model.CalendarSetup;
import com.glory.mes.base.model.Shift;
import com.glory.mes.base.model.ShiftTime;
import com.glory.framework.core.exception.ExceptionBundle;

public class ShiftTimeProperties extends EntityProperties {

	protected ToolItem itemSetUp;

	protected ExtendTableForm extendTableForm;

	private static final String TABLE_NAME = "BASShiftTime";

	// extendTableForm����λ��ʼ����ֹλ��
	public static final int SHIFT_TIME_START = 0;
	public static final int SHIFT_TIME_END = 6;

	public ShiftTimeProperties() {
		super();
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	@Override
	protected void saveAdapter() {
		try {
			Shift shift = (Shift) this.masterParent.getTableManager().getSelectedObject();
			if (shift == null) {
				UI.showWarning(Message.getString("bas.please_select_shift"));
			} else {
				String shiftId = shift.getShiftId();
				ADManager adManager = Framework.getService(ADManager.class);
				List<ShiftTime> shiftTimeList = adManager.getEntityList(Env.getOrgRrn(), ShiftTime.class,
						Env.getMaxResult(), "shiftId = '" + shiftId + "'", "weekDay ASC");
				if (shiftTimeList.size() == 0) {
					shiftTimeList = new ArrayList<ShiftTime>();
					int j = 1;
					for (int i = SHIFT_TIME_START; i <= SHIFT_TIME_END; i++) {
						ShiftTime shiftTime = new ShiftTime();
						shiftTime.setPart1(this.extendTableForm.getFields().get("part1" + i).getValue().toString());
						shiftTime.setShiftId(shiftId);
						shiftTime.setOrgRrn(Env.getOrgRrn());
						shiftTime.setWeekDay(j + "");
						shiftTimeList.add(shiftTime);
						j++;
					}

					adManager.saveEntityList(shiftTimeList, Env.getSessionContext());
				} else {
					int i = SHIFT_TIME_START;
					int j = 1;
					for (ShiftTime shiftTime : shiftTimeList) {
						shiftTime.setPart1(this.extendTableForm.getFields().get("part1" + i).getValue().toString());
						shiftTime.setShiftId(shiftId);
						shiftTime.setOrgRrn(Env.getOrgRrn());
						shiftTime.setWeekDay(j + "");
						i++;
						j++;
					}
					adManager.saveEntityList(shiftTimeList, Env.getSessionContext());
				}

				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	@Override
	protected void createBasicSection(Composite client) {
		try {
//			final FormToolkit toolkit = form.getToolkit();
//			mmng = form.getMessageManager();
//			Section section = toolkit.createSection(client, Section.TITLE_BAR | Section.EXPANDED);
//			section.setText(Message.getString("bas.teamtime_editortitle_detailed"));
//			GridData gd = new GridData(GridData.FILL_BOTH);
//			section.setLayoutData(gd);
//			section.setLayout(new GridLayout(1, true));
//
//			Composite sectionClient = toolkit.createComposite(client, SWT.BORDER);
//			sectionClient.setLayout(new FillLayout());
//			gd = new GridData(GridData.FILL_BOTH);
//			gd.grabExcessHorizontalSpace = false;
//			sectionClient.setLayoutData(gd);
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable shiftTimeAdtable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);

			List<ShiftTime> shiftTimes = null;
			shiftTimes = new ArrayList<ShiftTime>();

			for (int i = 0; i < 7; i++) {
				ShiftTime shiftTime = new ShiftTime();
				shiftTimes.add(shiftTime);
			}

			this.extendTableForm = new ExtendTableForm(client, SWT.None, false, shiftTimeAdtable, shiftTimes, 1, null);
			TextField field0 = (TextField) extendTableForm.getFields().get("part10");
			((Label) (field0.getControls()[0])).setText(Message.getString("common.monday"));
			TextField field1 = (TextField) extendTableForm.getFields().get("part11");
			((Label) (field1.getControls()[0])).setText(Message.getString("common.tuesday"));
			TextField field2 = (TextField) extendTableForm.getFields().get("part12");
			((Label) (field2.getControls()[0])).setText(Message.getString("common.wednesday"));
			TextField field3 = (TextField) extendTableForm.getFields().get("part13");
			((Label) (field3.getControls()[0])).setText(Message.getString("common.thursday"));
			TextField field4 = (TextField) extendTableForm.getFields().get("part14");
			((Label) (field4.getControls()[0])).setText(Message.getString("common.friday"));
			TextField field5 = (TextField) extendTableForm.getFields().get("part15");
			((Label) (field5.getControls()[0])).setText(Message.getString("common.saturday"));
			TextField field6 = (TextField) extendTableForm.getFields().get("part16");
			((Label) (field6.getControls()[0])).setText(Message.getString("common.sunday"));
			getDetailForms().add(extendTableForm);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void refresh() {
		try {
			Shift shift = (Shift) getAdObject();
			for (IForm detailForm : getDetailForms()) {
				if (getAdObject() != null) {
					if (detailForm instanceof ExtendTableForm) {
						int size = ((ExtendTableForm) detailForm).getFields().size();

						ADManager adManager = Framework.getService(ADManager.class);
						List<ShiftTime> shiftTimes = (List<ShiftTime>) adManager.getEntityList(Env.getOrgRrn(),
								ShiftTime.class, Integer.MAX_VALUE, "shiftId = '" + shift.getShiftId() + "'", "");
						int shiftTimesSize = 0;
						if (shiftTimes != null && shiftTimes.size() > 0) {
							shiftTimesSize = shiftTimes.size();
						}
						while (shiftTimesSize < size) {
							shiftTimes.add(new ShiftTime());
							shiftTimesSize++;
						}
						detailForm.setObject(shiftTimes);
						detailForm.loadFromObject();
					}
				}

			}
			form.getMessageManager().removeAllMessages();
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}

	}

	@Override
	protected void deleteAdapter() {
		try {
			if (this.getAdObject() == null) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
				return;
			}
			CalendarSetup calendarSetup = (CalendarSetup) this.getAdObject();
			if (calendarSetup != null) {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()))) {
					ADManager adManager = Framework.getService(ADManager.class);
					List<CalendarDay> calendarDays = adManager.getEntityList(Env.getOrgRrn(), CalendarDay.class,
							Env.getMaxResult(), " calendarId = '" + calendarSetup.getCalendarId() + "'", "");
					if (calendarDays.size() > 0) {
						adManager.deleteEntityList(calendarDays, Env.getSessionContext());
					}
					adManager.deleteEntity(calendarSetup, Env.getSessionContext());

					refresh();
					this.masterParent.refresh();
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	
}
