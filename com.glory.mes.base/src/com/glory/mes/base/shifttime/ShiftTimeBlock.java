package com.glory.mes.base.shifttime;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class ShiftTimeBlock extends EntityBlock{
	
private static final Logger logger = Logger.getLogger(ShiftTimeBlock.class);
	
	ShiftTimeProperties properties;

	public ShiftTimeBlock(ListTableManager tableManager) {
		super(tableManager);
	}
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class<?> klass = Class.forName(table.getModelClass());
			properties = new ShiftTimeProperties();
			properties.setTable(table);
			properties.setMasterParent(this);
			detailsPart.registerPage(klass, properties);
			refresh();
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((ShiftTimeProperties)properties).setFocus();
	}
}
