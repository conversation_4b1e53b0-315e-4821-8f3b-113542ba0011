package com.glory.mes.base.shift;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class Shift<PERSON>lock extends EntityBlock {
	
	private static final Logger logger = Logger.getLogger(ShiftBlock.class);
	ShiftProperties page;

	public ShiftBlock(ListTableManager tableManager) {
		super(tableManager);
	}

	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class<?> klass = Class.forName(table.getModelClass());
			page = new ShiftProperties();
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("EntityBlock : registerPages ", e);
		}
	}
	

}
