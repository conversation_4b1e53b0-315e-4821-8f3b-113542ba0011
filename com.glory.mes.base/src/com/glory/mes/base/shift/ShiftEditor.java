package com.glory.mes.base.shift;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class ShiftEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.base/com.glory.mes.base.shift.ShiftEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new ShiftBlock(new ListTableManager(adTable));
	}
	
}
