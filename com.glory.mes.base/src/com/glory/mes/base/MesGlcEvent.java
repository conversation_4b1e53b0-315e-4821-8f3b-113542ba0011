package com.glory.mes.base;

import com.glory.framework.base.entitymanager.glc.GlcEvent;

public class MesGlcEvent extends GlcEvent {

	public static final String PROPERTY_LOT_ID = "lotId";
	public static final String PROPERTY_LOT_RRN = "lotRrn";
	
	public static final String PROPERTY_EQUIPMENT_ID = "equipmentId";
	public static final String PROPERTY_EQUIPMENT_RRN = "equipmentRrn";

	public static final String PROPERTY_CARRIER_ID = "carrierId";
    public static final String PROPERTY_TARGET_CARRIER_ID = "targetCarrierId";
	public static final String PROPERTY_CARRIER_RRN = "carrierRrn";

	public static final String PROPERTY_USER_ID = "userId";
	public static final String PROPERTY_USER_RRN = "userRrn";
	
	public static final String PROPERTY_COMPONENT_ID = "componentId";
	public static final String PROPERTY_COMPONENT_RRN = "componentRrn";
	
	public static final String PROPERTY_MLOT_ID = "mLotId";
	public static final String PROPERTY_MLOT_RRN = "mLotRrn";

	public static final String PROPERTY_TOOL_ID = "toolId";
	public static final String PROPERTY_TOOL_RRN = "toolRrn";
	
	public static final String PROPERTY_PROCEDURE_NAME = "procedureName";
	
	public static final String PROPERTY_STEP_NAME = "stepName";

}
