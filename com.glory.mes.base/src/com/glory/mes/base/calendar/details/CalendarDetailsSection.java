package com.glory.mes.base.calendar.details;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IFormPart;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.SectionPart;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.FFormSection;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.editor.FixEditorTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.CalendarDay;
import com.glory.framework.core.exception.ExceptionBundle;

public class CalendarDetailsSection {
	private static final Logger logger = Logger.getLogger(CalendarDetailsSection.class);
	
	protected ToolItem itemSave;
	protected FixEditorTableManager calendarDetailsListManager;
	protected String whereClause;
	protected Section section;
	protected IFormPart spart;
	protected ToolItem queryItem;
	protected ToolItem refreshItem;
	protected ADTable adTable;
	protected String TABLE_NAME = "BASCalendarDaySearch";
	protected ToolItem itemNextMonth,itemPrevMonth;
	public String calendarDayId; 
	public String calendarDayType;
	
	public CalendarDetailsSection(FixEditorTableManager calendarDetailsListManager, String calendarDayType) {
		this.setCalendarDetailsListManager(calendarDetailsListManager);
		this.setCalendarDayType(calendarDayType);
	}

	public void createContents(IManagedForm form, Composite parent){
		createContents(form, parent, Section.NO_TITLE | FFormSection.FFORM);
		createSectionDesc(section);
	}

	public void createContents(final IManagedForm form, Composite parent, int sectionStyle) {
		final FormToolkit toolkit = form.getToolkit();
		final ADTable table = getCalendarDetailsListManager().getADTable();
		
		section = toolkit.createSection(parent, sectionStyle);
		section.setText(I18nUtil.getI18nMessage(table, "label"));
		section.marginWidth = 0;
	    section.marginHeight = 0;

		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		section.setLayout(layout);
		section.setLayoutData(new GridData(GridData.FILL_BOTH));
	    
	    Composite client = toolkit.createComposite(section);    
	    GridLayout gridLayout = new GridLayout();    
	    layout.numColumns = 1;    
	    client.setLayout(gridLayout);
	    
	    createToolBar(section);
	    
	    spart = new SectionPart(section);    
	    form.addPart(spart);
	    section.setText(String.format(Message.getString(ExceptionBundle.bundle.CommonList()), I18nUtil.getI18nMessage(table, "label")));  
		
	    initCalendarDayType();
		try {
			MBASManager mBasManager = Framework.getService(MBASManager.class);
			Date now=new Date();
			java.sql.Timestamp nowDate = new java.sql.Timestamp(now.getTime());
			List<CalendarDay> input = mBasManager.selectCalendarDay(nowDate.getYear() + 1900, nowDate.getMonth() + 1, getCalendarDayType(), getCalendarDayId(), Env.getSessionContext());		   
			calendarDetailsListManager.setInput(input);
			calendarDetailsListManager.setIndexFlag(true);
			calendarDetailsListManager.newViewer(client);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
		}
		section.setClient(client);
	}

	public void initCalendarDayType() {
		
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemPreMonth(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemNextMonth(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemSearch(ToolBar tBar) {
		queryItem = new ToolItem(tBar, SWT.PUSH);
		queryItem.setText(Message.getString("common.search_Title"));
		queryItem.setImage(SWTResourceCache.getImage("search"));
		queryItem.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				queryAdapter();
			}
		});
	}
	
	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new AuthorityToolItem(tBar, SWT.PUSH, null);
		itemSave.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}
	
	protected void createToolItemRefresh(ToolBar tBar) {
		refreshItem = new ToolItem(tBar, SWT.PUSH);
		refreshItem.setText(Message.getString(ExceptionBundle.bundle.CommonRefresh()));
		refreshItem.setImage(SWTResourceCache.getImage("refresh"));
		refreshItem.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				refreshAdapter();
			}
		});
	}
	
	protected void createToolItemNextMonth(ToolBar tBar) {
		itemNextMonth = new AuthorityToolItem(tBar, SWT.PUSH, null);
		itemNextMonth.setText(Message.getString("bas.calendar_next_month"));
		itemNextMonth.setImage(SWTResourceCache.getImage("next"));
		itemNextMonth.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				boolean flag = UI.showConfirm(Message.getString("bas.calendar_save_prompt"));
				if(flag == true){
					saveAdapter();
				}
				nextMonthAdapter();
			}
		});
	}
	
	protected void createToolItemPreMonth(ToolBar tBar) {
		itemPrevMonth = new AuthorityToolItem(tBar, SWT.PUSH, null);
		itemPrevMonth.setText(Message.getString("bas.calendar_per_month"));
		itemPrevMonth.setImage(SWTResourceCache.getImage("before"));
		itemPrevMonth.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				boolean flag = UI.showConfirm(Message.getString("bas.calendar_save_prompt"));
				if(flag == true){
					saveAdapter();
				}
				preMonthAdapter();
			}
		});
	}
	
	protected void nextMonthAdapter() {
		try {
			MBASManager mBasManager = Framework.getService(MBASManager.class);
			List<CalendarDay> getInput = (List<CalendarDay>) calendarDetailsListManager.getInput();
			if (getInput.size() == 0) {
				return;
			}
			CalendarDay calendarDay = getInput.get(0);
			Date date = calendarDay.getSysDate();
			int year = date.getYear() + 1900;
			int month = date.getMonth() + 2;			
			List<CalendarDay> input = mBasManager.selectCalendarDay(year,month, getCalendarDayType(), getCalendarDayId(), Env.getSessionContext());
			if (input.size() != 0) {
				refresh(input);
			} else {
				UI.showInfo(Message.getString("bas.calendar_next_prompt"));
			}
		} catch (Exception e) {
				e.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e);
				return;
		}
	}
	
	protected void preMonthAdapter() {
		try {
			MBASManager mBasManager = Framework.getService(MBASManager.class);
			List<CalendarDay> getInput = (List<CalendarDay>) calendarDetailsListManager.getInput();
			if (getInput.size() == 0) {
				return;
			}
			CalendarDay calendarDay = getInput.get(0);
			Date date = calendarDay.getSysDate();
			int year = date.getYear() + 1900;
			int month = date.getMonth();
			if(month == 0){
				year = year - 1;
				month = 12;
			}
			List<CalendarDay> input = mBasManager.selectCalendarDay(year, month, getCalendarDayType(), getCalendarDayId(), Env.getSessionContext());			
			if (input.size() != 0) {
				refresh(input);
			} else {
				UI.showInfo(Message.getString("bas.calendar_pre_prompt"));
			}
		} catch (Exception e) {
				e.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e);
				return;
		}
	}

	protected void createSectionDesc(Section section){
	}
	
	public void refresh(List<CalendarDay> input){
		calendarDetailsListManager.setInput(input);
		calendarDetailsListManager.refresh();
		createSectionDesc(section);
	}
	
	protected void queryAdapter() {
		adTable = getADTableOfCalendarDay();
		SearchCalendarDayDialog searchCalendarDayDialog = new SearchCalendarDayDialog(UI.getActiveShell(), adTable, new CalendarDay());
		if (searchCalendarDayDialog.open() == IDialogConstants.OK_ID) {
			CalendarDay calendarDay = searchCalendarDayDialog.getCalendarDay();
			Date d = new Date();
			int year = calendarDay.getYear() == null ? d.getYear() + 1900 : Integer.parseInt(calendarDay.getYear().toString());
			int month = calendarDay.getMonth() == null ? d.getMonth() + 1 : Integer.parseInt(calendarDay.getMonth().toString());
			this.setCalendarDayType(calendarDay.getCalendarType());
			this.setCalendarDayId(calendarDay.getCalendarId());
			try {
				MBASManager mBasManager = Framework.getService(MBASManager.class);
				List<CalendarDay> input = mBasManager.selectCalendarDay(year, month, getCalendarDayType(), getCalendarDayId(), Env.getSessionContext());
				if (input.size() > 0) {
					refresh(input);
				} else {
					UI.showInfo(Message.getString("bas.calendar_query_prompt"));
				}
			} catch (Exception e) {
				e.printStackTrace();
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	}
	
	public void saveAdapter() {
		try {
			List<CalendarDay> getInput = new ArrayList<CalendarDay>(); 
			List<? extends Object> objects = calendarDetailsListManager.getInput();
			if (objects == null || objects.isEmpty()) {
				return;
			}
			
			for (Object object : objects) {
				getInput.add((CalendarDay) object);
			}
			
			MBASManager mBasManager = Framework.getService(MBASManager.class);
			mBasManager.saveCalendarDay(Env.getOrgRrn(), getInput, Env.getSessionContext());
			if (getInput.size() == 0) {
				return;
			}
			CalendarDay calendarDay = getInput.get(0);
			Date date = calendarDay.getSysDate();
			int year = date.getYear() + 1900;
			int month = date.getMonth() + 1;
			List<CalendarDay> input = mBasManager.selectCalendarDay(year, month, getCalendarDayType(), getCalendarDayId(), Env.getSessionContext());
			refresh(input);			
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void refreshAdapter() {
		try {
			MBASManager mBasManager = Framework.getService(MBASManager.class);
			List<CalendarDay> getInput = (List<CalendarDay>)calendarDetailsListManager.getInput();
			if (getInput.size() == 0) {
				return;
			}
			CalendarDay calendarDay = getInput.get(0);
			Date date = calendarDay.getSysDate();
			int year = date.getYear()+1900;
			int month = date.getMonth()+1;
			List<CalendarDay> input;
			input = mBasManager.selectCalendarDay(year, month, getCalendarDayType(), getCalendarDayId(),Env.getSessionContext());
			refresh(input);
		} catch (Exception e) {
			e.printStackTrace();
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		
	}

	public void setWhereClause(String whereClause) {
		this.whereClause = whereClause;
	}

	public String getWhereClause() {
		return whereClause;
	}

	public FixEditorTableManager getCalendarDetailsListManager() {
		return calendarDetailsListManager;
	}

	public void setCalendarDetailsListManager(FixEditorTableManager calendarDetailsListManager) {
		this.calendarDetailsListManager = calendarDetailsListManager;
	}
	
	protected ADTable getADTableOfCalendarDay() {
		try {
			if (adTable == null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				adTable = entityManager.getADTable(0L, TABLE_NAME);
//				adTable = entityManager.getADTableDeep(adTable.getObjectRrn());
			}
			return adTable;
		} catch (Exception e) {
			logger.error("NewAlarmDialog : getADTableOfAlarmType()", e);
		}
		return null;
	}

	public String getCalendarDayId() {
		return calendarDayId;
	}

	public void setCalendarDayId(String calendarDayId) {
		this.calendarDayId = calendarDayId;
	}

	public String getCalendarDayType() {
		return calendarDayType;
	}

	public void setCalendarDayType(String calendarDayType) {
		this.calendarDayType = calendarDayType;
	}
	
	
}
