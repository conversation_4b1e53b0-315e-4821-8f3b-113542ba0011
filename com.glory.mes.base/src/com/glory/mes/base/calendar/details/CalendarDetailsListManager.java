package com.glory.mes.base.calendar.details;

import java.util.List;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.CellEditor;
import org.eclipse.jface.viewers.ComboBoxCellEditor;
import org.eclipse.jface.viewers.ICellModifier;
import org.eclipse.jface.viewers.StructuredViewer;
import org.eclipse.jface.viewers.TableViewer;
import org.eclipse.jface.viewers.TextCellEditor;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.TableItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.viewers.TableViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.CalendarDay;

public class CalendarDetailsListManager extends TableViewerManager {
	private static final Logger logger = Logger.getLogger(CalendarDetailsListManager.class);
	private CellEditor[] cellEditor;
	protected TableViewer tableViewer;
	protected String[] str;

	public CalendarDetailsListManager(ADTable adTable) {
		super(adTable);
		super.setStyle(SWT.BORDER | SWT.H_SCROLL | SWT.V_SCROLL | SWT.FULL_SELECTION | SWT.HIDE_SELECTION);
	}

	public CalendarDetailsListManager(ADTable adTable, int style) {
		this(adTable);
		super.addStyle(style);
	}

	@Override
	protected ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		try {
			factory.registerAdapter(Object.class, new CalendarDayItemAdapter<ADBase>());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return factory;
	}

	@Override
	protected StructuredViewer newViewer(Composite parent, FormToolkit toolkit, int h) {
		final TableViewer viewer = (TableViewer) super.newViewer(parent, toolkit, 400);
		setCellEditor(viewer);
		return viewer;
	}

	private void setCellEditor(TableViewer tableViewer) {
		ADManager adManager;
		int j = 0;
		try {
			adManager = Framework.getService(ADManager.class);
			List<ADURefList> adURefList = adManager.getEntityList(Env.getOrgRrn(), ADURefList.class, 0, Env.getMaxResult(), "referenceName = 'ShiftCode'", "");
			str = new String[adURefList.size()+1];
			str[j]="";
			for(ADURefList a : adURefList){	
				j++;
				str[j]=a.getText();				
			}			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		int size = this.getColumns().length;
		cellEditor = new CellEditor[size];
		String[] properties = new String[size];
		for (int i = 0; i < size; i++) {
			String column = (String) tableViewer.getTable().getColumn(i).getData(TableViewerManager.COLUMN_ID);
			if ("isHoliday".equals(column)) {
				cellEditor[i] = new CdayCheckboxCellEditor(tableViewer);
			}else if("year".equals(column)){
				cellEditor[i] = new TextCellEditor(tableViewer.getTable());
			}else if("month".equals(column)){
				cellEditor[i] = new TextCellEditor(tableViewer.getTable());
			}else if("week".equals(column)){
				cellEditor[i] = new TextCellEditor(tableViewer.getTable());
			}else if("quarter".equals(column)){
				cellEditor[i] = new TextCellEditor(tableViewer.getTable());
			}else if("shift1".equals(column)){
				cellEditor[i] = new ComboBoxCellEditor(tableViewer.getTable(), str);
			}else if("shift2".equals(column)){
				cellEditor[i] = new ComboBoxCellEditor(tableViewer.getTable(), str);
			}else if("shift3".equals(column)){
				cellEditor[i] = new ComboBoxCellEditor(tableViewer.getTable(), str);
			}else if("shift4".equals(column)){
				cellEditor[i] = new ComboBoxCellEditor(tableViewer.getTable(), str);
			}else if("shift5".equals(column)){
				cellEditor[i] = new ComboBoxCellEditor(tableViewer.getTable(), str);
			}else {
				cellEditor[i] = null;
			}
			properties[i] = column;
		}
		tableViewer.setColumnProperties(properties);
		tableViewer.setCellEditors(cellEditor);
		CalendarDayCellModifier ccm = new CalendarDayCellModifier(tableViewer);
		tableViewer.setCellModifier(ccm);
	}

	private class CalendarDayCellModifier implements ICellModifier {
		TableViewer tableViewer;
		CalendarDay calendarDay;

		public CalendarDayCellModifier(TableViewer tableViewer) {
			this.tableViewer = tableViewer;
		}

		@Override
		public boolean canModify(Object element, String property) {
			if ("isHoliday".equals(property)||"year".equals(property)||"month".equals(property)||"week".equals(property)||"quarter".equals(property)||"shift1".equals(property)||"shift2".equals(property)||"shift3".equals(property)||"shift4".equals(property)||"shift5".equals(property)) {
				TableItem it = tableViewer.getTable().getSelection()[0];
				it.setImage(1, null);
				return true;
			}
			return false;
		}

		@Override
		public Object getValue(Object element, String property) {
			if (element instanceof CalendarDay) {
				calendarDay = (CalendarDay) element;
				if ("isHoliday".equals(property)) {
					return calendarDay.getIsHoliday();
				}
				if("year".equals(property)){
					return calendarDay.getYear();
				}
				if("month".equals(property)){
					return calendarDay.getMonth();
				}
				if("week".equals(property)){
					return calendarDay.getWeek();
				}
				if("quarter".equals(property)){
					return calendarDay.getQuarter();
				}
				if("shift1".equals(property)){
					for(int i=0;i<str.length;i++){
						if(str[i].equals(calendarDay.getShift1())){
							return i;
						}
					}
				}
				if("shift2".equals(property)){
					for(int i=0;i<str.length;i++){
						if(str[i].equals(calendarDay.getShift2())){
							return i;
						}
					}
				}
				if("shift3".equals(property)){
					for(int i=0;i<str.length;i++){
						if(str[i].equals(calendarDay.getShift3())){
							return i;
						}
					}
				}
				if("shift4".equals(property)){
					for(int i=0;i<str.length;i++){
						if(str[i].equals(calendarDay.getShift4())){
							return i;
						}
					}
				}
				if("shift5".equals(property)){
					for(int i=0;i<str.length;i++){
						if(str[i].equals(calendarDay.getShift5())){
							return i;
						}
					}
				}
			}
			return null;
		}
		@Override
		public void modify(Object element, String property, Object value) {
			try {
				TableItem it = ((TableItem)element);
				//Boolean isHoliday = calendarDay.getIsHoliday();
				String year = calendarDay.getYear();
				String month = calendarDay.getMonth();
				String week = calendarDay.getWeek();
				String quarter = calendarDay.getQuarter();
				CalendarDay calendarDay = (CalendarDay)it.getData();
				if(value != null) {
					if("year".equals(property)){
						year = (String)value;
						calendarDay.setYear(year);
					}
					if("month".equals(property)){
						month = (String)value;
						calendarDay.setMonth(month);
					}
					if("week".equals(property)){
						week = (String)value;
						calendarDay.setWeek(week);
					}
					if("quarter".equals(property)){
						quarter = (String)value;
						calendarDay.setQuarter(quarter);
					}
					if("shift1".equals(property)){	
						calendarDay.setShift1(str[(Integer)value]);				
					}
					if("shift2".equals(property)){
						calendarDay.setShift2(str[(Integer)value]);
					}
					if("shift3".equals(property)){
						calendarDay.setShift3(str[(Integer)value]);
					}
					if("shift4".equals(property)){
						calendarDay.setShift4(str[(Integer)value]);
					}
					if("shift5".equals(property)){
						calendarDay.setShift5(str[(Integer)value]);
					}
				}								
				/*if (Boolean.TRUE.equals(isHoliday)) {
					it.setImage(1, JFaceResources.getImageRegistry().getDescriptor("CHECKED").createImage());
				} else {
					it.setImage(1, JFaceResources.getImageRegistry().getDescriptor("UNCHECKED").createImage());;
				}*/
				tableViewer.refresh();
			} catch(Exception e) {
				logger.error("Error CalendarDayListManager : modify()" + e.getMessage());
			}
		}
	}

}
