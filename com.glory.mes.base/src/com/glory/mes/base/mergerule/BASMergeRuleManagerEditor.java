package com.glory.mes.base.mergerule;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.FMessage.MsgType;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.custom.CombineExpressionCustomComposite;
import com.glory.mes.base.merge.MergeRule;
import com.glory.mes.base.merge.MergeRuleLine;
import com.glory.mes.wip.client.MergeManager;
import com.google.common.base.Objects;
import com.glory.framework.core.exception.ExceptionBundle;

public class BASMergeRuleManagerEditor extends GlcEditor { 
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.base/com.glory.mes.base.mergerule.glc.BASMergeRuleManagerEditor";
	
	private static final String FIELD_EQUALRULE = "equalRule";
	private static final String FIELD_COUNTRULE = "countRule";
	private static final String FIELD_COMBINERULE = "combineRule";
	private static final String FIELD_CDIRULE = "cdiRule";
	private static final String FIELD_EQUALRULETABLE = "equalRuleTable";
	private static final String FIELD_EQUALRULEINFO = "equalRuleInfo";
	private static final String FIELD_COUNTRULETABLE = "countRuleTable";
	private static final String FIELD_COUNTRULEINFO = "countRuleInfo";
	private static final String FIELD_COMBINERULETABLE = "combineRuleTable";
	private static final String FIELD_COMBINERULEINFO = "combineRuleInfo";
	private static final String FIELD_RULENAME = "ruleName";
	private static final String FIELD_RULEDESC = "ruleDesc";
	private static final String FIELD_EXPRESSIONCOM = "expressionCom";
	private static final String FIELD_OPERATOR = "operator";
	private static final String FIELD_VALUE = "value";
	
	private static final String BUTTON_NEW = "new";
	private static final String BUTTON_SAVE = "save";
	private static final String BUTTON_FROZEN = "frozen";
	private static final String BUTTON_ACTIVE = "active";
	private static final String BUTTON_DELETE = "delete";
	private static final String BUTTON_LOTMOCK = "lotMock";
	private static final String BUTTON_COMPMOCK = "compMock";
	private static final String BUTTON_REFRESH = "refresh";
	private static final String BUTTON_ENTITYREFRESH = "entityRefresh";
	private static final String BUTTON_NEWEQUALLINE = "newEqualLine";
	private static final String BUTTON_SAVEEQUALLINE = "saveEqualLine";
	private static final String BUTTON_DELETEEQUALLINE = "deleteEqualLine";
	private static final String BUTTON_NEWCOUNTLINE = "newCountLine";
	private static final String BUTTON_SAVECOUNTLINE = "saveCountLine";
	private static final String BUTTON_DELETECOUNTLINE = "deleteCountLine";
	private static final String BUTTON_NEWCOMBINELINE = "newCombineLine";
	private static final String BUTTON_SAVECOMBINELINE = "saveCombineLine";
	private static final String BUTTON_DELETECOMBINELINE = "deleteCombineLine";
	
	protected GlcFormField equalRuleField;
	protected GlcFormField countRuleField;
	protected GlcFormField combineRuleField;
	protected ListTableManagerField cdiRuleField;
	protected ListTableManagerField equalRuleTableField;
	protected EntityFormField equalRuleInfoField;
	protected ListTableManagerField countRuleTableField;
	protected EntityFormField countRuleInfoField;
	protected ListTableManagerField combineRuleTableField;
	protected GlcFormField combineRuleInfoField;
	protected TextField ruleNameField;
	protected TextField ruleDescField;
	protected CustomField expressionFieldCom;
	protected CombineExpressionCustomComposite expressionCustomComposite;
	protected RefTableField operatorField;
	protected TextField valueField;
	
	protected ToolItem itemSave;
	protected ToolItem itemFrozen;
	protected ToolItem itemDelete;
	protected ToolItem itemActive;
	protected ToolItem itemLotMock;
	protected ToolItem itemCompMock;
	
	protected SquareButton itemNewEqualLine;
	protected SquareButton itemSaveEqualLine;
	protected SquareButton itemDeleteEqualLine;
	protected SquareButton itemNewCountLine;
	protected SquareButton itemSaveCountLine;
	protected SquareButton itemDeleteCountLine;
	protected SquareButton itemNewCombineLine;
	protected SquareButton itemSaveCombineLine;
	protected SquareButton itemDeleteCombineLine;
	
	private EntityForm entityForm;
	private EntityBlock block;
	private MergeRuleLine combineRule;
	private List<MergeRuleLine> cdiRuleLines;
	
	protected IMessageManager mmng;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		equalRuleField = form.getFieldByControlId(FIELD_EQUALRULE, GlcFormField.class);
		countRuleField = form.getFieldByControlId(FIELD_COUNTRULE, GlcFormField.class);
		combineRuleField = form.getFieldByControlId(FIELD_COMBINERULE, GlcFormField.class);
		cdiRuleField = form.getFieldByControlId(FIELD_CDIRULE, ListTableManagerField.class);
		equalRuleTableField = equalRuleField.getFieldByControlId(FIELD_EQUALRULETABLE, ListTableManagerField.class);
		equalRuleInfoField = equalRuleField.getFieldByControlId(FIELD_EQUALRULEINFO, EntityFormField.class);
		countRuleTableField = countRuleField.getFieldByControlId(FIELD_COUNTRULETABLE, ListTableManagerField.class);
		countRuleInfoField = countRuleField.getFieldByControlId(FIELD_COUNTRULEINFO, EntityFormField.class);
		combineRuleTableField = combineRuleField.getFieldByControlId(FIELD_COMBINERULETABLE, ListTableManagerField.class);
		combineRuleInfoField = combineRuleField.getFieldByControlId(FIELD_COMBINERULEINFO, GlcFormField.class);
		ruleNameField = combineRuleInfoField.getFieldByControlId(FIELD_RULENAME, TextField.class);
		ruleDescField = combineRuleInfoField.getFieldByControlId(FIELD_RULEDESC, TextField.class);
		expressionFieldCom = combineRuleInfoField.getFieldByControlId(FIELD_EXPRESSIONCOM, CustomField.class);
		expressionCustomComposite = (CombineExpressionCustomComposite) expressionFieldCom.getCustomComposite();
		operatorField = equalRuleInfoField.getFieldByControlId(FIELD_OPERATOR, RefTableField.class);
		valueField = equalRuleInfoField.getFieldByControlId(FIELD_VALUE, TextField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::selectionChanged);
		subscribeAndExecute(eventBroker, equalRuleTableField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::equalRuleTableSelectionChanged);
		subscribeAndExecute(eventBroker, countRuleTableField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::countRuleTableSelectionChanged);
		subscribeAndExecute(eventBroker, combineRuleTableField.getFullTopic(GlcEvent.EVENT_SELECTION_CHANGED), this::comBineRuleTableSelectionChanged);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_NEW), this::newAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_SAVE), this::saveAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_FROZEN), this::frozenAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ACTIVE), this::activeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_DELETE), this::deleteAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_LOTMOCK), this::lotMockAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_COMPMOCK), this::compMockAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_ENTITYREFRESH), this::entityRefreshAdapter);
		subscribeAndExecute(eventBroker, equalRuleField.getFullTopic(BUTTON_NEWEQUALLINE), this::newEqualLineAdapter);
		subscribeAndExecute(eventBroker, equalRuleField.getFullTopic(BUTTON_SAVEEQUALLINE), this::saveEqualLineAdapter);
		subscribeAndExecute(eventBroker, equalRuleField.getFullTopic(BUTTON_DELETEEQUALLINE), this::deleteEqualLineAdapter);
		subscribeAndExecute(eventBroker, countRuleField.getFullTopic(BUTTON_NEWCOUNTLINE), this::newCountLineAdapter);
		subscribeAndExecute(eventBroker, countRuleField.getFullTopic(BUTTON_SAVECOUNTLINE), this::saveCountLineAdapter);
		subscribeAndExecute(eventBroker, countRuleField.getFullTopic(BUTTON_DELETECOUNTLINE), this::deleteCountLineAdapter);
		subscribeAndExecute(eventBroker, combineRuleField.getFullTopic(BUTTON_NEWCOMBINELINE), this::newCombineLineAdapter);
		subscribeAndExecute(eventBroker, combineRuleField.getFullTopic(BUTTON_SAVECOMBINELINE), this::saveCombineLineAdapter);
		subscribeAndExecute(eventBroker, combineRuleField.getFullTopic(BUTTON_DELETECOMBINELINE), this::deleteCombineLineAdapter);
		
		init();
		initMergeRuleForm();
		statusChanged(null);
	}
	
	private void init() {
		block = (EntityBlock) form.getBlock();
		entityForm = (EntityForm) form.getSubForms().get(0);
		
		itemSave = (ToolItem) form.getButtonByControl(null, BUTTON_SAVE);
		itemFrozen = (ToolItem) form.getButtonByControl(null, BUTTON_FROZEN);
		itemDelete = (ToolItem) form.getButtonByControl(null, BUTTON_DELETE);
		itemActive = (ToolItem) form.getButtonByControl(null, BUTTON_ACTIVE);
		
		itemLotMock = (ToolItem) form.getButtonByControl(null, BUTTON_LOTMOCK);
		itemCompMock = (ToolItem) form.getButtonByControl(null, BUTTON_COMPMOCK);
		
		itemNewEqualLine = (SquareButton) equalRuleField.getButtonByControl(BUTTON_NEWEQUALLINE);
		itemSaveEqualLine = (SquareButton) equalRuleField.getButtonByControl(BUTTON_SAVEEQUALLINE);
		itemDeleteEqualLine = (SquareButton) equalRuleField.getButtonByControl(BUTTON_DELETEEQUALLINE);
		
		itemNewCountLine = (SquareButton) countRuleField.getButtonByControl(BUTTON_NEWCOUNTLINE);
		itemSaveCountLine = (SquareButton) countRuleField.getButtonByControl(BUTTON_SAVECOUNTLINE);
		itemDeleteCountLine = (SquareButton) countRuleField.getButtonByControl(BUTTON_DELETECOUNTLINE);
		
		itemNewCombineLine = (SquareButton) combineRuleField.getButtonByControl(BUTTON_NEWCOMBINELINE);
		itemSaveCombineLine = (SquareButton) combineRuleField.getButtonByControl(BUTTON_SAVECOMBINELINE);
		itemDeleteCombineLine = (SquareButton) combineRuleField.getButtonByControl(BUTTON_DELETECOMBINELINE);
		
		try {
			MergeManager mergeManager = Framework.getService(MergeManager.class);
			cdiRuleLines = mergeManager.getCdiMergeRuleLine();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		
		operatorField.addValueChangeListener(new IValueChangeListener() {
			
			@Override
			public void valueChanged(Object sender, Object newValue) {
				if (MergeRuleLine.EQUAL_OPERATOR_EQUAL.equals(newValue)) {
					valueField.getLabelControl().setText(valueField.getLabelControl().getText().replace("*", ""));
					valueField.getTextControl().setBackground(valueField.getBackColor());
					((ADField)valueField.getADField()).setIsMandatory(false);
				} else {
					valueField.getLabelControl().setText(valueField.getLabelControl().getText() + "*");
					valueField.getTextControl().setBackground(valueField.getMandatoryColor());
					((ADField)valueField.getADField()).setIsMandatory(true);
				}
			}
		});
	}
	
	
	private void newAdapter(Object object) {
		statusChanged(null);
		initMergeRuleForm();
		entityForm.refresh();
		block.refresh();
		
		if (mmng == null) {
			mmng = (IMessageManager) new FMessageManager();
		}
		mmng.removeAllMessages();
	}
	
	private void saveAdapter(Object object) {
		try {
			entityForm.removeAllMessages();
			if (entityForm.saveToObject()) {
				MergeRule mergeRule = (MergeRule) entityForm.getObject();
				List<MergeRuleLine> equalRuleLines = (List<MergeRuleLine>) equalRuleTableField.getListTableManager().getInput();
				List<MergeRuleLine> countRuleLines = (List<MergeRuleLine>) countRuleTableField.getListTableManager().getInput();
				List<MergeRuleLine> comBineRuleLines = (List<MergeRuleLine>) combineRuleTableField.getListTableManager().getInput();
				List<Object> cdiRuleLines = cdiRuleField.getListTableManager().getCheckedObject();
				List<MergeRuleLine> allRuleLine = Lists.newArrayList();
				allRuleLine.addAll(equalRuleLines);
				allRuleLine.addAll(countRuleLines);
				allRuleLine.addAll(comBineRuleLines);
				for(Object line : cdiRuleLines) {
					allRuleLine.add((MergeRuleLine) line);
				}
				
				List<String> ruleLineName = Lists.newArrayList();
				for (MergeRuleLine ruleLine : allRuleLine) {
					if(ruleLineName.contains(ruleLine.getRuleName())) {
						UI.showWarning(Message.getString(ruleLine.getRuleName() + Message.getString("common.merge_rule_line_repeat")));
						return;
					} else {
						ruleLineName.add(ruleLine.getRuleName());
					}
				}
				
				if (CollectionUtils.isEmpty(allRuleLine)) {
					UI.showError(Message.getString("wip.please_set_detail_merge_rule"));
					return;
				}
				
				mergeRule.setMergeRuleLines(allRuleLine);
				mergeRule.setIsActive(true);
				if (mergeRule.getObjectRrn() == null) {
					mergeRule.setStatus(VersionControl.STATUS_UNFROZNE);
					mergeRule.setVersion(1l);
					ADManager entityManager = Framework.getService(ADManager.class);
					List<MergeRule> ruleList = entityManager.getEntityList(Env.getOrgRrn(), MergeRule.class, Integer.MAX_VALUE, 
							" name = '" + mergeRule.getName() + "' and category = '" + mergeRule.getCategory() + "'", "");
					if (ruleList.size() > 0) {
						boolean isNew = UI.showConfirm(mergeRule.getName() + ":" + Message.getString("prd.version_infor") + ( ruleList.size() + 1));
						if (isNew) {
							mergeRule.setVersion(Long.valueOf(ruleList.size() + 1));
						} else {
							return;
						}
					}
				}
				
				MBASManager mbasManager = Framework.getService(MBASManager.class);
				MergeRule base = mbasManager.saveMergeRule(mergeRule, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
				ADManager entityManager = getADManger();
				ADBase newBase = entityManager.getEntity(base);
				if (mergeRule.getObjectRrn() == null)
					block.refreshAdd(newBase);
				else {
					block.refreshUpdate(newBase);
				}
				entityForm.setObject(newBase);
				entityForm.loadFromObject();
				statusChanged(((MergeRule)newBase));
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void frozenAdapter(Object object) {
		try {
			form.getMessageManager().removeAllMessages();
			MergeRule mergeRule = (MergeRule) entityForm.getObject();
			if (mergeRule != null && mergeRule.getObjectRrn() != null ) {
				ADBase obj;
				if (VersionControl.STATUS_UNFROZNE.equalsIgnoreCase((mergeRule).getStatus())) {
					BASManager basManager = Framework.getService(BASManager.class);
					obj = basManager.frozen(mergeRule, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonFrozenSuccess()));
				} else {
					BASManager basManager = Framework.getService(BASManager.class);
					obj = basManager.unFrozen(mergeRule, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonUnFrozenSuccess()));
				}
				refresh((MergeRule) obj);
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void activeAdapter(Object object) {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			MergeRule mergeRule = (MergeRule) entityForm.getObject();
			MBASManager mbasManager = Framework.getService(MBASManager.class);
			if (mergeRule != null && mergeRule.getObjectRrn() != null ) {
				ADManager adManager = Framework.getService(ADManager.class);
				if (VersionControl.STATUS_FROZNE.equalsIgnoreCase(mergeRule.getStatus()) || VersionControl.STATUS_INACTIVE.equalsIgnoreCase(mergeRule.getStatus())) {
					BASManager basManager = Framework.getService(BASManager.class);
					List<MergeRule> bmrList = adManager.getEntityList(Env.getOrgRrn(), MergeRule.class, Integer.MAX_VALUE,
							" name = '" + mergeRule.getName() + "' and status = '" + VersionControl.STATUS_ACTIVE + "' and category = '" + mergeRule.getCategory() + "'", "");
					if (bmrList.size() > 0) {
						mbasManager.inActiveMergeRule(bmrList.get(0), Env.getSessionContext());
					}
					mergeRule.setActiveTime(new Date());
					mergeRule = (MergeRule) basManager.active(mergeRule, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));
				} else {
					mergeRule = mbasManager.inActiveMergeRule(mergeRule, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
				}
				refresh(mergeRule);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void deleteAdapter(Object object) {
		try {
			if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()))) {
				MergeRule mergeRule = (MergeRule) entityForm.getObject();
				if (mergeRule != null && mergeRule.getObjectRrn() != null ) {
					form.getMessageManager().setAutoUpdate(false);
					form.getMessageManager().removeAllMessages();
					if(!mergeRule.getStatus().equals(VersionControl.STATUS_UNFROZNE)){
						MBASManager mbasManager = Framework.getService(MBASManager.class);
						mergeRule.setStatus(VersionControl.STATUS_DELETE);
						mbasManager.saveMergeRule(mergeRule, Env.getSessionContext());
					} else{
						BASManager basManager = Framework.getService(BASManager.class);
						basManager.delete(mergeRule, Env.getSessionContext());
					}
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
					block.refresh();
					newAdapter(object);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void entityRefreshAdapter(Object object) {
		try {
			MergeRule mergeRule = (MergeRule) entityForm.getObject();
			if (mergeRule.getObjectRrn() != null) {
				ADManager adManager = getADManger();
				mergeRule = (MergeRule) adManager.getEntity(mergeRule);
				
				entityForm.loadFromObject();
				statusChanged(mergeRule);
				
				List<MergeRuleLine> equalLines = mergeRule.getMergeRuleLines().stream()
						.filter(line -> MergeRuleLine.RULE_TYPE_EQUAL.equals(line.getRuleType())).collect(Collectors.toList());
				List<MergeRuleLine> countLines = mergeRule.getMergeRuleLines().stream()
						.filter(line -> MergeRuleLine.RULE_TYPE_COUNT.equals(line.getRuleType())).collect(Collectors.toList());
				List<MergeRuleLine> combineLines = mergeRule.getMergeRuleLines().stream()
						.filter(line -> MergeRuleLine.RULE_TYPE_COMBINE.equals(line.getRuleType())).collect(Collectors.toList());
				equalRuleTableField.getListTableManager().setInput(equalLines);
				countRuleTableField.getListTableManager().setInput(countLines);
				combineRuleTableField.getListTableManager().setInput(combineLines);
				cdiRuleField.getListTableManager().setInput(cdiRuleLines);
				setCombineRefTable();
				setCheckCdiRuleLine(mergeRule, false);
			} else {
				mergeRule = new MergeRule();
				mergeRule.setOrgRrn(Env.getOrgRrn());
				entityForm.setObject(mergeRule);
				entityForm.loadFromObject();
				initMergeRuleForm();
				cdiRuleField.getListTableManager().setInput(cdiRuleLines);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void refreshAdapter(Object object) {
		try {
			newAdapter(object);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void newEqualLineAdapter(Object object) {
		equalRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
		equalRuleInfoField.refresh();
		equalRuleInfoField.getFormControl().removeAllMessages();
	}
	
	private void saveEqualLineAdapter(Object object) {
		try {
			MergeRuleLine mergeRule = (MergeRuleLine) equalRuleInfoField.getValue();
			if (equalRuleInfoField.validate()) {
				mergeRule = createExpression(mergeRule);
				if (mergeRule.getSeqNo() != null) {
					equalRuleTableField.getListTableManager().update(mergeRule);
				} else {
					int seqNo = equalRuleTableField.getListTableManager().getInput().size();
					mergeRule.setSeqNo(Long.valueOf(seqNo));
					List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) equalRuleTableField.getListTableManager().getInput();
					List<MergeRuleLine> allMergeRuleLines = Lists.newArrayList();
					allMergeRuleLines.addAll(mergeRuleLines);
					allMergeRuleLines.add(mergeRule);
					equalRuleTableField.getListTableManager().setInput(allMergeRuleLines);
				}
				equalRuleInfoField.setValue(mergeRule);
				equalRuleInfoField.refresh();
				equalRuleInfoField.getFormControl().removeAllMessages();
				setCombineRefTable();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void deleteEqualLineAdapter(Object object) {
		List<Object> mergeRuleLineList = equalRuleTableField.getListTableManager().getCheckedObject();
		List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) equalRuleTableField.getListTableManager().getInput();
		mergeRuleLines.removeAll(mergeRuleLineList);
		
		List<String> equalNames = mergeRuleLineList.stream().map(obj -> (MergeRuleLine)obj).map(MergeRuleLine :: getRuleName).collect(Collectors.toList());
		List<MergeRuleLine> mergeRuleList = Lists.newArrayList();
		int i = 0;
		for (MergeRuleLine ruleLine : mergeRuleLines) {
			ruleLine.setSeqNo(Long.valueOf(i));
			mergeRuleList.add(ruleLine);
			i++;
		}
		equalRuleTableField.getListTableManager().setInput(mergeRuleList);
		equalRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
		equalRuleInfoField.refresh();
		setCombineRefTable();
	}
	
	private void selectionChanged(Object object) {
		try {
			initMergeRuleForm();
			Event event = (Event) object;
			StructuredSelection selection = (StructuredSelection) event.getProperty(GlcEvent.PROPERTY_DATA);
			MergeRule mergeRule = (MergeRule)selection.getFirstElement();
			if (mergeRule != null) {
				ADManager adManager = getADManger();
				mergeRule = (MergeRule) adManager.getEntity(mergeRule);
				
				statusChanged(mergeRule);
				
				List<MergeRuleLine> equalLines = mergeRule.getMergeRuleLines().stream()
						.filter(line -> MergeRuleLine.RULE_TYPE_EQUAL.equals(line.getRuleType())).collect(Collectors.toList());
				List<MergeRuleLine> countLines = mergeRule.getMergeRuleLines().stream()
						.filter(line -> MergeRuleLine.RULE_TYPE_COUNT.equals(line.getRuleType())).collect(Collectors.toList());
				List<MergeRuleLine> combineLines = mergeRule.getMergeRuleLines().stream()
						.filter(line -> MergeRuleLine.RULE_TYPE_COMBINE.equals(line.getRuleType())).collect(Collectors.toList());
				equalRuleTableField.getListTableManager().setInput(equalLines);
				countRuleTableField.getListTableManager().setInput(countLines);
				combineRuleTableField.getListTableManager().setInput(combineLines);
				setCombineRefTable();
				setCheckCdiRuleLine(mergeRule, false);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void equalRuleTableSelectionChanged(Object object) {
		try {
			MergeRuleLine mergeRuleLine = (MergeRuleLine) equalRuleTableField.getListTableManager().getSelectedObject();
			if (mergeRuleLine != null) {
				equalRuleInfoField.setValue(mergeRuleLine);
				equalRuleInfoField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void newCountLineAdapter(Object object) {
		countRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_COUNT));
		countRuleInfoField.refresh();
		countRuleInfoField.getFormControl().removeAllMessages();
	}
	
	private void saveCountLineAdapter(Object object) {
		try {
			MergeRuleLine mergeRule = (MergeRuleLine) countRuleInfoField.getValue();
			if (countRuleInfoField.validate()) {
				mergeRule = createExpression(mergeRule);
				if (mergeRule.getSeqNo() != null) {
					countRuleTableField.getListTableManager().update(mergeRule);
				} else {
					int seqNo = countRuleTableField.getListTableManager().getInput().size();
					mergeRule.setSeqNo(Long.valueOf(seqNo));
					List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) countRuleTableField.getListTableManager().getInput();
					List<MergeRuleLine> allMergeRuleLines = Lists.newArrayList();
					allMergeRuleLines.addAll(mergeRuleLines);
					allMergeRuleLines.add(mergeRule);
					countRuleTableField.getListTableManager().setInput(allMergeRuleLines);
				}
				countRuleInfoField.setValue(mergeRule);
				countRuleInfoField.refresh();
				setCombineRefTable();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void deleteCountLineAdapter(Object object) {
		List<Object> mergeRuleLineList = countRuleTableField.getListTableManager().getCheckedObject();
		List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) countRuleTableField.getListTableManager().getInput();
		mergeRuleLines.removeAll(mergeRuleLineList);
		
		List<MergeRuleLine> mergeRuleList = Lists.newArrayList();
		int i = 0;
		for(MergeRuleLine ruleLine : mergeRuleLines) {
			ruleLine.setSeqNo(Long.valueOf(i));
			mergeRuleList.add(ruleLine);
			i++;
		}
		countRuleTableField.getListTableManager().setInput(mergeRuleList);
		countRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_COUNT));
		countRuleInfoField.refresh();
		setCombineRefTable();
	}
	
	private void countRuleTableSelectionChanged(Object object) {
		try {
			MergeRuleLine mergeRuleLine = (MergeRuleLine) countRuleTableField.getListTableManager().getSelectedObject();
			if (mergeRuleLine != null) {
				countRuleInfoField.setValue(mergeRuleLine);
				countRuleInfoField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void newCombineLineAdapter(Object object) {
		ruleNameField.setText("");
		ruleDescField.setText("");
		expressionCustomComposite.refresh();
		this.combineRule = null;
		mmng.removeAllMessages();
	}
	
	private void saveCombineLineAdapter(Object object) {
		try {
			if (validateCombine()) {
				MergeRuleLine combineRule;
				if (this.combineRule != null) {
					combineRule = this.combineRule;
				} else {
					combineRule = createMergeRuleLine(MergeRuleLine.RULE_TYPE_COMBINE);
				}
				combineRule.setRuleName(ruleNameField.getText());
				combineRule.setRuleDesc(ruleDescField.getText());
				combineRule.setExpression(expressionCustomComposite.getValue().toString());
				
				if (combineRule.getSeqNo() != null) {
					combineRuleTableField.getListTableManager().update(combineRule);
				} else {
					int seqNo = combineRuleTableField.getListTableManager().getInput().size();
					combineRule.setSeqNo(Long.valueOf(seqNo));
					List<MergeRuleLine> mergeRuleLines = (List<MergeRuleLine>) combineRuleTableField.getListTableManager().getInput();
					List<MergeRuleLine> allMergeRuleLines = Lists.newArrayList();
					allMergeRuleLines.addAll(mergeRuleLines);
					allMergeRuleLines.add(combineRule);
					combineRuleTableField.getListTableManager().setInput(allMergeRuleLines);
					this.combineRule = combineRule;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void deleteCombineLineAdapter(Object object) {
		List<Object> comBineRuleLineList = combineRuleTableField.getListTableManager().getCheckedObject();
		List<MergeRuleLine> comBineRuleLines = (List<MergeRuleLine>) combineRuleTableField.getListTableManager().getInput();
		comBineRuleLines.removeAll(comBineRuleLineList);
		
		List<MergeRuleLine> mergeRuleList = Lists.newArrayList();
		int i = 0;
		for(MergeRuleLine ruleLine : comBineRuleLines) {
			ruleLine.setSeqNo(Long.valueOf(i));
			mergeRuleList.add(ruleLine);
			i++;
		}
		combineRuleTableField.getListTableManager().setInput(mergeRuleList);
		ruleNameField.setText("");
		ruleDescField.setText("");
		expressionCustomComposite.refresh();
		this.combineRule = null;
	}
	
	private void comBineRuleTableSelectionChanged(Object object) {
		try {
			this.combineRule = (MergeRuleLine) combineRuleTableField.getListTableManager().getSelectedObject();
			if(this.combineRule != null) {
				ruleNameField.setText(this.combineRule.getRuleName());
				ruleDescField.setText(this.combineRule.getRuleDesc());
				expressionCustomComposite.setValue(this.combineRule.getExpression());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void statusChanged(MergeRule mergeRule) {
		String newStatus = mergeRule == null? null: mergeRule.getStatus();
		if (VersionControl.STATUS_UNFROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
			itemActive.setEnabled(false);
			itemLotMock.setEnabled(false);
			itemCompMock.setEnabled(false);
			
		} else if (VersionControl.STATUS_FROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
			itemActive.setEnabled(true);
			itemLotMock.setEnabled(false);
			itemCompMock.setEnabled(false);
			
		} else if (VersionControl.STATUS_ACTIVE.equals(newStatus)) {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setImage(SWTResourceCache.getImage("inactive"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonInActive()));
			itemActive.setEnabled(true);
			itemLotMock.setEnabled(true);
			itemCompMock.setEnabled(true);
		}  else if (VersionControl.STATUS_INACTIVE.equals(newStatus)) {
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setImage(SWTResourceCache.getImage("active"));
			itemActive.setText(Message.getString(ExceptionBundle.bundle.CommonActive()));
			itemActive.setEnabled(true);
			itemLotMock.setEnabled(false);
			itemCompMock.setEnabled(false);
		} else {
			itemSave.setEnabled(true);
			itemFrozen.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(false);
			itemLotMock.setEnabled(false);
			itemCompMock.setEnabled(false);
		}
		
		if (VersionControl.STATUS_UNFROZNE.equals(newStatus) || newStatus == null) {
			itemNewEqualLine.setEnabled(true);
			itemSaveEqualLine.setEnabled(true);
			itemDeleteEqualLine.setEnabled(true);
			itemNewCountLine.setEnabled(true);
			itemSaveCountLine.setEnabled(true);
			itemDeleteCountLine.setEnabled(true);
			itemNewCombineLine.setEnabled(true);
			itemSaveCombineLine.setEnabled(true);
			itemDeleteCombineLine.setEnabled(true);
		} else {
			itemNewEqualLine.setEnabled(false);
			itemSaveEqualLine.setEnabled(false);
			itemDeleteEqualLine.setEnabled(false);
			itemNewCountLine.setEnabled(false);
			itemSaveCountLine.setEnabled(false);
			itemDeleteCountLine.setEnabled(false);
			itemNewCombineLine.setEnabled(false);
			itemSaveCombineLine.setEnabled(false);
			itemDeleteCombineLine.setEnabled(false);
		}
		if (mergeRule != null && !MergeRule.CATEGORY_MERGE.equals(mergeRule.getCategory())) {
			itemLotMock.setEnabled(false);
			itemCompMock.setEnabled(false);
		}
	}	
	
	private void lotMockAdapter(Object object) {
		try {
			MergeRule mergeRule = (MergeRule) entityForm.getObject();
			BASMergeRuleLotMockDialog dialog = new BASMergeRuleLotMockDialog("BASMergeRuleLotMockDialog", null, eventBroker, mergeRule);
			if (Dialog.OK == dialog.open()) {
				
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void compMockAdapter(Object object) {
		MergeRule mergeRule = (MergeRule) entityForm.getObject();
		BASMergeRuleCompMockDialog dialog = new BASMergeRuleCompMockDialog("BASMergeRuleCompMockDialog", null, eventBroker, mergeRule);
		if (Dialog.OK == dialog.open()) {
			
		}
	}
	
	/*
	 * ��ȹ���${����} LIKE 'A%'     
	 * ��������${Count} > 1  
	 * ��Ϲ���${A1} and ${A2}
	 */
	private MergeRuleLine createExpression(MergeRuleLine ruleLine) {
		String expression = null;
		if (MergeRuleLine.RULE_TYPE_EQUAL.equals(ruleLine.getRuleType())) {
			expression = "${" + ruleLine.getVariableName() + "} " + ruleLine.getOperator() + " '" + ruleLine.getValue() + "%'";
		} else if (MergeRuleLine.RULE_TYPE_COUNT.equals(ruleLine.getRuleType())) {
			expression = String.format("${%s} %s %s", ruleLine.getCountType(), ruleLine.getOperator(), ruleLine.getValue());
		}
		ruleLine.setExpression(expression);
		return ruleLine;
	}
	
	private void initMergeRuleForm() {
		equalRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_EQUAL));
		equalRuleInfoField.refresh();
		countRuleInfoField.setValue(createMergeRuleLine(MergeRuleLine.RULE_TYPE_COUNT));
		countRuleInfoField.refresh();
		ruleNameField.setText("");
		ruleDescField.setText("");
		expressionCustomComposite.refresh();
		equalRuleTableField.getListTableManager().setInput(Lists.newArrayList());
		countRuleTableField.getListTableManager().setInput(Lists.newArrayList());
		combineRuleTableField.getListTableManager().setInput(Lists.newArrayList());
		cdiRuleField.getListTableManager().setInput(cdiRuleLines);
	}
	
	private MergeRuleLine createMergeRuleLine(String ruleType) {
		MergeRuleLine ruleLine = new MergeRuleLine();
		ruleLine.setOrgRrn(Env.getOrgRrn());
		ruleLine.setRuleType(ruleType);
		return ruleLine;
	}
	
	private void setCombineRefTable() {
		List<MergeRule> equalRules = (List<MergeRule>) equalRuleTableField.getListTableManager().getInput();
		List<MergeRule> countRules = (List<MergeRule>) countRuleTableField.getListTableManager().getInput();
		List<MergeRule> allRules = Lists.newArrayList();
		allRules.addAll(equalRules);
		allRules.addAll(countRules);
		
		expressionCustomComposite.getRuleLineField().setInput(allRules);
	}
	
	private void refresh(MergeRule mergeRule) {
		statusChanged(mergeRule);
		block.refreshUpdate(mergeRule);
		entityForm.setObject(mergeRule);
		entityForm.loadFromObject();
	}
	
	private Boolean validateCombine() {
		if (mmng == null) {
			mmng = (IMessageManager) new FMessageManager();
		}
		Boolean validateCombine = true;
		if (StringUtil.isEmpty(ruleNameField.getText())) {
			mmng.addMessage(Message.getString("wip.futuer_step_count") + "common.ismandatory", 
					String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), Message.getString("wip.futuer_step_count")), null,
					MsgType.MSG_ERROR.getIndex(), ruleNameField.getTextControl());
			validateCombine = false;
		}
		if (StringUtil.isEmpty(expressionCustomComposite.getValue().toString())) {
			mmng.addMessage(Message.getString("wip.futuer_step_count") + "common.ismandatory", 
					String.format(Message.getString(ExceptionBundle.bundle.CommonIsMandatory()), Message.getString("wip.futuer_step_count")), null,
					MsgType.MSG_ERROR.getIndex(), expressionCustomComposite.getFormulaField());
			validateCombine = false;
		}
		if (validateCombine) {
			mmng.removeAllMessages();
		}
		return validateCombine;
	}
	
	private void setCheckCdiRuleLine(MergeRule mergeRule, Boolean isQuery) {
		try {
			if (isQuery) {
				ADManager adManager = Framework.getService(ADManager.class);
				mergeRule = (MergeRule) adManager.getEntity(mergeRule);
			}
			mergeRule.getMergeRuleLines().stream()
				.filter(rule -> MergeRuleLine.RULE_TYPE_CDI.equals(rule.getRuleType()))
				.forEach(cdiRule -> {
					for(MergeRuleLine mergeRuleLine : cdiRuleLines) {
						if(Objects.equal(cdiRule.getRuleName(), mergeRuleLine.getRuleName())) {
							((CheckBoxTableViewerManager)cdiRuleField.getListTableManager().getTableManager()).checkObject(mergeRuleLine);
						}
					}
				});
			cdiRuleField.getListTableManager().refresh();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
}