package com.glory.mes.base.mergerule;


import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ICheckChangedListener;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.variable.client.VariableManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.merge.MergeRule;
import com.glory.mes.base.merge.MergeRuleLine;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.ProcessUnit;
import com.glory.mes.wip.rule.RuleFactory;
import com.glory.mes.wip.rule.merge.LotMergeRuleContext;
import com.google.common.collect.Lists;

public class BASMergeRuleCompMockDialog extends GlcBaseDialog { 
	
	private static int DIALOG_WIDTH = 810;
	private static int DIALOG_HEIGHT = 350;

	private static final String FIELD_LOTCOMPONENT = "lotComponent";
	private static final String FIELD_MOCKLOTRESULT = "mockLotResult";
	private static final String FIELD_LOTQUERY = "lotQuery";
	private static final String FIELD_PARENTCOMPONENTINFO = "parentComponentInfo";
	private static final String FIELD_CHILDCOMPONENTINFO = "childComponentInfo";

	private static final String BUTTON_MOCK = "mock";

	protected GlcFormField lotComponentField;
	protected ListTableManagerField mockLotResultField;
	protected QueryFormField lotQueryField;
	protected ListTableManagerField parentComponentInfoField;
	protected ListTableManagerField childComponentInfoField;
	
	protected MergeRule mergeRule;
	protected CheckBoxTableViewerManager checkBoxTableViewerManager;

	public BASMergeRuleCompMockDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}
	
	public BASMergeRuleCompMockDialog(String adFormName, String authority, IEventBroker eventBroker, MergeRule mergeRule) {
		super(adFormName, authority, eventBroker);
		this.mergeRule = mergeRule;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotComponentField = form.getFieldByControlId(FIELD_LOTCOMPONENT, GlcFormField.class);
		mockLotResultField = form.getFieldByControlId(FIELD_MOCKLOTRESULT, ListTableManagerField.class);
		lotQueryField = lotComponentField.getFieldByControlId(FIELD_LOTQUERY, QueryFormField.class);
		
		parentComponentInfoField = lotComponentField.getFieldByControlId(FIELD_PARENTCOMPONENTINFO, ListTableManagerField.class);
		childComponentInfoField = lotComponentField.getFieldByControlId(FIELD_CHILDCOMPONENTINFO, ListTableManagerField.class);
		
		subscribeAndExecute(eventBroker, lotQueryField.getFullTopic(GlcEvent.EVENT_TABLE_CHECK), this::lotQueryTableCheck);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MOCK), this::mockAdapter);
		
		checkBoxTableViewerManager = (CheckBoxTableViewerManager)lotQueryField.getQueryForm().getTableManager();
		checkBoxTableViewerManager.addICheckChangedListener(checkChangedListener);
		
	}
	
	private void lotQueryTableCheck(Object object) {
		try {
			List<Lot> lots = lotQueryField.getCheckedObjects().stream().map(o -> ((Lot)o)).collect(Collectors.toList());
			if (lots.size() == 1) {
				List<ComponentUnit> componentUnits = getLotComponents(lots.get(0));
				parentComponentInfoField.getListTableManager().setInput(componentUnits);
				childComponentInfoField.getListTableManager().setInput(new ArrayList<Object>());
			} else if (lots.size() == 2) {
				List<ComponentUnit> parentComponentUnits = getLotComponents(lots.get(0));
				parentComponentInfoField.getListTableManager().setInput(parentComponentUnits);
				List<ComponentUnit> childComponentUnits = getLotComponents(lots.get(1));
				childComponentInfoField.getListTableManager().setInput(childComponentUnits);
			} else if (lots.size() == 0) {
				parentComponentInfoField.getListTableManager().setInput(new ArrayList<Object>());
				childComponentInfoField.getListTableManager().setInput(new ArrayList<Object>());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void mockAdapter(Object object) {
		try {
			if (lotQueryField.getCheckedObjects().size() != 2) {
				UI.showInfo(Message.getString("mbas.please_select_two_lot"));
				return;
			}
			if (childComponentInfoField.getListTableManager().getCheckedObject().size() == 0) {
				UI.showInfo(Message.getString("mbas.please_select_merge_component"));
				return;
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot parentLot = lotManager.getLot(((ComponentUnit)parentComponentInfoField.getListTableManager().getInput().get(0)).getParentUnitRrn());
			Lot childLot = lotManager.getLot(((ComponentUnit)childComponentInfoField.getListTableManager().getInput().get(0)).getParentUnitRrn());
			List<ComponentUnit> mergeComponentUnits = childComponentInfoField.getListTableManager().getCheckedObject().stream().map(o -> ((ComponentUnit)o)).collect(Collectors.toList());
			childLot.setSubProcessUnit((List)mergeComponentUnits);
			
			ADManager adManager = Framework.getService(ADManager.class);
			VariableManager variableManager = Framework.getService(VariableManager.class);
			MBASManager mbasManager = Framework.getService(MBASManager.class);
			
			LotMergeRuleContext context = new LotMergeRuleContext();
			context.setObjectType(LotMergeRuleContext.OBJECT_TYPE_LOTLIST);
			context.setTransaction(LotMergeRuleContext.TRANSACTION_ASSIGN);
			context.setCategory(mergeRule.getCategory());
			SessionContext sc = new SessionContext();
			sc.setOrgRrn(parentLot.getOrgRrn());
			context.setSessionContext(sc);
			context.setBaseObject(parentLot);
			context.setCheckObject(childLot.getSubProcessUnit());
			context.setMergeRuleName(mergeRule.getName());
			context.setBasicCheck(true);
			context.setCheckAllRule(true);
			context.setAdManager(adManager);
			context.setVariableManager(variableManager);
			context.setMbasManager(mbasManager);	
			context.setLotManager(lotManager);
			
			MergeRuleResult result = RuleFactory.getMergeRule().checkMergeRule(context);
			if (CollectionUtils.isNotEmpty(result.getMergeRule().getMergeRuleLines())) {
				//����
				List<Lot> inputLots = new ArrayList<Lot>();
				List<MergeRuleLine> mergeRuleLines = result.getMergeRule().getMergeRuleLines().stream().sorted(Comparator.comparing(MergeRuleLine :: getSeqNo)).collect(Collectors.toList());
				mergeRuleLines = mergeRuleLines.stream().sorted(Comparator.comparing(MergeRuleLine :: getRuleType, Collections.reverseOrder())).collect(Collectors.toList());
				for (MergeRuleLine mergeRuleLine : mergeRuleLines) {
					Lot lot = new Lot();
					for (String mergeRuleName : result.getResultMap().keySet()) {
						if (mergeRuleLine.getRuleName().equals(mergeRuleName)) {
							lot.setAttribute1(mergeRuleName);
							lot.setAttribute2(mergeRuleLine.getRuleType());
							if (MergeRuleLine.EQUAL_OPERATOR_EQUAL.equals(mergeRuleLine.getOperator())) {
								lot.setAttribute3(MergeRuleLine.EQUAL_OPERATOR_EQUAL);
							} else {
								lot.setAttribute3(mergeRuleLine.getExpression());
							}
							lot.setAttribute4(result.getResultMap().get(mergeRuleName).toString());
						}
					}
					inputLots.add(lot);
				}
				mockLotResultField.getListTableManager().setInput(inputLots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	private List<ComponentUnit> getLotComponents(Lot lot) {
		try {
			LotManager lotManager = Framework.getService(LotManager.class);
			Lot lotWithComps = lotManager.getLotWithComponent(lot.getObjectRrn());
			List<ComponentUnit> componentUnits = Lists.newArrayList();
			for (ProcessUnit processUnit : lotWithComps.getSubProcessUnit()) {
				ComponentUnit componentUnit = (ComponentUnit) processUnit;
				componentUnit.setAttribute1(lot.getLotId());
				componentUnits.add(componentUnit);
			}
			
			componentUnits = componentUnits.stream().sorted(Comparator.comparingInt(
					c -> StringUtil.isEmpty(c.getPosition()) ? 0 : Integer.valueOf(c.getPosition()))).collect(Collectors.toList());
			return componentUnits;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	/**
	  * ��ѡ�¼�
	  * @ComponentUnit
	  */
	ICheckChangedListener checkChangedListener = new ICheckChangedListener() {
		@Override
		public void checkChanged(List<Object> eventObjects, boolean checked) {
			try {
				List<Object> checkObjects = lotQueryField.getCheckedObjects();
				if (checkObjects.size() > 2) {
					checkBoxTableViewerManager.unCheckObject(eventObjects.get(0));
				} else {
					if (checked) {
						if (CollectionUtils.isNotEmpty(eventObjects)) {
							for (Object obj : eventObjects) {
								Lot lot = (Lot) obj;
								checkBoxTableViewerManager.checkObject(lot);
							}
						}
					}
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				return;
			}
		}
	};
	 
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(
			convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
			Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
					shellSize.y));
	}

}