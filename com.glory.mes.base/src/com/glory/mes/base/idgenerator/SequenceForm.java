package com.glory.mes.base.idgenerator;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Group;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.idgenerator.GeneratorRule;
import com.glory.mes.base.model.idgenerator.GeneratorRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceRuleLine;

public class SequenceForm extends DSTypeForm {
	
	private static final String TABLE_NAME = "BASIDBaseOn";

	private GeneratorRule rule;

	private ListTableManager tableManager;
	
	public SequenceForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}
	
	@Override
	protected void createContent() {
		
        toolkit = new FormToolkit(getDisplay());
		 
        GridLayout gl = new GridLayout();
        gl.numColumns = 4;
        GridData gridData = new GridData(GridData.FILL_BOTH);
        
        Composite mainBody = toolkit.createComposite(this, SWT.NONE);
        mainBody.setLayout(gl);
        mainBody.setLayoutData(gridData);
        setForm(toolkit.createScrolledForm(mainBody));
        toolkit.paintBordersFor(this); 
        
        Composite body = getForm().getBody();
        GridLayout layout = new GridLayout();
        layout.marginLeft = -5;
//		layout.marginRight = -5;
		layout.marginTop = -5;
		layout.marginBottom = -5;
        body.setLayout(layout);
        
        // first: create the children controls and compute the
        // number of columns to be used by the grid layout
        int cols = 1;
        for (IField f : fields.values()){
            f.createContent(body, toolkit);
            
            Control[] ctrls = f.getControls();
            int c = f.getColumnsCount();
            c = c > ctrls.length ? c : ctrls.length;
            if (cols < c) {
                cols = c;
            }
        }
        layout.numColumns = cols * this.getGridY();
        
        // second: place the created controls inside the grid layout cells
        int i = 0;
        for (IField f : fields.values()) {
            Control[] ctrls = f.getControls();
            if (ctrls.length == 0) {
            	continue;
            }
            i++;
            if (i % getGridY() == 0 && getGridY() != 1){
	            GridData gd = (GridData)ctrls[0].getLayoutData();
	            if (gd == null) {
	                gd = new GridData();
	                ctrls[0].setLayoutData(gd);
	            }
	            gd.horizontalIndent = 10;
            }
            // get the last r controls that should be spanned horizontally
            // to fit into the grid
            int r = ctrls.length % cols;
            if (r > 0) {
                GridData gd = (GridData)ctrls[ctrls.length-1].getLayoutData();
                if (gd == null) {
                    gd = new GridData();
                    ctrls[ctrls.length-1].setLayoutData(gd);
                }
                gd.horizontalSpan = cols - r + 1;
            }
            if (f.getADField() != null){
            	ADField field = (ADField)f.getADField();
            	if (field.getIsSameline()){
            		int num = (i - 1) % this.getGridY();
            		if (num != 0){
            			Label l = toolkit.createLabel(ctrls[0].getParent(), "");
            			GridData gd = new GridData();
            			gd.horizontalSpan = cols * (this.getGridY() - num);
            			l.setLayoutData(gd);
            			l.moveAbove(ctrls[0]);
            		}
                	GridData gd = (GridData)ctrls[ctrls.length-1].getLayoutData();
                	if (gd == null) {
                        gd = new GridData();
                        ctrls[ctrls.length-1].setLayoutData(gd);
                    }
            		gd.horizontalSpan = cols * this.getGridY() - (r == 0 ? 1 : r - 1);
            		gd = (GridData)ctrls[0].getLayoutData();
    	            if (gd == null) {
    	                gd = new GridData();
    	                ctrls[0].setLayoutData(gd);
    	            }
    	            gd.horizontalIndent = 0;
            		i = 0;
            	}
            }
        }
        
        createBaseOnViewer(mainBody);
    }

	protected void createBaseOnViewer(Composite parent){
		try {
			Group hGroup = new Group(parent, SWT.NONE);
			hGroup.setText("Base On");
			hGroup.setBackground(new Color(null, 255, 255, 255));
			hGroup.setLayout(new GridLayout(1, true));
			
			GridData gd = new GridData(SWT.FILL, SWT.FILL, true, true, 2, 2);
			gd.heightHint = 10;
			gd.widthHint = 10;
			hGroup.setLayoutData(gd);
		    toolkit.paintBordersFor(parent); 
		    
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			tableManager = new ListTableManager(adTable, true);
			
			tableManager.newViewer(hGroup);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public boolean saveToObject() {
		boolean f1 = super.saveToObject();
		boolean f2 = saveBaseOns();
		return f1 && f2;
	}
	
	private boolean saveBaseOns(){
		SequenceRuleLine line = (SequenceRuleLine) this.getObject();
	    StringBuffer baseOns = new StringBuffer();
	    List<Object> objects = tableManager.getCheckedObject();
	    if (objects != null && objects.size() > 0) {
		    for (Object object : objects) {
		        GeneratorRuleLine ruleLine = (GeneratorRuleLine) object;
		        baseOns.append(ruleLine.getSeqNo() + ";");
				
			} 
		    line.setBaseOn(baseOns.toString());
		    this.setObject(line);
	    }
	    return true;
	}
	
	private void refreshBaseOnTable(){
		if (rule != null && rule.getObjectRrn() != null) {
			List<GeneratorRuleLine> generatorRuleLines = new ArrayList<GeneratorRuleLine>();
			generatorRuleLines.addAll(rule.getRuleLines());
			if (getObject() != null) {
				SequenceRuleLine line = (SequenceRuleLine) this.getObject();
				generatorRuleLines.remove(line);
			}
			
			tableManager.setInput(generatorRuleLines);
		} else {
			tableManager.setInput(new ArrayList<GeneratorRuleLine>());
		}
		
		if(getObject() != null){
			SequenceRuleLine line = (SequenceRuleLine) this.getObject();
			String baseOn = line.getBaseOn();
			if(baseOn != null){
				String[] baseOns = baseOn.split(";");
				for(String bo : baseOns){
					if (bo != null && bo.trim().length() > 0) {
						Long seqNo = Long.valueOf(bo);
						for (GeneratorRuleLine checkedLine : rule.getRuleLines()) {
							if (seqNo - checkedLine.getSeqNo() == 0) {
								tableManager.setCheckedObject(checkedLine);
							}
						}
					}
				}
			}
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
		refreshBaseOnTable();
	}

	public GeneratorRule getRule() {
		return rule;
	}

	public void setRule(GeneratorRule rule) {
		this.rule = rule;
	}
	
}
