package com.glory.mes.base.idgenerator;

import org.apache.log4j.Logger;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;

public class DateTimeForm extends DSTypeForm {
	private static final Logger logger = Logger.getLogger(DateTimeForm.class);

	private static final String FIELD_ID_DATETYPE = "dateType";
	private static final String FIELD_ID_SPECIFICDATE = "specificDate";
	private static final String FIELD_ID_PARAMETER = "parameter";

	public DateTimeForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	protected void createContent() {
		try {
			setGridY(2);
			super.createContent();
			addRelatedListener();
		} catch (Exception e) {
			logger.error("DateTimeForm : createContent()", e);
		}
	}

	private void addRelatedListener() {
		IField dateTypeField = fields.get(FIELD_ID_DATETYPE);
		final IField specificDateField = fields.get(FIELD_ID_SPECIFICDATE);
		final IField parameterField = fields.get(FIELD_ID_PARAMETER);
		if (dateTypeField != null && specificDateField != null) {
			if (dateTypeField instanceof RadioField) {
				RadioField rf = (RadioField) dateTypeField;
				Control[] ctrls = rf.getControls();
				GridLayout layout = (GridLayout) ctrls[ctrls.length - 1].getParent().getLayout();
				layout.horizontalSpacing = 2;
				ctrls[ctrls.length - 1].getParent().setLayout(layout);
				rf.addValueChangeListener(new IValueChangeListener() {

					@Override
					public void valueChanged(Object sender, Object newValue) {
						if ("SPECIFIC".equals(newValue)) {
							specificDateField.setEnabled(true);
							parameterField.setEnabled(false);
						} else if ("VARIABLE".equals(newValue)) {
							parameterField.setEnabled(true);
							specificDateField.setEnabled(false);
						} else {
							specificDateField.setEnabled(false);
							parameterField.setEnabled(false);
						}
					}
				});
			}
		}
	}
}
