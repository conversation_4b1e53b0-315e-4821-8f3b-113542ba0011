package com.glory.mes.base.idgenerator;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;
import org.eclipse.ui.forms.IManagedForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class IdGeneratorBlock extends EntityBlock {
	private static final Logger logger = Logger.getLogger(IdGeneratorBlock.class);
	IdGeneratorProperties page;
	
	public IdGeneratorBlock(ListTableManager tableManager) {
		super(tableManager);
	}
	
	@Override
	protected void createToolBarActions(IManagedForm managedForm) {
	    if (getTableManager().getADTable().getIsVertical().booleanValue()) {
	      this.sashForm.setOrientation(512);
	      this.sashForm.setWeights(new int[] { 5, 5 });
	    } else {
	      this.sashForm.setOrientation(256);
	      this.sashForm.setWeights(new int[] { 3, 7 });
	    }
	  }
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable table = getTableManager().getADTable();
			Class<?> klass = Class.forName(table.getModelClass());
			page = new IdGeneratorProperties();
			page.setTable(table);
			page.setMasterParent(this);
			detailsPart.registerPage(klass, page);
		} catch (Exception e){
			logger.error("IdGeneratorBlock : registerPages ", e);
		}
	}

	public void setFocus() {
		((IdGeneratorProperties)page).setFocus();
	}
}
