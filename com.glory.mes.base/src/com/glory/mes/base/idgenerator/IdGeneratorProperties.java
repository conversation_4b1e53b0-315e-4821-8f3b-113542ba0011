package com.glory.mes.base.idgenerator;

import java.util.ArrayList;
import java.util.List;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.common.context.model.ContextValue;
import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.BASManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADSequence;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.VersionControlProperties;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.model.idgenerator.GeneratorRule;
import com.glory.mes.base.model.idgenerator.GeneratorRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceCycleRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceRuleLine;
import com.glory.framework.core.exception.ExceptionBundle;

public class IdGeneratorProperties extends VersionControlProperties {
	private static final Logger logger = Logger.getLogger(IdGeneratorProperties.class);
	
	public static final String KEY_INACTIVE = "InActive";
	protected ToolItem itemInActive;
	
	protected GeneratorForm eForm;
	public IdGeneratorProperties () {
		super();
	}
	
	@Override
	public void createToolItemCopyFrom(ToolBar tBar){
		
	}
	
	@Override
	protected IForm getForm(Composite composite, ADTab tab) {
		IForm eForm = null;
		if(tab.getSeqNo() == 1){
			eForm = new GeneratorForm(composite, SWT.NONE, tab, mmng, this);
		}else{
			eForm = super.getForm(composite, tab);
		}
		return eForm;
	}
	
	@Override
    public void createToolBar(Section section) {
        ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
        createToolItemNew(tBar);
        createToolItemCopyFrom(tBar);
        createToolItemSave(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemFrozen(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemActive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemInActive(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemDelete(tBar);
        new ToolItem(tBar, SWT.SEPARATOR);
        createToolItemRefresh(tBar);
        section.setTextClient(tBar);
    }
	
	protected void createToolItemInActive(ToolBar tBar) {
		itemInActive = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_INACTIVE);;
		itemInActive.setImage(SWTResourceCache.getImage("inactive"));
		itemInActive.setText(Message.getString(ExceptionBundle.bundle.CommonInActive()));
		itemInActive.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				inActiveAdapter();
			}
		});
	}
	
    protected void inActiveAdapter() {
        try {
            form.getMessageManager().removeAllMessages();
            if (getAdObject() != null) {
                boolean saveFlag = true;
                for (IForm detailForm : getDetailForms()) {
                    if (!detailForm.saveToObject()) {
                        saveFlag = false;
                    }
                }
                if (saveFlag) {
                	GeneratorRule rule = (GeneratorRule)getAdObject();	
					BASManager basManager = Framework.getService(BASManager.class);
					basManager.frozen(rule, Env.getSessionContext());
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
					this.setAdObject(rule);
					this.refreshAdapter();
                }
            }
        } catch (Exception e) {
            ExceptionHandlerManager.asyncHandleException(e);
            return;
        }
    }
	
	@Override
	protected void saveAdapter() {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			ADBase oldBase = getAdObject();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					
					List<GeneratorRuleLine> ruleLines = new ArrayList<GeneratorRuleLine>();
					ADManager entityManager = Framework.getService(ADManager.class);
					GeneratorRule rule = (GeneratorRule) getAdObject();
					if(rule.getObjectRrn() != null){
						GeneratorRule oldRule = (GeneratorRule) entityManager.getEntity(rule);
						ruleLines.addAll(oldRule.getRuleLines());
					}else{
						List<GeneratorRule> ruleList = entityManager.getEntityList(Env.getOrgRrn(), GeneratorRule.class, Integer.MAX_VALUE, 
								" name = '" + rule.getName() + "'", "");
						if ( ruleList.size() > 0 ) {
							boolean isNew = UI.showConfirm(rule.getName() + ":" + Message.getString("prd.version_infor") + ( ruleList.size() + 1));
							if ( isNew ) {
								rule.setVersion(Long.valueOf(ruleList.size() + 1));
							}else{
								return;
							}
						}
						rule.setStatus(VersionControl.STATUS_UNFROZNE);
					}
					
					for(IForm eForm : getDetailForms()){
						if(eForm instanceof GeneratorForm){
							GeneratorForm gf = (GeneratorForm)eForm;
							GeneratorRuleLine line = gf.getRuleLine();
							
							if (line == null ) {
								return;
							}
							
							if (checkRule(line)) {
								UI.showError(Message.getString("base.idgenerator_size_rule"));
								return;
							}
							
							if (line.getObjectRrn() == null) {
								line.setSeqNo(ruleLines.size() + 1l);
								line.setOrgRrn(Env.getOrgRrn());
								ruleLines.add(line);
							} else {
								for (int i = 0; i < ruleLines.size(); i++) {
									if ( ruleLines.get(i).getObjectRrn().intValue() == line.getObjectRrn().intValue() 
											|| ruleLines.get(i).getObjectRrn().equals(line.getObjectRrn()) ) {
										GeneratorRuleLine lastLine = (GeneratorRuleLine) entityManager.getEntity(line);
										if (lastLine == null) {
											ruleLines.remove(i);
										}else{
											line.setSeqNo(ruleLines.get(i).getSeqNo());
											ruleLines.set(i, line);
										}
									}
								}
							}
						}
					}
					
					rule.setRuleLines(ruleLines);
					GeneratorRule obj = (GeneratorRule)entityManager.saveEntity(rule, Env.getSessionContext());
					
					//tableManager�����ݱ仯ʱ,����ӦselectionChanged�¼�
					ADBase newBase = entityManager.getEntity(obj);
					if (oldBase.getObjectRrn() == null) {
						getMasterParent().refreshAdd(newBase);
					} else {
						getMasterParent().refreshUpdate(newBase);
					}
					refreshAdapter();
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
					refresh();
					//���õ�ǰѡ��ֵ
					getMasterParent().getTableManager().setSelection(new StructuredSelection(new Object[] {newBase}));
				}
			}
		} catch (Exception e) {
			logger.error("IdGeneratorProperties : save()",e);
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			form.getMessageManager().setAutoUpdate(true);
		}	
	}
	
	@Override
	protected void activeAdapter() {
		try {
			form.getMessageManager().setAutoUpdate(false);
			form.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						if (detailForm instanceof GeneratorForm) {

						} else if (detailForm instanceof EntityForm) {
							if (((EntityForm)detailForm).getObject() instanceof GeneratorRuleLine) {						
								
							} else {
								PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());
							}
						} else {		
							PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());
						}						
					}
					
					GeneratorRule rule = (GeneratorRule)getAdObject();	
					ADManager adManager = Framework.getService(ADManager.class);
					BASManager basManager = Framework.getService(BASManager.class);
					if (VersionControl.STATUS_FROZNE.equalsIgnoreCase(rule.getStatus()) || VersionControl.STATUS_INACTIVE.equalsIgnoreCase(rule.getStatus())) {
						List<GeneratorRule> rList = adManager.getEntityList(Env.getOrgRrn(), GeneratorRule.class, Integer.MAX_VALUE,
								" name = '" + rule.getName() + "' and status = '" + VersionControl.STATUS_ACTIVE + "'", "");
						if ( rList.size() > 0 ) {
							basManager.inActive(rList.get(0), Env.getSessionContext());
						}
						basManager.active(rule, Env.getSessionContext());
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonActiveSuccess()));
					} else {
						basManager.frozen(rule, Env.getSessionContext());
						UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonInActiveSuccess()));
					}	
					this.setAdObject(rule);
					this.refreshAdapter();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			form.getMessageManager().setAutoUpdate(true);
		}
	}
	
	@Override
	public void deleteAdapter() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));

			if (confirmDelete) {
				ADManager adManager = Framework.getService(ADManager.class);
				form.getMessageManager().setAutoUpdate(false);
				form.getMessageManager().removeAllMessages();

				// �ж��Ƿ����Context
				GeneratorRule rule = (GeneratorRule) getAdObject();
				List<ContextValue> lis = adManager.getEntityList(Env.getOrgRrn(), ContextValue.class, Integer.MAX_VALUE,
						" status != 'Delete' and resultValue1 = '" + rule.getName() + "'", "");
				if (lis != null && lis.size() > 0) {
					UI.showInfo(Message.getString("base.idgenerator_has_been_used_cannot_delete"));
					return;
				}

				// ���AD_SEQUENCE�Ƿ��������
				List<GeneratorRuleLine> ruleLines = rule.getRuleLines();
				List<ADSequence> seq = new ArrayList<ADSequence>();
				for (int i = 0; i < ruleLines.size(); i++) {
					long ruleRrn = ruleLines.get(i).getObjectRrn();
					seq = adManager.getEntityList(Env.getOrgRrn(), ADSequence.class, Integer.MAX_VALUE,	" generatorRrn = " + ruleRrn, "");
				}

				if (seq != null && seq.size() > 0) {
					UI.showInfo(Message.getString("base.idgenerator_has_been_used_cannot_delete"));
					return;
				}
				// ִ��ɾ������
				BASManager basManager = Framework.getService(BASManager.class);
				basManager.delete(rule, Env.getSessionContext());
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));
				refresh();
				getMasterParent().refresh();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Override
	public void statusChanged(String newStatus) {
		buildTitle(newStatus);
		if (VersionControl.STATUS_UNFROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("frozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
			setEnable(true);
		} else if (VersionControl.STATUS_FROZNE.equals(newStatus)) {
			itemFrozen.setImage(SWTResourceCache.getImage("unfrozen"));
			itemFrozen.setText(Message.getString(ExceptionBundle.bundle.CommonUnFrozen()));
			itemFrozen.setEnabled(true);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(true);
			itemInActive.setEnabled(false);
		} else if (VersionControl.STATUS_ACTIVE.equals(newStatus)) {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(true);
		}  else if (VersionControl.STATUS_INACTIVE.equals(newStatus)) {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(false);
			itemDelete.setEnabled(true);
			itemActive.setEnabled(true);
			itemInActive.setEnabled(false);
		} else {
			itemFrozen.setEnabled(false);
			itemSave.setEnabled(true);
			itemDelete.setEnabled(false);
			itemActive.setEnabled(false);
			itemInActive.setEnabled(false);
			setEnable(true);
		}
	}
	
	private boolean checkRule(GeneratorRuleLine line) {
		boolean flag = false;
		if (line instanceof SequenceRuleLine) {
			if ("4".equals(((SequenceRuleLine) line).getSequenceType())) {
				if (((SequenceRuleLine) line).getSizee() < 2) {
					flag = true;
				}
			}
		}
		return flag;
	}
}

