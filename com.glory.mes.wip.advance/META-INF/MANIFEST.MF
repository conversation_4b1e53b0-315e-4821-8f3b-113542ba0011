Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Wip Advance Plug-in
Bundle-SymbolicName: com.glory.mes.wip.advance;singleton:=true
Bundle-Version: 8.4.0
Bundle-Activator: com.glory.mes.wip.advance.Activator
Bundle-Vendor: GlorySoft
Require-Bundle: org.eclipse.ui;bundle-version="3.107.0",
 org.eclipse.core.runtime;bundle-version="3.11.0",
 com.glory.mes.wip;bundle-version="8.4.0",
 com.glory.framework.base;bundle-version="8.4.0",
 com.glory.framework.lib;bundle-version="8.4.0",
 com.glory.framework.runtime;bundle-version="8.4.0",
 com.glory.mes.prd;bundle-version="8.4.0",
 org.eclipse.ui.forms;bundle-version="3.6.200",
 org.eclipse.nebula.widgets.nattable.core;bundle-version="1.3.0",
 org.apache.commons.lang;bundle-version="2.6.0",
 org.apache.log4j;bundle-version="1.2.15",
 com.glory.mes.prd.designer;bundle-version="8.4.0",
 javax.annotation;bundle-version="1.3.5",
 javax.inject;bundle-version="1.0.0"
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Bundle-ActivationPolicy: lazy
Export-Package: com.glory.mes.wip.advance,
 com.glory.mes.wip.advance.component.reserved,
 com.glory.mes.wip.advance.flowcondition,
 com.glory.mes.wip.advance.future,
 com.glory.mes.wip.advance.future.flowaction,
 com.glory.mes.wip.advance.future.hold,
 com.glory.mes.wip.advance.future.multihold,
 com.glory.mes.wip.advance.future.note,
 com.glory.mes.wip.advance.future.procedurehold,
 com.glory.mes.wip.advance.future.procedurenote,
 com.glory.mes.wip.advance.future.query,
 com.glory.mes.wip.advance.future.query.lot,
 com.glory.mes.wip.advance.future.query.procedure,
 com.glory.mes.wip.advance.future.query.procedurehold,
 com.glory.mes.wip.advance.future.query.startedtimer,
 com.glory.mes.wip.advance.future.query.timer,
 com.glory.mes.wip.advance.future.step,
 com.glory.mes.wip.advance.future.timer,
 com.glory.mes.wip.advance.future.timer.multiproc,
 com.glory.mes.wip.advance.future.timer.multitimer,
 com.glory.mes.wip.advance.future.timer.step
Import-Package: org.eclipse.e4.core.services.events,
 org.eclipse.e4.ui.model.application,
 org.eclipse.e4.ui.model.application.ui,
 org.eclipse.e4.ui.model.application.ui.basic,
 org.eclipse.e4.ui.workbench.modeling,
 org.osgi.service.event;version="1.3.1"
