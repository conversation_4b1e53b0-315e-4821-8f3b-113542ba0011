package com.glory.mes.wip.advance.batchquery;


import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.mes.wip.advance.future.query.FutureActionQueryItemAdapter;

public class FutureActionQueryFixTableManager extends FixSizeListTableManager {

	public FutureActionQueryFixTableManager(ADTable adTable, boolean checkFlag) {
		super(adTable, checkFlag);
	}

	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new FutureActionQueryItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
}

