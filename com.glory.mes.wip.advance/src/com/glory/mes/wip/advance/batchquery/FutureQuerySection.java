package  com.glory.mes.wip.advance.batchquery;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.SectionPart;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureNote;
import com.glory.framework.core.exception.ExceptionBundle;

public class FutureQuerySection extends QueryEntityListSection implements IRefresh {
	
	private Button btnNote;
	private Button btnHold;
	private Button btnTimer;
	private Button btnMerge;
	
	private ToolItem itemCancelNote;
	private ToolItem itemCancelHold;
	
	private List<String> actionTypes;
	
	public ListTableManager tableManager;
	
	public FutureQuerySection(ListTableManager tableManager) {
		super(tableManager);
		this.tableManager = tableManager;
	}

	@Override
	public void createContents(final IManagedForm form, Composite parent, int sectionStyle) {
		final FormToolkit toolkit = form.getToolkit();
		final ADTable table = getADTable();
		managedForm = (ManagedForm) form;
		
		section = toolkit.createSection(parent, sectionStyle);
		section.setText(I18nUtil.getI18nMessage(table, "label"));
		section.marginWidth = 3;
	    section.marginHeight = 4;
	    if ((sectionStyle & Section.NO_TITLE) == 0) {
	    	toolkit.createCompositeSeparator(section);
	    }

	    createToolBar(section);
		
	    setLayout(parent);
	    
		GridLayout layout = new GridLayout();
		layout.marginWidth = 5;
		layout.marginHeight = 5;
		section.setLayout(layout);
	    
	    Composite client = toolkit.createComposite(section);    
	    layout = new GridLayout();    
	    layout.numColumns = 1;    
	    client.setLayout(layout);
	    
	    spart = new SectionPart(section);    
	    form.addPart(spart);
	    section.setText(String.format(Message.getString(ExceptionBundle.bundle.CommonList()), I18nUtil.getI18nMessage(table, "label")));  

        Composite queryComp = new Composite(client, SWT.NONE);
        GridData gd = new GridData(GridData.FILL_HORIZONTAL);
        queryComp.setLayoutData(gd);
        layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        layout.numColumns = 2;    
        queryComp.setLayout(layout);
        
        setQueryForm(createQueryFrom(form, queryComp));
        gd = new GridData(GridData.FILL_BOTH);
        getQueryForm().setLayoutData(gd);

        Composite buttonBar = toolkit.createComposite(queryComp, SWT.NONE);
        gd = new GridData(GridData.FILL_VERTICAL);
        gd.widthHint = 60;
        gd.horizontalAlignment = SWT.END;
        buttonBar.setLayoutData(gd);
        
		layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginBottom = 15;
		buttonBar.setLayout(layout);
		
		Button btnQuery = toolkit.createButton(buttonBar, Message.getString(ExceptionBundle.bundle.CommonSearch()), SWT.PUSH);
		gd = new GridData(GridData.FILL_BOTH);
		gd.verticalAlignment = SWT.END;
		btnQuery.setLayoutData(gd);
		btnQuery.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				queryAdapter();
			}
		});
		
		Composite right = toolkit.createComposite(queryComp);
		layout = new GridLayout(4, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL | GridData.FILL_BOTH);
		gd.horizontalAlignment = SWT.BEGINNING;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
		
		btnHold = new Button(right, SWT.CHECK | SWT.LEFT);
		btnHold.setSelection(true);
		btnHold.setText("FutureHold");
		
		btnNote = new Button(right, SWT.CHECK);
		btnNote.setText("FutureNote");
		btnNote.setSelection(true);
		
		btnTimer = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnTimer.setText("FutureTimer");
		btnTimer.setSelection(true);
		
		btnMerge = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnMerge.setText("MergeHold");
		btnMerge.setSelection(true);
		
		Composite resultComp = new Composite(client, SWT.NONE);
        layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        resultComp.setLayout(layout);
        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
	    createNewViewer(resultComp, form);
	    section.setClient(client);
	}
	

	public List<String> getActionTypes() {
		actionTypes = new ArrayList<String>();
		if (this.btnNote.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_NOTE);
		}
		if (this.btnHold.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_HOLD);
		}
		if (this.btnTimer.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_TIMER);
		}
		if (this.btnMerge.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_MERGE);
		}
		return actionTypes;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNote(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	private void createToolItemNote(ToolBar tBar) {
		itemCancelNote = new AuthorityToolItem(tBar, SWT.PUSH , "WipAdv.FutureMultiQuery.wipadv.cancel_note");
		itemCancelNote.setText("CancelNote");
		itemCancelNote.setImage(SWTResourceCache.getImage("note"));
		itemCancelNote.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelnote"))){
					cancelNoteAdapter();
				}
			}
		});
	}
	
	protected void cancelNoteAdapter() {
		try {
			List<Object> checkedObjects = tableManager.getCheckedObject();
            if (checkedObjects != null && !checkedObjects.isEmpty()) {
            	List<FutureAction> actions = new ArrayList<FutureAction>();
            	for (Object o : checkedObjects) {
            		if (o instanceof FutureNote) {
            			FutureNote action = (FutureNote) o;
                		actions.add(action);
            		}
            	}
            	LotManager lotManager = Framework.getService(LotManager.class);
            	lotManager.deleteFutureAction(actions, Env.getSessionContext());
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	protected void createToolItemHold(ToolBar tBar) {
		itemCancelHold = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.FutureMultiQuery.wipadv.cancel_hold");
		itemCancelHold.setText("CancelHold");
		itemCancelHold.setImage(SWTResourceCache.getImage("hold"));
		itemCancelHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelhold"))){
					cancelHoldAdapter();
				}
			}
		});
	}
	
	protected void cancelHoldAdapter() {
		try {
			List<Object> checkedObjects = tableManager.getCheckedObject();
            if (checkedObjects != null && !checkedObjects.isEmpty()) {
            	List<FutureAction> actions = new ArrayList<FutureAction>();
            	for (Object o : checkedObjects) {
            		if (o instanceof FutureHold) {
            			FutureHold action = (FutureHold) o;
                		actions.add(action);
            		}
            	}
            	LotManager lotManager = Framework.getService(LotManager.class);
            	lotManager.deleteFutureAction(actions, Env.getSessionContext());
				refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void queryAdapter() {
		managedForm.getMessageManager().removeAllMessages();
		if (!getQueryForm().validate()){
			return;
		}
		
		String whereClause = " 1 = 1 " + getQueryForm().createWhereClause();
		
		if (getActionTypes().size() > 0) {
			String actionTypes = "";
			for (String actionType : getActionTypes()) {
				actionTypes = actionTypes + actionType + "','";
			}
			whereClause = whereClause + "AND action IN ('" + actionTypes.substring(0, actionTypes.length() - 3) + "')";
		} else {
			whereClause = whereClause + "AND action NOT IN ('N','H','T','M')";
		}
		
		whereClause = StringUtil.relpaceWildcardCondition(whereClause);
		setWhereClause(whereClause);
		refresh();
	}
}
