package com.glory.mes.wip.advance.component.reserved;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.swt.widgets.TreeItem;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.lot.flow.LotFlowTreeField;
import com.glory.mes.wip.lot.flow.LotFlowTreeManager;
import com.glory.mes.wip.model.ComponentUnitReserved;
import com.glory.framework.core.exception.ExceptionBundle;

public class ComponentReseredSection extends EntitySection {

	private ComponentListForm componentListForm;
	private EntityForm entityForm;
	private QueryTableForm queryTableForm;

	protected LotFlowTreeManager manager;
	protected LotFlowSection lotFlowSection;

	protected ToolItem itemSave;
	protected AuthorityToolItem itemDelete;
	
	public static String KEY_SAVE = "save";
	public static String KEY_DELETE = "delete";
	public ComponentReseredSection(ADTable adTable) {
		super(adTable);
	}

	@Override
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		mmng = getMessageManager();

		MDSashForm sashForm = new MDSashForm(client, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);

		ComponentLotMediator lotMediator = new ComponentLotMediator();

		lotFlowSection = new LotFlowSection(table, lotMediator, manager);
		lotFlowSection.createContents(form, sashForm);

		componentListForm = new ComponentListForm(sashForm, SWT.NONE, null, mmng);
		entityForm = new EntityForm(sashForm, SWT.NONE, this.getAdObject(), table, mmng);
		entityForm.setADManager(getADManger());
		detailForms.add(entityForm);

		lotMediator.setComponentListForm(componentListForm);
		componentListForm.setSection(this);
	}
	
	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_SAVE);
		itemSave.setText(Message.getString(ExceptionBundle.bundle.CommonSave()));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}
	
	protected void createToolItemDelete(ToolBar tBar) {
		itemDelete = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_DELETE);
		itemDelete.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
		itemDelete.setImage(SWTResourceCache.getImage("delete"));
		itemDelete.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				deleteAdapter();
			}
		});
	}

	@Override
	public ADBase save(ADBase obj) throws Exception {
		try {
			List<ComponentUnitReserved> checkReserveds = componentListForm.getCheckedComponentUnitReserved();
			ComponentUnitReserved reserved = (ComponentUnitReserved) obj;

			if (checkReserveds.size() == 0) {
				reserved.setReservedSource(ComponentUnitReserved.RESERVED_SOURCE_MANUAL);
				checkReserveds.add(reserved);
			} else {
				for (ComponentUnitReserved checkReserved : checkReserveds) {
					checkReserved.setReservedSource(ComponentUnitReserved.RESERVED_SOURCE_MANUAL);
					checkReserved.setEquipmentRecipeName(reserved.getEquipmentRecipeName());
				}
			}
			
			ComponentManager componentManager = Framework.getService(ComponentManager.class);
			List<ComponentUnitReserved> reserveds = componentManager.saveComponentUnitReserved(checkReserveds,
					ComponentUnitReserved.RESERVED_TYPE_RECIPE, Env.getSessionContext());
			obj = reserveds.get(0);
			if (queryTableForm != null) {
				queryTableForm.refresh();
			}
			componentListForm.loadFromObject();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return obj;
	}

	@Override
	public boolean delete() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				List<ComponentUnitReserved> unReserveds = new ArrayList<ComponentUnitReserved>();

				List<ComponentUnitReserved> checkReserveds = componentListForm.getCheckedComponentUnitReserved();
				ComponentUnitReserved reserved = (ComponentUnitReserved) getAdObject();

				if (checkReserveds.size() == 0 && reserved.getObjectRrn() != null) {
					unReserveds.add(reserved);
				} else {
					for (ComponentUnitReserved checkReserved : checkReserveds) {
						if (checkReserved.getObjectRrn() != null) {
							unReserveds.add(checkReserved);
						}
					}
				}
				
				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				componentManager.unReserveComponentUnit(unReserveds, Env.getSessionContext());
				setAdObject(createAdObject());
				refresh();
				if (queryTableForm != null) {
					queryTableForm.refresh();
				}
				componentListForm.loadFromObject();
				return true;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}

	public void setObject(ComponentUnitReserved reserved) {
		lotFlowSection.txtLot.setText(reserved.getLotId());
		componentListForm.setStepName(reserved.getStepName());
		lotFlowSection.excuteSearch();

		LotFlowTreeField treeField = (LotFlowTreeField) lotFlowSection.getField("process");
		TreeItem parentItem = treeField.getTreeViewer().getTree().getItems()[0];
		TreeItem[] treeItems = parentItem.getItems();
		for (TreeItem treeItem : treeItems) {
			TreeItem[] stepItems = treeItem.getItems();
			boolean flag = false;
			for (TreeItem stepItem : stepItems) {
				if (StringUtils.contains(stepItem.getText(), reserved.getStepName())) {
					treeField.getTreeViewer().getTree().setSelection(stepItem);
					flag = true;
					break;
				}
			}

			if (flag) {
				break;
			}
		}

		setAdObject(reserved);
		refresh();
	}

	public void setQueryTableForm(QueryTableForm queryTableForm) {
		this.queryTableForm = queryTableForm;
	}
}
