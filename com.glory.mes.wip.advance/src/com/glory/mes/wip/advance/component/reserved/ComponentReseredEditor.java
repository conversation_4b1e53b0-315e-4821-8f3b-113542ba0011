package com.glory.mes.wip.advance.component.reserved;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.forms.FMessageManager;
import com.glory.mes.wip.model.ComponentUnitReserved;

public class ComponentReseredEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.component.reserved.ComponentReseredEditor";

	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;

	protected SashForm sashForm;

	private QueryTableForm queryForm;
	private ComponentReseredSection section;

	@PostConstruct
	public void postConstruct(Composite parent) {
		ADTable adTable = (ADTable) mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);

		FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
		ScrolledForm form = toolkit.createScrolledForm(parent);

		ManagedForm mform = new ManagedForm(toolkit, form);

		Composite body = form.getBody();
		configureBody(body);

		// ������ѯform
		createQueryForm(body, adTable, new FMessageManager());

		queryForm.getTableManager().addSelectionChangedListener(new ISelectionChangedListener() {

			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection selection = (StructuredSelection) event.getSelection();
				Object object = selection.getFirstElement();
				if (object instanceof ComponentUnitReserved) {
					ComponentUnitReserved reserved = (ComponentUnitReserved) object;
					section.setObject(reserved);
				}
			}
		});
		GridData gd = new GridData(GridData.FILL_BOTH);
		gd.heightHint = 220;
		queryForm.setLayoutData(gd);
		
		Composite sectionComposite = toolkit.createComposite(body, SWT.NULL);
		GridLayout layout = new GridLayout(1, false);
		sectionComposite.setLayout(layout);
		sectionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

		section = new ComponentReseredSection(adTable);
		section.createContents(mform, sectionComposite);
		section.setQueryTableForm(queryForm);

	}

	public void createQueryForm(Composite parent, ADTable adTable, IMessageManager manager) {
		queryForm = new QueryTableForm(parent, SWT.NONE, adTable, manager);
		GridData gridData = new GridData(GridData.FILL_BOTH);
		queryForm.setLayoutData(gridData);
		queryForm.setLayout(new GridLayout(1, false));
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout(1, false);
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;

		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
}
