package com.glory.mes.wip.advance.component.reserved;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.ComponentManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.ComponentUnit;
import com.glory.mes.wip.model.ComponentUnitReserved;
import com.glory.mes.wip.model.Lot;

public class ComponentListForm extends EntityForm {

	private ListTableManager listTableManager;
	private List<ComponentUnitReserved> reserveds;
	private EntitySection section; 
	private String stepName;

	public ComponentListForm(Composite parent, int style, Object object, IMessageManager mmng) {
		super(parent, style, object, mmng);
		createContent();
	}

	private static final String TABLE_COMPONENT = "WIPComponentUnitReseredList";

	@Override
	protected void createContent() {
		toolkit = new FormToolkit(getDisplay());

		GridLayout layout = new GridLayout();
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		setLayout(new GridLayout(1, true));

		toolkit.setBackground(getBackground());
		form = toolkit.createScrolledForm(this);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite body = getForm().getBody();
		layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginWidth = mMarginWidth;
		layout.marginHeight = mMarginHeight;
		layout.marginLeft = mLeftPadding;
		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));

		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_COMPONENT);

			listTableManager = new ListTableManager(adTable, true);
			listTableManager.newViewer(body);

			listTableManager.addSelectionChangedListener(new ISelectionChangedListener() {

				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection selection = (StructuredSelection) event.getSelection();
					Object object = selection.getFirstElement();
					if (object instanceof ComponentUnit) {
						ComponentUnit unit = (ComponentUnit) object;
						Optional<ComponentUnitReserved> reserved = null;
						if (reserveds != null && !reserveds.isEmpty()) {
							reserved = reserveds.stream().filter(unitReserved -> StringUtils
									.equals(unitReserved.getComponentId(), unit.getComponentId())).findFirst();
						}
						if (reserved != null && reserved.isPresent()) {
							section.setAdObject(reserved.get());
							section.refresh();
						} else {
							Lot lot = (Lot) getObject();
							ComponentUnitReserved unitReserved = new ComponentUnitReserved();
							unitReserved.setOrgRrn(Env.getOrgRrn());
							unitReserved.setLotId(lot.getLotId());
							unitReserved.setComponentId(unit.getComponentId());
							unitReserved.setStepName(stepName);

							section.setAdObject(unitReserved);
							section.refresh();
						}
					}
				}
			});
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public void loadFromObject() {
		if (getObject() != null) {
			Lot lot = (Lot) getObject();
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				lot = lotManager.getLotWithComponentOrderByPosition(lot.getObjectRrn(), true);

				listTableManager.setInput(lot.getSubProcessUnit());
				listTableManager.refresh();

				ComponentManager componentManager = Framework.getService(ComponentManager.class);
				if (StringUtils.isEmpty(stepName)) {
					stepName = lot.getStepName();
				}
				reserveds = componentManager.getComponentUnitReservedByLotId(
						Env.getOrgRrn(), lot.getLotId(), lot.getProcedureName(), stepName, ComponentUnitReserved.RESERVED_TYPE_RECIPE);

				if (reserveds != null) {
					for (Object object : listTableManager.getInput()) {
						ComponentUnit componentUnit = (ComponentUnit) object;
						for (ComponentUnitReserved componentUnitReserved : reserveds) {
							if (StringUtils.equals(componentUnitReserved.getComponentId(),
									componentUnit.getComponentId())) {
								componentUnit.setAttribute1(componentUnitReserved.getEquipmentRecipeName());
							}
						}
					}
				}

			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
		}
	}

	public void setSection(EntitySection section) {
		this.section = section;
	}

	public void setStepName(String stepName) {
		this.stepName = stepName;
	}
	
	public List<ComponentUnitReserved> getCheckedComponentUnitReserved() {
		List<ComponentUnitReserved> checkComponentReserves = new ArrayList<ComponentUnitReserved>();
		
		List<ComponentUnit> checkComponents = (List)listTableManager.getCheckedObject();
		Lot lot = (Lot) getObject();
		for (ComponentUnit checkComponent : checkComponents) {
			ComponentUnitReserved checkComponentReserved = null;
			if (reserveds != null && !reserveds.isEmpty()) {
				Optional<ComponentUnitReserved> reserved = reserveds.stream().filter(unitReserved -> StringUtils
						.equals(unitReserved.getComponentId(), checkComponent.getComponentId())).findFirst();
				if (reserved != null && reserved.isPresent()) {
					checkComponentReserved = reserved.get();
				} 
			}
			if (checkComponentReserved != null) {
				checkComponentReserves.add(checkComponentReserved);
			} else {
				ComponentUnitReserved unitReserved = new ComponentUnitReserved();
				unitReserved.setOrgRrn(Env.getOrgRrn());
				unitReserved.setLotId(lot.getLotId());
				unitReserved.setComponentId(checkComponent.getComponentId());
				unitReserved.setStepName(stepName);
				checkComponentReserves.add(unitReserved);
			}
		}
		return checkComponentReserves;
	}

}
