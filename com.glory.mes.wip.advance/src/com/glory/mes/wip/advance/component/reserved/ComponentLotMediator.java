package com.glory.mes.wip.advance.component.reserved;

import org.eclipse.swt.events.SelectionEvent;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.model.Lot;

public class ComponentLotMediator extends LotMediator {
	
	private ComponentListForm componentListForm;
	
	@Override
	public void notifySection(ADBase adBase) {
		if(adBase instanceof Lot) {
			componentListForm.setObject(adBase);
			componentListForm.loadFromObject();
			notifySectionAdapter(adBase);
		}
	}
	
	@Override
	protected void selectionChangedAdapter(SelectionEvent event) {
		Object object = event.item.getData();
		if (object instanceof StepState) {
			StepState stepState = (StepState) object;
			componentListForm.setStepName(stepState.getUsedStep().getName());
			componentListForm.loadFromObject();
		}
	}

	public void setComponentListForm(ComponentListForm componentListForm) {
		this.componentListForm = componentListForm;
	}
	
}
