package com.glory.mes.wip.advance.future.timer.multiproc;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.resource.ImageDescriptor;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.adapter.StepStateItemAdapter;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.future.FutureTimer;

public class MultiProcedureItemAdapter extends StepStateItemAdapter {

	private static final Logger logger = Logger.getLogger(MultiProcedureItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	
	private Process process;
	
	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState) {
			List<Object> children = new ArrayList<Object>();
			
			StepState stepState = (StepState)object;
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<String> timeTypes = new ArrayList<String>();
				timeTypes.add(FutureAction.ACTION_MULTIPROCEDURETIMER);
				if (stepState.getOrgRrn() == 0) {
					stepState.setOrgRrn(Env.getOrgRrn());
				}
				//Timer��ʼλ�ü��
				List<? extends FutureTimer> startTimers = lotManager.getFutureTimerByStart(stepState, timeTypes, null);
				for (FutureTimer timer : startTimers) {
					if (timer instanceof FutureMultiProcedureTimer) {
						FutureMultiProcedureTimer pTimer = (FutureMultiProcedureTimer)timer;
						//���Process�Ƿ���ͬ 
						if (pTimer.getProcessName().equals(process.getName()) && 
								(timer.getParentRrn() == null || timer.getObjectRrn().compareTo(timer.getParentRrn()) == 0)) {
							if (pTimer.getProcessVersion() != null) {
								if (pTimer.getProcessVersion().equals(process.getVersion())) {
									children.add(pTimer);
								}
							} else {
								children.add(pTimer);
							}
						}
					}
				}

				//Timer����λ�ü��
				List<? extends FutureTimer> endTimers = lotManager.getFutureTimerByEnd(stepState, timeTypes);
				for (FutureTimer timer : endTimers) {
					if (timer instanceof FutureMultiProcedureTimer) {
						FutureMultiProcedureTimer pTimer = (FutureMultiProcedureTimer)timer;
						//���Process�Ƿ���ͬ
						if (pTimer.getProcessName().equals(process.getName())) {
							if (pTimer.getProcessVersion() != null) {
								if (pTimer.getProcessVersion().equals(process.getVersion())) {
									EndTimer endTimer = new EndTimer(pTimer);
									children.add(endTimer);
								}
							} else {
								EndTimer endTimer = new EndTimer(pTimer);
								children.add(endTimer);
							}
						}
					}
				}
				
				//Timerδ����ͣλ�ü��
				ADManager adManager = Framework.getService(ADManager.class);
				List<FutureMultiProcedureTimer> futureHoldTimers = adManager.getEntityList(Env.getOrgRrn(), FutureMultiProcedureTimer.class,
						Env.getMaxResult(), " timerAction = 'SHOLD' AND actionStepStateName = '" + stepState.getName() + "'", null);
				for (FutureMultiProcedureTimer pTimer : futureHoldTimers) {
					//���Process�Ƿ���ͬ
					if (pTimer.getProcessName().equals(process.getName()) 
							&& (pTimer.getParentRrn() == null || pTimer.getObjectRrn().compareTo(pTimer.getParentRrn()) == 0)) {
						if (pTimer.getProcessVersion() != null) {
							if (pTimer.getProcessVersion().equals(process.getVersion())) {
								FutureHoldTimer futureHoldTimer = new FutureHoldTimer(pTimer);
								children.add(futureHoldTimer);
							}
						} else {
							FutureHoldTimer futureHoldTimer = new FutureHoldTimer(pTimer);
							children.add(futureHoldTimer);
						}
					}
				}
				
				return children.toArray();
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		} 
		return EMPTY;
	}

	/**
	 * ���StepState���Ƿ��ж�Ӧ��Timer
	 */
	public boolean hasChildren(Object object) {
		if (object instanceof StepState) {
			StepState stepState = (StepState)object;
			try {
//				String currentPath = stepState.getParent().getName()+"/"+stepState.getName()+"/";
//				stepState.setPath(currentPath);
				LotManager lotManager = Framework.getService(LotManager.class);
				List<String> timeTypes = new ArrayList<String>();
				timeTypes.add(FutureAction.ACTION_MULTIPROCEDURETIMER);
				if (stepState.getOrgRrn() == 0) {
					stepState.setOrgRrn(Env.getOrgRrn());
				}
				//Timer��ʼλ�ü��
				List<? extends FutureTimer> startTimers = lotManager.getFutureTimerByStart(stepState, timeTypes, null);
				for (FutureTimer timer : startTimers) {
					if (timer instanceof FutureMultiProcedureTimer) {
						FutureMultiProcedureTimer pTimer = (FutureMultiProcedureTimer)timer;
						//���Process�Ƿ���ͬ
						if (pTimer.getProcessName().equals(process.getName()) && 
								(timer.getParentRrn() == null || timer.getObjectRrn().compareTo(timer.getParentRrn()) == 0)) {
							if (pTimer.getProcessVersion() != null) {
								if (pTimer.getProcessVersion().equals(process.getVersion())) {
									return true;
								}
							} else {
								return true;
							}
						}
					}
				}

				//Timer����λ�ü��
				List<? extends FutureTimer> endTimers = lotManager.getFutureTimerByEnd(stepState, timeTypes);
				for (FutureTimer timer : endTimers) {
					if (timer instanceof FutureMultiProcedureTimer) {
						FutureMultiProcedureTimer pTimer = (FutureMultiProcedureTimer)timer;
						//���Process�Ƿ���ͬ
						if (pTimer.getProcessName().equals(process.getName())) {
							if (pTimer.getProcessVersion() != null) {
								if (pTimer.getProcessVersion().equals(process.getVersion())) {
									return true;
								}
							} else {
								return true;
							}
						}
					}
				}
				
				//Timerδ����ͣλ�ü��
				ADManager adManager = Framework.getService(ADManager.class);
				List<FutureMultiProcedureTimer> futureHoldTimers = adManager.getEntityList(Env.getOrgRrn(), FutureMultiProcedureTimer.class,
						Env.getMaxResult(), " timerAction = 'SHOLD' AND actionStepStateName = '" + stepState.getName() + "'", null);
				for (FutureMultiProcedureTimer pTimer : futureHoldTimers) {
					//���Process�Ƿ���ͬ
					if (pTimer.getProcessName().equals(process.getName()) 
							&& (pTimer.getParentRrn() == null || pTimer.getObjectRrn().compareTo(pTimer.getParentRrn()) == 0)) {
						if (pTimer.getProcessVersion() != null) {
							if (pTimer.getProcessVersion().equals(process.getVersion())) {
								return true;
							}
						} else {
							return true;
						}
					}
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		}
		return false;
	}
	
	@Override
	public ImageDescriptor getImageDescriptor(Object object, String id) {
		if (object instanceof StepState) {
			StepState state = (StepState)object;
			if (state.getIsTransitionRework()) {
				return SWTResourceCache.getImageDescriptor("rework");
			}
			return SWTResourceCache.getImageDescriptor("step");
		} else if (object instanceof FutureMultiProcedureTimer) {
			return SWTResourceCache.getImageDescriptor("timer_start");
		} else if (object instanceof EndTimer) {
			return SWTResourceCache.getImageDescriptor("timer_end");
		} else if (object instanceof FutureHoldTimer) {
			return SWTResourceCache.getImageDescriptor("hold");
		}
		return null;
	}

	@Override
	public String getText(Object element) {
		if (element instanceof StepState) {
			StepState stepState = (StepState) element;
			Step step = getStep(stepState);
			if (step.getDescription() != null) {
				return step.getName() + "<" + step.getDescription() + ">";
			}
			return step.getName();
		} else if (element instanceof FutureMultiProcedureTimer) {
			FutureMultiProcedureTimer timer = (FutureMultiProcedureTimer) element;
			String description = (timer.getDescription() == null
					|| "".equals(timer.getDescription().trim()) ? "" : " <" + timer.getDescription() + ">");
			return timer.getName() + description;
		} else if (element instanceof EndTimer) {
			EndTimer endTime = (EndTimer)element;
			String description = (endTime.getDescription() == null
					|| "".equals(endTime.getDescription().trim()) ? "" : " <" + endTime.getDescription() + ">");
			return ((EndTimer) element).getTimerName() + description;
		} else if (element instanceof FutureHoldTimer) {
			FutureHoldTimer futureHoldTime = (FutureHoldTimer)element;
			String description = (futureHoldTime.getDescription() == null
					|| "".equals(futureHoldTime.getDescription().trim()) ? "" : " <" + futureHoldTime.getDescription() + ">");
			return ((FutureHoldTimer) element).getTimerName() + description;
		}
		return "";
	}

	public Process getProcess() {
		return process;
	}

	public void setProcess(Process process) {
		this.process = process;
	}
	
}

/**
 * ��������Timer�Ľ����ڵ�
 */
class EndTimer {
	
	private long timerRrn;
	private String timerName;
	private String endStepStateName;
	private String description;
	
	public EndTimer(FutureMultiProcedureTimer pTimer) {
		this.timerRrn = pTimer.getObjectRrn();
		this.timerName = pTimer.getName();
		this.description = pTimer.getDescription();
		this.endStepStateName = pTimer.getEndStepStateName();
	}
	
	public long getTimerRrn() {
		return timerRrn;
	}
	
	public void setTimerRrn(long timerRrn) {
		this.timerRrn = timerRrn;
	}
	
	public String getTimerName() {
		return timerName;
	}
	
	public void setTimerName(String timerName) {
		this.timerName = timerName;
	}
	
	public String getEndStepStateName() {
		return endStepStateName;
	}
	
	public void setEndStepStateName(String endStepStateName) {
		this.endStepStateName = endStepStateName;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
}


/**
 * ��������Timer��δ����ͣ�ڵ�
 */
class FutureHoldTimer {
	
	private long timerRrn;
	private String timerName;
	private String actionStepStateName;
	private String description;
	
	public FutureHoldTimer(FutureMultiProcedureTimer pTimer) {
		this.timerRrn = pTimer.getObjectRrn();
		this.timerName = pTimer.getName();
		this.description = pTimer.getDescription();
		this.actionStepStateName = pTimer.getActionStepStateName();
	}
	
	public long getTimerRrn() {
		return timerRrn;
	}
	
	public void setTimerRrn(long timerRrn) {
		this.timerRrn = timerRrn;
	}
	
	public String getTimerName() {
		return timerName;
	}
	
	public void setTimerName(String timerName) {
		this.timerName = timerName;
	}
	
	public String getActionStepStateName() {
		return actionStepStateName;
	}

	public void setActionStepStateName(String actionStepStateName) {
		this.actionStepStateName = actionStepStateName;
	}

	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
}