package com.glory.mes.wip.advance.future.timer.multitimer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.procedure.flow.ProcedureFlowTreeField;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.advance.future.timer.multiproc.MultiProcedureTreeField.MultiReworkEndStepStateAction;
import com.glory.mes.wip.advance.future.timer.multiproc.MultiProcedureTreeField.MultiReworkStartStepStateAction;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.google.common.collect.Lists;

public class MultiTimerTreeField extends ProcedureFlowTreeField {

	private static final Logger logger = Logger.getLogger(MultiTimerTreeField.class);
	private static final String TABLE_NAME = "WIPADVMultiStepChildTimer";

	public MultiTimerSection lotSection;
	protected List<ADBase> currentFlowList;
	
	public MultiTimerTreeField(String id, String label, TreeViewerManager manager) {
		super(id, label, manager);
	}

	/**
	 * ��ʼ���Ҽ��˵�
	 */
	protected void initMenu() {
		MenuManager mgr = new MenuManager();
		
		Action startAction = new MultiStartStepStateAction();
		startAction.setText("Start");
		startAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("startstep")));
		
		Action reworkStartAction = new MultiReworkStartStepStateAction();
		reworkStartAction.setText("StartByRework");
		reworkStartAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("startstep")));
		
		Action endAction = new MultiEndStepStateAction();
		endAction.setText("End");
		endAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("endstep")));
		
		Action reworkEndAction = new MultiReworkEndStepStateAction();
		reworkEndAction.setText("EndByRework");
		reworkEndAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("endstep")));
		
		Action addEndAction = new AddEndStepStateAction();
		addEndAction.setText("AddEnd");
		addEndAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("endstep")));

		Action actionStep = new MultiAddFutureStepStateAction();
		actionStep.setText("ActionStep");
		actionStep.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("hold")));
		
		mgr.add(startAction);
		mgr.add(reworkStartAction);
		mgr.add(endAction);
		mgr.add(reworkEndAction);
		mgr.add(addEndAction);
		mgr.add(actionStep);

		menu = mgr.createContextMenu(tree);
	}
	
	@Override
	protected void doSelect(MouseEvent e) {
		if (e.button == 3) {
			//����Ҽ�
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			if (treeItem != null) {
				IStructuredSelection selection = (IStructuredSelection)viewer.getSelection();
				Object obj = selection.getFirstElement();
				if (obj.equals(treeItem.getData())) {// ����һ�����ѡ�еĽڵ�
					this.currentSelectedItem = treeItem;
					if (obj instanceof StepState) {
						//���ѡ�е���StepState�ڵ�
						//��ʾ�Ҽ��˵�
						tree.setMenu(menu);
						lotSection.itemTimer.setEnabled(true);	
					} else {
						//����ȡ���Ҽ��˵�
						tree.setMenu(null);
					}
				}
			} else {
				tree.setMenu(null);
			}
		} else if (e.button == 1) {
			//������
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			FutureAction currentTimer = (FutureAction)lotSection.getAdObject();
			if (treeItem != null) {
				IStructuredSelection selection = (IStructuredSelection)viewer.getSelection();
				Object obj = selection.getFirstElement();
				if (obj.equals(treeItem.getData())) {// ����һ�����ѡ�еĽڵ�
					if (obj instanceof StepState) {
						//���ѡ�е���StepState�ڵ�
						lotSection.itemTimer.setEnabled(true);
						if (currentTimer.getObjectRrn() != null) {
							lotSection.setAdObject(new FutureMultiStepTimer());
							lotSection.refresh();
						}
					} else if( treeItem.getData() instanceof FutureMultiStepTimer){
						//���ѡ�е���FutureMultiStepTimer�ڵ�
						//����CurrentStepState��Timer��Ϣ
						lotSection.itemTimer.setEnabled(true);
						FutureMultiStepTimer timer = (FutureMultiStepTimer) treeItem.getData();
//						Object o = treeItem.getParentItem().getData();
//						if (o instanceof StepState) {
//							StepState step = ((StepState)o);
//							if (lotSection != null) {
//								lotSection.setCurrentStepState(step);
//							}
//						}
						lotSection.setAdObject(timer);
						lotSection.refresh();
					} else if (treeItem.getData() instanceof EndTimer){
						//���ѡ�е���EndTimer�ڵ�
						//����CurrentStepState��Timer��Ϣ
						EndTimer timer = (EndTimer)treeItem.getData();
						long rrn = timer.getTimerRrn();
						FutureMultiStepTimer fmst = new FutureMultiStepTimer();
						fmst.setObjectRrn(rrn);
						try {
							//����Timer��ObjectRrn��ö�Ӧ��Timer
							ADManager em = Framework.getService(ADManager.class);
							fmst=(FutureMultiStepTimer) em.getEntity(fmst);
							lotSection.setAdObject(fmst);
							lotSection.refresh();
						} catch (Exception e1) {
							e1.printStackTrace();
						}
					} else if (treeItem.getData() instanceof FutureHoldTimer){
						//���ѡ�е���FutureHoldTimer�ڵ�
						//����CurrentStepState��Timer��Ϣ
						FutureHoldTimer timer = (FutureHoldTimer)treeItem.getData();
						long rrn = timer.getTimerRrn();
						FutureMultiStepTimer fmst = new FutureMultiStepTimer();
						fmst.setObjectRrn(rrn);
						try {
							//����Timer��ObjectRrn��ö�Ӧ��Timer
							ADManager em = Framework.getService(ADManager.class);
							fmst=(FutureMultiStepTimer) em.getEntity(fmst);
							lotSection.setAdObject(fmst);
							lotSection.refresh();
						} catch (Exception e1) {
							e1.printStackTrace();
						}
					} else {
						lotSection.itemTimer.setEnabled(false);
						if (currentTimer.getObjectRrn() != null) {
							lotSection.setAdObject(new FutureMultiStepTimer());
						}
						lotSection.refresh();
					}
				} else {
					lotSection.itemTimer.setEnabled(false);
					if (currentTimer.getObjectRrn() != null) {
						lotSection.setAdObject(new FutureMultiStepTimer());
					}
					lotSection.refresh();
				}
			}
		}
	}
	
	public class MultiStartStepStateAction extends StartStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState startStepState = getStepStart();
			FutureMultiStepTimer futureTimer = new FutureMultiStepTimer();
			if (startStepState != null) {
				futureTimer.setCreated(new Date());
				futureTimer.setOrgRrn(Env.getOrgRrn());
				futureTimer.setStepName(startStepState.getStepName());
				futureTimer.setStepStateName(startStepState.getName());
				futureTimer.setProcedureName(startStepState.getProcessDefinition().getName());
				if (futureTimer.getIsAll()) {
					futureTimer.setProcedureVersion(null);
				} else {
					futureTimer.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
				}
				lotSection.setAdObject(futureTimer);
				lotSection.refresh();
			}
		}
	}
	
	public class MultiReworkStartStepStateAction extends ReworkStartStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState startStepState = (StepState) reworkStepStates[0];
			FutureMultiStepTimer futureTimer = new FutureMultiStepTimer();
			if (startStepState != null) {
				futureTimer.setCreated(new Date());
				futureTimer.setOrgRrn(Env.getOrgRrn());
				futureTimer.setStepName(startStepState.getStepName());
				futureTimer.setStepStateName(startStepState.getName());
				futureTimer.setProcedureName(startStepState.getProcessDefinition().getName());
				if (futureTimer.getIsAll()) {
					futureTimer.setProcedureVersion(null);
				} else {
					futureTimer.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
				}
				lotSection.setAdObject(futureTimer);
				lotSection.refresh();
			}
		}
	}
	
	public class MultiEndStepStateAction extends EndStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState endStepState = getStepEnd();
			FutureMultiStepTimer futureTimer = (FutureMultiStepTimer) lotSection.getAdObject();
			if (endStepState != null) {
				futureTimer.setEndStepName(endStepState.getStepName());
				futureTimer.setEndStepStateName(endStepState.getName());
				for (IForm detailForm : lotSection.getDetailForms()) {
					((Form)detailForm).setFieldValue("endStepName", endStepState.getStepName());
				}
				lotSection.setAdObject(futureTimer);
			}
		}
	}
	
	public class MultiReworkEndStepStateAction extends ReworkEndStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState endStepState = (StepState) reworkStepStates[1];
			FutureMultiStepTimer futureTimer = (FutureMultiStepTimer) lotSection.getAdObject();
			if (endStepState != null) {
				futureTimer.setEndStepName(endStepState.getStepName());
				futureTimer.setEndStepStateName(endStepState.getName());
				for (IForm detailForm : lotSection.getDetailForms()) {
					((Form)detailForm).setFieldValue("endStepName", endStepState.getStepName());
				}
				lotSection.setAdObject(futureTimer);
			}
		}
	}
	
	public class AddEndStepStateAction extends Action {
		@Override
		public void run() {
			try {
				// ���п�ʼ�����ڵ��������ӽ����ڵ�
				if (endStepStateItem == null || startStepStateItem == null
						|| startStepStateItem.equals(currentSelectedItem)) {
					UI.showError(Message.getString("wip.prd_start_end_is_null"));
					return;
				}
				FutureMultiStepTimer futureTimer = (FutureMultiStepTimer) lotSection.getAdObject();
				for (IForm detailForm : lotSection.getDetailForms()) {
					detailForm.saveToObject();
					PropertyUtil.copyProperties(futureTimer, detailForm
							.getObject(), detailForm.getCopyProperties());
				}
				FutureMultiStepTimer addEndTimer = (FutureMultiStepTimer) futureTimer.clone();
				StepState stepState = (StepState) currentSelectedItem.getData();
				List<FutureAction> childFutureActions = futureTimer.getChildFutureActions() != null ? futureTimer.getChildFutureActions() : Lists.newArrayList();
				if (!childFutureActions.isEmpty()) {
					for (FutureAction action : futureTimer.getChildFutureActions()) {
						FutureMultiStepTimer childFutureTimer = (FutureMultiStepTimer) action;
						if (childFutureTimer.getEndStepStateName().equals(stepState.getName())) {
							addEndTimer = childFutureTimer;
							break;
						}
					}
				}
				addEndTimer.setEndStepName(stepState.getStepName());
				addEndTimer.setEndStepStateName(stepState.getName());
				
				ADManager adManager = (ADManager) Framework.getService(ADManager.class);
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
				MultiTimerAddEndEntityDialog dialog = new MultiTimerAddEndEntityDialog(adTable, addEndTimer);
				if (dialog.open() == Dialog.OK) {
					// ����ԭ��startEnd
					if (startEndStepItem != null) {
						if (!startEndStepItem.isDisposed()) {
							startEndStepItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
						}
						startEndStepItem = null;
					}

					// ���õ�ǰ��ӽ����ڵ���ɫ
					currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_RED));
					addEndStepStateItems.put(currentSelectedItem.getText(), currentSelectedItem);
					childFutureActions.add((FutureMultiStepTimer)dialog.getAdObject());
					futureTimer.setChildFutureActions(childFutureActions);
					for (IForm detailForm : lotSection.getDetailForms()) {
						((Form)detailForm).setFieldValue("childFutureActions", childFutureActions);
					}
					lotSection.setAdObject(futureTimer);
	            }
			} catch (Exception e) {
	            ExceptionHandlerManager.asyncHandleException(e);
	            return;
	        }
		}
	}
	
	public class MultiAddFutureStepStateAction extends AddFutureStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState actionStepState = getActionStep();
			FutureMultiStepTimer futureTimer = (FutureMultiStepTimer) lotSection.getAdObject();
			if (actionStepState != null) {
				futureTimer.setTimerAction(FutureTimer.ACTION_STEP_HOLD);
				futureTimer.setActionPath(actionStepState.getPath());
				futureTimer.setActionStepName(actionStepState.getStepName());
				futureTimer.setActionStepStateName(actionStepState.getName());
				List<FutureAction> childFutureActions = futureTimer.getChildFutureActions();
				if (childFutureActions != null && !childFutureActions.isEmpty()) {
					for (FutureAction action : futureTimer.getChildFutureActions()) {
						FutureMultiStepTimer childFutureTimer = (FutureMultiStepTimer) action;
						childFutureTimer.setActionPath(futureTimer.getActionPath());
						childFutureTimer.setActionStepName(futureTimer.getActionStepName());
						childFutureTimer.setActionStepStateName(futureTimer.getActionStepStateName());
					}
				}
				for (IForm detailForm : lotSection.getDetailForms()) {
					((Form)detailForm).setFieldValue("actionStepName", actionStepState.getStepName());
					((Form)detailForm).setFieldValue("childFutureActions", childFutureActions);
				}
				lotSection.setAdObject(futureTimer);
			}
		}
	}
	
	public void loadFlowTreeByProcedure(Procedure procedure, String nodeName ,List<FutureMultiStepTimer> futureMultiStepTimers) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);		
			if (procedure != null) {

				List<Procedure> procedures = new ArrayList<Procedure>();
				procedure = (Procedure) prdManager.getProcessDefinition(procedure);
				procedures.add(procedure);
				manager.setInput(procedures);
			
				if (nodeName != null) {
					List<ADBase> processes = new ArrayList<ADBase>();
					processes.add(procedure);
					
					PrdQueryAction queryAction = PrdQueryAction.newIntance();
					queryAction.setCopyNode(true);
					List<Node> nodes = prdManager.getProcessDefinitionChildern(procedure, queryAction);
					nodes = nodes.stream().filter(s -> s.getName().equals(nodeName)).collect(Collectors.toList());
					processes.addAll(nodes);
					//��Qtime�ڵ�
					if (CollectionUtils.isNotEmpty(futureMultiStepTimers)) {
						processes.addAll(futureMultiStepTimers);
					}		
					currentFlowList = (List)processes;
					setCurrentFlow(currentFlowList);	
				} else {
					viewer.expandToLevel(3);
				}
			} else {
				manager.setInput(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ���õ�ǰ�ڵ�
	 */
	public void setCurrentFlow(List<ADBase> currentFlowList) {
		if (currentFlowList != null && currentFlowList.size() > 0) {
			TreeSelection section = new TreeSelection(new TreePath(currentFlowList.toArray()));
			viewer.setSelection(section);

//			TreeItem[] items = tree.getSelection();
//			for (TreeItem item : items) {
//				item.setImage(SWTResourceCache.getImage("currentstep"));
//				while (item.getParentItem() != null) {
//					item = item.getParentItem();
//				}
//			}
		}
	}
}
