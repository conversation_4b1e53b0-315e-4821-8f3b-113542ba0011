package com.glory.mes.wip.advance.future.procedurehold;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.mes.prd.procedure.flow.ProcedureFlowTreeField;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.future.FutureHold;

public class ProcedureHoldFlowTreeField extends ProcedureFlowTreeField {

	private static final Logger logger = Logger.getLogger(ProcedureHoldFlowTreeField.class);

	ProcedureHoldSection lotSection;
	
	public ProcedureHoldFlowTreeField(String id, String label,
			TreeViewerManager manager) {
		super(id, label, manager);
	}

	@Override
	protected void doSelect(MouseEvent e) {
		if (e.button == 1) {
			// ��굥�����
			// ����Ƿ�㵽�˾���Ľڵ�
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			// û�е㵽������
			if (treeItem != null) {
				// ѡ����ĳһ�ڵ�
				// selection��TreeItem��һ�µģ�����TreeViewer���д���
				IStructuredSelection selection = (IStructuredSelection)viewer.getSelection();
				Object obj = selection.getFirstElement();
				if (obj.equals(treeItem.getData())) {
					if (obj instanceof StepState) {
						lotSection.itemNote.setEnabled(true);
						lotSection.initAdObject();
						lotSection.setCurrentStepState((StepState)obj);
						lotSection.refresh();
					} else if (treeItem.getData() instanceof FutureHold) {
						lotSection.itemNote.setEnabled(true);
						FutureHold hold = (FutureHold) treeItem.getData();
						Object o = treeItem.getParentItem().getData();
						if (o instanceof StepState) {
							StepState step = ((StepState) o);
							if (lotSection != null) {
								lotSection.setCurrentStepState(step);
							}
						}
						lotSection.setAdObject(hold);
						lotSection.refresh();
					} else {
						lotSection.itemNote.setEnabled(false);
						lotSection.refresh();
					}
				}
			} 
		}
	}

	
}
