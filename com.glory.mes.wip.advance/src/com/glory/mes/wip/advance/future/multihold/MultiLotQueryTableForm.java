package com.glory.mes.wip.advance.future.multihold;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Message;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.FormLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiLotQueryTableForm extends QueryTableForm {

	public MultiLotQueryTableForm(Composite parent, int style, ADTable table, IMessageManager mmng,CheckBoxTableViewerManager tableManager) {
		super(parent, style, table, mmng);
		Composite resultComp = new Composite(this, SWT.NONE);
		GridLayout layout = new GridLayout();
	    layout.verticalSpacing = 0;
	    layout.marginHeight = 0;
	    resultComp.setLayout(layout);
	    resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
	    
	    createNewViewer(resultComp,tableManager);
	}
	
	public void createForm() {
	    this.toolkit = new FormToolkit(getDisplay());
	    GridLayout layout = new GridLayout();
	    layout.verticalSpacing = 0;
	    layout.horizontalSpacing = 0;
	    layout.marginWidth = 0;
	    layout.marginHeight = 0;
	    setLayout(new GridLayout(1, true));

	    Composite queryComp = new Composite(this, 0);
	    GridData gd = new GridData(768);
	    queryComp.setLayoutData(gd);

	    FormLayout formLayout = new FormLayout();
	    formLayout.marginHeight = 0;
	    formLayout.marginWidth = 10;
	    queryComp.setLayout(formLayout);

	    this.queryForm = createQueryFrom(queryComp);
	    addFields();

	    SquareButton btnQuery = UIControlsFactory.createButton(queryComp, "Default");
	    btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));

	    FormData fd = new FormData();
	    fd.bottom = new FormAttachment(this.queryForm, -20, 1024);
	    fd.left = new FormAttachment(this.queryForm, 20, 131072);
	    btnQuery.setLayoutData(fd);
	    btnQuery.addSelectionListener(new SelectionAdapter() {
		    public void widgetSelected(SelectionEvent event) {
		        queryAdapter();
		    }
	    });
	  }

	protected void createNewViewer(Composite client,CheckBoxTableViewerManager tableManagerpare) {
		tableManagerpare.setIndexFlag(true);
		tableManagerpare.newViewer(client);
		
		setTableManager(tableManagerpare);
	}
	  
}
