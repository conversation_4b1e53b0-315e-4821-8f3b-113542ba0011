package com.glory.mes.wip.advance.future.step;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.model.Lot;

public class FutureStepAdapter extends ListItemAdapter {
	
	@Override
	public Color getBackground(Object element, String id) {
		Lot lot = (Lot) element;
		if("Y".equals(lot.getAttribute1())) {
			return SWTResourceCache.getColor(SWTResourceCache.COLOR_GREEN);
		}
		return super.getBackground(element, id);
	}
	
	@Override
	public String getText(Object element) {
		return super.getText(element);
	}
	
	@Override
	public Font getFont(Object object) {
		return super.getFont(object);
	}
	
	@Override
	public Color getColor(Object object) {
		return super.getColor(object);
	}
}
