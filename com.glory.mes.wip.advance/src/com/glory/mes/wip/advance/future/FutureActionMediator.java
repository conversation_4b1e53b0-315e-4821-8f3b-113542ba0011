package com.glory.mes.wip.advance.future;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.future.FutureChangeFlow;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureNewPart;
import com.glory.mes.wip.future.FutureNewProcedure;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.model.Lot;

public class FutureActionMediator extends LotMediator {
	private static final Logger logger = Logger.getLogger(FutureActionMediator.class);
	
	public static final String REFRESH = "Refresh";
	public static final String DELETE = "Delete";
	
	protected StepState currentStepState;
	protected TreeViewer viewer;
	private Lot currentLot;

	public FutureActionMediator(LotSection lotSection) {
		super(lotSection);
	}
	
	@Override
	protected void notifyLotFlowTreeFieldAdapter(String action, ADBase adBase) {
		if(REFRESH.equals(action)) {
			if(currentStepState != null && viewer != null) {
				treeField.refreshViewerNode(currentStepState);
			}
		} else if(DELETE.equals(action)) {
			if(adBase != null) {
				treeField.removeLeafNode(currentStepState, adBase);
			}
		}
	}
	
	@Override
	protected void notifySectionAdapter(ADBase adBase) {
		Lot lot = (Lot)adBase;
		if(adBase instanceof Lot) {
			if(lotSection instanceof LotFutureActionSection) {
				((LotFutureActionSection)lotSection).setCurrentLot(lot);
			}
		}
		if(currentLot == null || !currentLot.equals(lot)) {
			currentLot = lot;
		}
	}
	
	@Override
	protected void selectionChangedAdapter(SelectionEvent e) {
		TreeItem[] items = getTreeViewer().getTree().getSelection();		
		if(getTreeViewer() != null) {
			resetChangedContent(items);
		}
	}
	
	private void resetChangedContent(TreeItem[] items) {
		if(items != null && items.length > 0) {
			TreeItem item = items[0];
			lotSection.initAdObject();
		
			if(item.getData() instanceof StepState) {
				StepState ss = currentStepState = ((StepState)item.getData());
				((LotFutureActionSection)lotSection).setCurrentStepState(ss);	
				((LotFutureActionSection)lotSection).statusChanged("StepState");
			} else if(item.getData() instanceof FutureHold) {
				FutureHold hold = (FutureHold)item.getData();
				Object obj = item.getParentItem().getData();
				if(obj instanceof StepState) {
					currentStepState = ((StepState)obj);
					if(lotSection != null) {
						((LotFutureActionSection)lotSection)
							.setCurrentStepState(((StepState)obj));
					}
					((LotFutureActionSection)lotSection).setAdObject(hold);
					((LotFutureActionSection)lotSection).statusChanged("FutureHold");
					lotSection.refresh();
				}
			} else if(item.getData() instanceof FutureNote){
				FutureNote note = (FutureNote)item.getData();
				Object obj = item.getParentItem().getData();
				if(obj instanceof StepState) {
					currentStepState = ((StepState)obj);
					if(lotSection != null) {
						((LotFutureActionSection)lotSection)
							.setCurrentStepState(((StepState)obj));
					}
				}
				((LotFutureActionSection)lotSection).setAdObject(note);
				((LotFutureActionSection)lotSection).statusChanged("FutureNote");
				lotSection.refresh();
			} else if (item.getData() instanceof FutureChangeFlow ||
					item.getData() instanceof FutureNewPart ||
					item.getData() instanceof FutureNewProcedure) {
				Object obj = item.getParentItem().getData();
				if(obj instanceof StepState) {
					currentStepState = ((StepState)obj);
					if(lotSection != null) {
						((LotFutureActionSection)lotSection)
							.setCurrentStepState(((StepState)obj));
					}
				}
				((LotFutureActionSection)lotSection).setAdObject((ADBase) item.getData());
				((LotFutureActionSection)lotSection).statusChanged("FutureFlowAction");
				lotSection.refresh();
			} else {
				((LotFutureActionSection)lotSection).initAdObject();
				((LotFutureActionSection)lotSection).setCurrentStepState(null);
				((LotFutureActionSection)lotSection).statusChanged("");
			}
		}
	}
	
	private void canAddFutureAction(TreeItem[] items) {
	}

	protected TreeViewer getTreeViewer() {
		if(viewer == null) {
			if(treeField != null) {
				viewer = treeField.getTreeViwer();
				return viewer;
			} 
		} else {
			return viewer;
		}
		return null;
	}
}
