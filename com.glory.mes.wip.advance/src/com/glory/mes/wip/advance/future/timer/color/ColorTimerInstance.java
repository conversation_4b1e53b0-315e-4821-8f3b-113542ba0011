package com.glory.mes.wip.advance.future.timer.color;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.graphics.Color;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class ColorTimerInstance {
	
	private static String ADREFLIST_NAME = "WIPFutureTimeInstanceColor";
	
	private static List<ADRefList> adRefLists;
	private static boolean flag = true;
	
	public static Color calculateTimeDiff (Long time) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			if (flag && CollectionUtils.isEmpty(adRefLists)) {
				adRefLists =  adManager.getADRefList(Env.getOrgRrn(), ADREFLIST_NAME);
				if (CollectionUtils.isEmpty(adRefLists)) {
					flag = false;
				}
			} else {
				flag = false;
			}
			adRefLists = adRefLists.stream().sorted(Comparator.comparing(ADRefList::getSeqNo)).collect(Collectors.toList());
			for(ADRefList adRefList : adRefLists) {
				long judTime = Long.parseLong(adRefList.getKey());
				if (time <= judTime*60*1000) {
					return load(adRefList.getText());
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return null;
	}

	public static Color load(String color){
		try {
			if(color != null && color.trim().length() > 0){
				String[] rgbStr = color.split(",");
				int[] rgb = new int[rgbStr.length];
				for (int i  =0; i < rgbStr.length; i++){
					rgb[i] = Integer.parseInt(rgbStr[i]);
				}
				
				Color col = new Color(null, rgb[0], rgb[1], rgb[2]);
				return col;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
        }
		return null;
	}

	public List<ADRefList> getAdRefLists() {
		return adRefLists;
	}

	public void setAdRefLists(List<ADRefList> adRefLists) {
		this.adRefLists = adRefLists;
	}
	
}
