package com.glory.mes.wip.advance.future.timer.color;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class ColorTimerInstanceQuerySection extends QueryEntityListSection{
	
	private static String ADREFLIST_NAME = "WIPFutureTimeInstanceColor";

	public ColorTimerInstanceQuerySection(ListTableManager tableManager) {
		super(tableManager);
	}
	
	@Override
	public void createContents(IManagedForm form, Composite parent, int sectionStyle) {
		super.createContents(form, parent, sectionStyle);
		try {
			final FormToolkit toolkit = form.getToolkit();
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADRefList> adRefLists =  adManager.getADRefList(Env.getOrgRrn(), ADREFLIST_NAME);
			if (!CollectionUtils.isEmpty(adRefLists)) {
				GridData gd = new GridData(GridData.FILL_HORIZONTAL);
				gd.verticalAlignment = SWT.RIGHT;
				Composite colorComposite = toolkit.createComposite(parent);
				colorComposite.setLayout(new GridLayout(20, false));
				colorComposite.setLayoutData(gd);
				GridData gText = new GridData();
				gText.widthHint = 66;
				gText.heightHint = 25;
				Label labelTitle = toolkit.createLabel(colorComposite, Message.getString("wip.color_example"));
				String calCulate = null;
				for (ADRefList adRefList : adRefLists) {
					String[] color = adRefList.getText().split(",");
					
					Text text = new Text(colorComposite, SWT.BORDER);
					text.setLayoutData(gText);
					text.setBackground(new Color(Display.getCurrent(), Integer.parseInt(color[0]), Integer.parseInt(color[1]), Integer.parseInt(color[2])));
					text.setEnabled(false);
					if (StringUtil.isEmpty(calCulate)) {
						Label label = toolkit.createLabel(colorComposite, Message.getString("wip.expire_date")+ "<="+adRefList.getKey()+"min");
					} else {
						Label label = toolkit.createLabel(colorComposite, calCulate+"min" +"<" +Message.getString("wip.expire_date")+ "<="+adRefList.getKey()+"min");
					}
					calCulate = adRefList.getKey();
				}
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
