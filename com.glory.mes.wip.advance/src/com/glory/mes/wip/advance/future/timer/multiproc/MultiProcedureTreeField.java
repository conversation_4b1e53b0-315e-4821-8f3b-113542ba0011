package com.glory.mes.wip.advance.future.timer.multiproc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.MenuManager;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.jface.viewers.IStructuredSelection;
import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.TreeItem;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.process.flow.ProcessFlowSection;
import com.glory.mes.prd.process.flow.ProcessFlowTreeField;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.action.PrdQueryAction;
import com.glory.mes.wip.advance.future.timer.multitimer.MultiTimerAddEndEntityDialog;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.google.common.collect.Lists;

public class MultiProcedureTreeField extends ProcessFlowTreeField {

	private static final Logger logger = Logger.getLogger(MultiProcedureTreeField.class);
	private static final String TABLE_NAME = "WIPADVMultiStepChildTimer";

	public MultiProcedureSection section;
	public ProcessFlowSection processSection;
	public TreeItem currentSelectedTimerItem;
	protected List<ADBase> currentFlowList;
	
	public MultiProcedureTreeField(String id, String label, TreeViewerManager manager) {
		super(id, label, manager);
	}

	/**
	 * ��ʼ���Ҽ��˵�
	 */
	protected void initMenu() {
		MenuManager mgr = new MenuManager();
		
		Action startAction = new MultiStartStepStateAction();
		startAction.setText("Start");
		startAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("startstep")));

		Action reworkStartAction = new MultiReworkStartStepStateAction();
		reworkStartAction.setText("StartByRework");
		reworkStartAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("startstep")));
		
		Action endAction = new MultiEndStepStateAction();
		endAction.setText("End");
		endAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("endstep")));

		Action reworkEndAction = new MultiReworkEndStepStateAction();
		reworkEndAction.setText("EndByRework");
		reworkEndAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("endstep")));
		
		Action addEndAction = new AddEndStepStateAction();
		addEndAction.setText("AddEnd");
		addEndAction.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("endstep")));
		
		Action actionStep = new MultiAddFutureStepStateAction();
		actionStep.setText("ActionStep");
		actionStep.setImageDescriptor(ImageDescriptor.createFromImage(SWTResourceCache.getImage("hold")));
		
		mgr.add(startAction);
		mgr.add(reworkStartAction);
		mgr.add(endAction);
		mgr.add(reworkEndAction);
		mgr.add(addEndAction);
		mgr.add(actionStep);

		menu = mgr.createContextMenu(tree);
	}
	
	@Override
	protected void doSelect(MouseEvent e) {
		if (e.button == 1) {
			//������
			TreeItem treeItem = tree.getItem(new Point(e.x, e.y));
			FutureAction currentTimer = (FutureAction)section.getAdObject();
			
			if (treeItem != null) {
				IStructuredSelection selection = (IStructuredSelection)viewer.getSelection();
				Object obj = selection.getFirstElement();
				if (obj.equals(treeItem.getData())) {// ����һ�����ѡ�еĽڵ�
					if (obj instanceof StepState) {
						//���ѡ�е���StepState�ڵ�
						if (currentTimer.getObjectRrn() != null) {
							section.setAdObject(new FutureMultiProcedureTimer());
							section.refresh();
						}
					} else if(treeItem.getData() instanceof FutureMultiProcedureTimer){
						//���ѡ�е���FutureMultiProcedureTimer�ڵ�
						//����CurrentStepState��Timer��Ϣ
						currentSelectedTimerItem = treeItem;
						
						FutureMultiProcedureTimer timer = (FutureMultiProcedureTimer)treeItem.getData();
						section.setAdObject(timer);
						section.refresh();
					} else if (treeItem.getData() instanceof EndTimer){
						//���ѡ�е���EndTimer�ڵ�
						//����CurrentStepState��Timer��Ϣ
						currentSelectedTimerItem = treeItem;
						
						EndTimer timer = (EndTimer)treeItem.getData();
						long rrn = timer.getTimerRrn();
						FutureMultiProcedureTimer fmst = new FutureMultiProcedureTimer();
						fmst.setObjectRrn(rrn);
						try {
							//����Timer��ObjectRrn��ö�Ӧ��Timer
							ADManager em = Framework.getService(ADManager.class);
							fmst = (FutureMultiProcedureTimer)em.getEntity(fmst);
							section.setAdObject(fmst);
							section.refresh();
						} catch (Exception e1) {
							e1.printStackTrace();
						}
					} else if (treeItem.getData() instanceof FutureHoldTimer){
						//���ѡ�е���FutureHoldTimer�ڵ�
						//����CurrentStepState��Timer��Ϣ
						currentSelectedTimerItem = treeItem;
						
						FutureHoldTimer timer = (FutureHoldTimer)treeItem.getData();
						long rrn = timer.getTimerRrn();
						FutureMultiProcedureTimer fmst = new FutureMultiProcedureTimer();
						fmst.setObjectRrn(rrn);
						try {
							//����Timer��ObjectRrn��ö�Ӧ��Timer
							ADManager em = Framework.getService(ADManager.class);
							fmst = (FutureMultiProcedureTimer)em.getEntity(fmst);
							section.setAdObject(fmst);
							section.refresh();
						} catch (Exception e1) {
							e1.printStackTrace();
						}
					} else {
						if (currentTimer.getObjectRrn() != null) {
							section.setAdObject(new FutureMultiProcedureTimer());
						}
						section.refresh();
					}
				} else {
					if (currentTimer.getObjectRrn() != null) {
						section.setAdObject(new FutureMultiProcedureTimer());
					}
					section.refresh();
				}
			}
		} else {
			super.doSelect(e);
		}
	}
	
	public void refreshProcedure() {
		Object[] objs = this.getTreeViewer().getExpandedElements();
		for (Object obj : objs) {
			if (obj instanceof ProcedureState) {
				this.getTreeViewer().refresh(obj);
			}
		} 
	}
	
//	public void addTimer(FutureMultiProcedureTimer timer) {
//		
//		if (startStepStateItem != null) {
//			TreePath path = this.getTreePath(startStepStateItem);		
//			this.getTreeViewer().getExpandedElements()
//			dElements().getadd(path, timer);
//		}
//		if (endStepStateItem != null) {
//			EndTimer endTimer = new EndTimer(timer);
//			TreePath path = this.getTreePath(endStepStateItem);		
//			this.getTreeViewer().add(path, endTimer);
//		}
//	}
//	
//	public void deleteTimer(FutureMultiProcedureTimer timer) {
//		if (currentSelectedTimerItem != null) {
//			if (currentSelectedTimerItem.getData() instanceof FutureMultiProcedureTimer) {
//				TreePath path = this.getTreePath(currentSelectedTimerItem);
//				this.getTreeViewer().remove(path);
//				
//				//
//			}
//		}
//	}
	
	@Override
	public void refresh() {
		TreeViewerManager manager = this.getTreeManager();
		MultiProcedureItemAdapter adapter = 
				(MultiProcedureItemAdapter)manager.adapterFactory.getAdapter(StepState.class);
		adapter.setProcess(this.getFlowSection().getProcess());
		super.refresh();
	}
	
	public void refresh(com.glory.mes.prd.model.Process process) {
		TreeViewerManager manager = this.getTreeManager();
		MultiProcedureItemAdapter adapter = 
				(MultiProcedureItemAdapter)manager.adapterFactory.getAdapter(StepState.class);
		adapter.setProcess(process);
	}

	public class MultiStartStepStateAction extends StartStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState startStepState = getStepStart();
			FutureMultiProcedureTimer futureTimer = new FutureMultiProcedureTimer();
			if (startStepState != null) {
				futureTimer.setCreated(new Date());
				futureTimer.setOrgRrn(Env.getOrgRrn());
				futureTimer.setProcessName(processSection.getProcess().getName());
				if (futureTimer.getIsAll()) {
					futureTimer.setProcessVersion(null);
				} else {
					futureTimer.setProcessVersion(processSection.getProcess().getVersion());
				}
				futureTimer.setPath(startStepState.getPath());
				futureTimer.setProcedureName(startStepState.getProcessDefinition().getName());
				futureTimer.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
				futureTimer.setStepName(startStepState.getStepName());
				futureTimer.setStepStateName(startStepState.getName());
				
				section.setAdObject(futureTimer);
				section.refresh();
			}
		}
	}
	
	public class MultiReworkStartStepStateAction extends ReworkStartStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState startStepState = (StepState) reworkStepStates[0];
			FutureMultiProcedureTimer futureTimer = new FutureMultiProcedureTimer();
			if (startStepState != null) {
				futureTimer.setCreated(new Date());
				futureTimer.setOrgRrn(Env.getOrgRrn());
				futureTimer.setProcessName(processSection.getProcess().getName());
				if (futureTimer.getIsAll()) {
					futureTimer.setProcessVersion(null);
				} else {
					futureTimer.setProcessVersion(processSection.getProcess().getVersion());
				}
				futureTimer.setPath(startStepState.getPath());
				futureTimer.setProcedureName(startStepState.getProcessDefinition().getName());
				futureTimer.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
				futureTimer.setStepName(startStepState.getStepName());
				futureTimer.setStepStateName(startStepState.getName());
				
				section.setAdObject(futureTimer);
				section.refresh();
			}
		}
	}
	
	public class MultiEndStepStateAction extends EndStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState endStepState = getStepEnd();
			FutureMultiProcedureTimer futureTimer = (FutureMultiProcedureTimer) section.getAdObject();
			if (endStepState != null) {
				futureTimer.setEndPath(endStepState.getPath());
				futureTimer.setEndProcedureName(endStepState.getProcessDefinition().getName());
				futureTimer.setEndProcedureVersion(endStepState.getProcessDefinition().getVersion());
				futureTimer.setEndStepName(endStepState.getStepName());
				futureTimer.setEndStepStateName(endStepState.getName());	
				for (IForm detailForm : section.getDetailForms()) {
					((Form)detailForm).setFieldValue("endStepName", endStepState.getStepName());
				}
				section.setAdObject(futureTimer);
			}
		}
	}
	
	public class MultiReworkEndStepStateAction extends ReworkEndStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState endStepState = (StepState) reworkStepStates[1];
			FutureMultiProcedureTimer futureTimer = (FutureMultiProcedureTimer) section.getAdObject();
			if (endStepState != null) {
				futureTimer.setEndPath(endStepState.getPath());
				futureTimer.setEndProcedureName(endStepState.getProcessDefinition().getName());
				futureTimer.setEndProcedureVersion(endStepState.getProcessDefinition().getVersion());
				futureTimer.setEndStepName(endStepState.getStepName());
				futureTimer.setEndStepStateName(endStepState.getName());	
				for (IForm detailForm : section.getDetailForms()) {
					((Form)detailForm).setFieldValue("endStepName", endStepState.getStepName());
				}
				section.setAdObject(futureTimer);
			}
		}
	}
	
	public class AddEndStepStateAction extends Action {
		@Override
		public void run() {
			try {
				// ���п�ʼ�����ڵ��������ӽ����ڵ�
				if (endStepStateItem == null || startStepStateItem == null || startStepStateItem.equals(currentSelectedItem)) {
					UI.showError(Message.getString("wip.prd_start_end_is_null"));
					return;
				}
				FutureMultiProcedureTimer futureTimer = (FutureMultiProcedureTimer) section.getAdObject();
				for (IForm detailForm : section.getDetailForms()) {
					detailForm.saveToObject();
					PropertyUtil.copyProperties(futureTimer, detailForm
							.getObject(), detailForm.getCopyProperties());
				}
				FutureMultiProcedureTimer addEndTimer = (FutureMultiProcedureTimer) futureTimer.clone();
				StepState stepState = (StepState) currentSelectedItem.getData();
				List<FutureAction> childFutureActions = futureTimer.getChildFutureActions() != null ? futureTimer.getChildFutureActions() : Lists.newArrayList();
				if (!childFutureActions.isEmpty()) {
					for (FutureAction action : futureTimer.getChildFutureActions()) {
						FutureMultiProcedureTimer childFutureTimer = (FutureMultiProcedureTimer) action;
						if (childFutureTimer.getEndStepStateName().equals(stepState.getName())) {
							addEndTimer = childFutureTimer;
							break;
						}
					}
				}
				futureTimer.setEndPath(stepState.getPath());
				futureTimer.setEndProcedureName(stepState.getProcessDefinition().getName());
				futureTimer.setEndProcedureVersion(stepState.getProcessDefinition().getVersion());
				futureTimer.setEndStepName(stepState.getStepName());
				futureTimer.setEndStepStateName(stepState.getName());
				
				ADManager adManager = (ADManager) Framework.getService(ADManager.class);
				ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
				MultiTimerAddEndEntityDialog dialog = new MultiTimerAddEndEntityDialog(adTable, addEndTimer);
				if (dialog.open() == Dialog.OK) {
					// ����ԭ��startEnd
					if (startEndStepItem != null) {
						if (!startEndStepItem.isDisposed()) {
							startEndStepItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_WHITE));
						}
						startEndStepItem = null;
					}

					// ���õ�ǰ��ӽ����ڵ���ɫ
					currentSelectedItem.setBackground(Display.getCurrent().getSystemColor(SWT.COLOR_RED));
					addEndStepStateItems.put(currentSelectedItem.getText(), currentSelectedItem);
					childFutureActions.add((FutureMultiProcedureTimer)dialog.getAdObject());
					futureTimer.setChildFutureActions(childFutureActions);
					for (IForm detailForm : section.getDetailForms()) {
						((Form)detailForm).setFieldValue("childFutureActions", childFutureActions);
					}
					section.setAdObject(futureTimer);
	            }
			} catch (Exception e) {
	            ExceptionHandlerManager.asyncHandleException(e);
	            return;
	        }
		}
	}
	
	public class MultiAddFutureStepStateAction extends AddFutureStepStateAction {
		@Override
		public void run() {	
			super.run();
			StepState actionStepState = getActionStep();
			FutureMultiProcedureTimer futureTimer = (FutureMultiProcedureTimer) section.getAdObject();
			if (actionStepState != null) {
				futureTimer.setTimerAction(FutureTimer.ACTION_STEP_HOLD);
				futureTimer.setActionPath(actionStepState.getPath());
				futureTimer.setActionStepName(actionStepState.getStepName());
				futureTimer.setActionStepStateName(actionStepState.getName());
				List<FutureAction> childFutureActions = futureTimer.getChildFutureActions();
				if (childFutureActions != null && !childFutureActions.isEmpty()) {
					for(FutureAction action : futureTimer.getChildFutureActions()) {
						FutureMultiProcedureTimer childFutureTimer = (FutureMultiProcedureTimer) action;
						childFutureTimer.setActionPath(futureTimer.getActionPath());
						childFutureTimer.setActionStepName(futureTimer.getActionStepName());
						childFutureTimer.setActionStepStateName(futureTimer.getActionStepStateName());
					}
				}
				for(IForm detailForm : section.getDetailForms()) {
					((Form)detailForm).setFieldValue("actionStepName", actionStepState.getStepName());
					((Form)detailForm).setFieldValue("childFutureActions", childFutureActions);
				}
				section.setAdObject(futureTimer);
			}
		}
	}
	
	public void loadFlowTreeByProcedure(com.glory.mes.prd.model.Process process, String procedureName ,String stepName, List<FutureMultiProcedureTimer> futureMultiProcedureTimers) {
		try {
			PrdManager prdManager = Framework.getService(PrdManager.class);		
			if (process != null) {

				List<com.glory.mes.prd.model.Process> processes = new ArrayList<com.glory.mes.prd.model.Process>();
				process = (com.glory.mes.prd.model.Process) prdManager.getProcessDefinition(process);
				processes.add(process);
				
				Procedure procedure = new Procedure();
				procedure.setOrgRrn(Env.getOrgRrn());
				procedure.setName(procedureName);
				procedure = (Procedure) prdManager.getProcessDefinition(procedure);
				
				manager.setInput(processes);
			
				if (procedureName != null) {
					List<ADBase> processesBase = new ArrayList<ADBase>();
					processesBase.add(process);
					
					PrdQueryAction queryAction = PrdQueryAction.newIntance();
					queryAction.setCopyNode(true);
					List<Node> nodes = prdManager.getProcessDefinitionChildern(process, queryAction);
					nodes = nodes.stream().filter(s -> s.getName().equals(procedureName)).collect(Collectors.toList());
					processesBase.addAll(nodes);
					//�򿪹����ڵ�
					if (stepName != null) {
						List<Node> stepNodes = prdManager.getProcessDefinitionChildern(procedure, queryAction);
						stepNodes = stepNodes.stream().filter(s -> s.getName().equals(stepName)).collect(Collectors.toList());
						processesBase.addAll(stepNodes);
					}	
					//��Qtime�ڵ�
					if (CollectionUtils.isNotEmpty(futureMultiProcedureTimers)) {
						processesBase.addAll(futureMultiProcedureTimers);
					}					
					currentFlowList = (List)processesBase;										
					setCurrentFlow(currentFlowList);	
				} else {
					viewer.expandToLevel(4);
				}
			} else {
				manager.setInput(null);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ���õ�ǰ�ڵ�
	 */
	public void setCurrentFlow(List<ADBase> currentFlowList) {
		if (currentFlowList != null && currentFlowList.size() > 0) {
			TreeSelection section = new TreeSelection(new TreePath(currentFlowList.toArray()));
			viewer.setSelection(section);

//			TreeItem[] items = tree.getSelection();
//			for (TreeItem item : items) {
//				item.setImage(SWTResourceCache.getImage("currentstep"));
//				while (item.getParentItem() != null) {
//					item = item.getParentItem();
//				}
//			}
		}		
	}

}
