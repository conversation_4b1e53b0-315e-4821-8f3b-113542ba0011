package com.glory.mes.wip.advance.future.timer.multiproc;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.SearchMultiField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.process.flow.ProcessFlowSection;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiProcedureSection extends EntitySection  {

	private static final Logger logger = Logger.getLogger(MultiProcedureSection.class);
	
	protected ToolItem itemTimer;
	
	private MultiProcedureTreeManager treeManager;
	private MultiProcedureTreeField timerField;	
	private ProcessFlowSection processSection;
	
	private RefTableField timerTypeField;
	private RefTableField timerActionField;
	private RefTableField holdCodeField;
	private SearchMultiField holdOwnerField;
	private TextField holdReasonField;
	
	public MultiProcedureSection(ADTable table) {
		super(table);
	}

	public void initAdObject() {
		FutureMultiProcedureTimer futureTimer = new FutureMultiProcedureTimer();
		futureTimer.setOrgRrn(Env.getOrgRrn());
		setAdObject(futureTimer);
		refresh();
	}
	
	@Override
	public void createToolBar(Section section) { 
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTimer(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
		
		toolItems.put(ADButtonDefault.BUTTON_NAME_NEW, itemNew);
		toolItems.put(ADButtonDefault.BUTTON_NAME_SAVE, itemTimer);
		toolItems.put(ADButtonDefault.BUTTON_NAME_DELETE, itemDelete);
		toolItems.put(ADButtonDefault.BUTTON_NAME_REFRESH, itemRefresh);
	}
	
	protected void createToolItemTimer(ToolBar tBar) {
		itemTimer = new ToolItem(tBar, SWT.PUSH);
		itemTimer.setText(Message.getString("wip.timer")); 
		itemTimer.setImage(SWTResourceCache.getImage("timer_end"));
		itemTimer.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				timerAdapter();
			}
		});
	}
	
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		final IMessageManager mmng = form.getMessageManager();
		
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		
		MDSashForm sashForm = new MDSashForm(client, SWT.NULL);
		toolkit.adapt(sashForm, false, false);
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
		treeManager = new MultiProcedureTreeManager();
		timerField = new MultiProcedureTreeField(MultiProcedureTreeField.FIELD_ID, "", treeManager);
		timerField.section = this;
		processSection = new ProcessFlowSection(table, timerField);
		processSection.createContents(form, sashForm);
		processSection.getiField().addValueChangeListener(new IValueChangeListener() {
			@Override
			public void valueChanged(Object sender, Object newValue) {
				initAdObject();
			}
		});
		timerField.processSection = processSection;
		
		for (ADTab tab : getTable().getTabs()) {
			EntityForm itemForm = new EntityForm(sashForm, SWT.NULL, null, tab, mmng);
			getDetailForms().add(itemForm);
		}
		
		if (super.getField("timerType") instanceof RefTableField) {
			timerTypeField = (RefTableField) super.getField("timerType");
			timerTypeField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						String timerType = newValue == null ? "" : String.valueOf(newValue);
						
						if (StringUtils.isEmpty(timerType) || StringUtils.equals(timerType, FutureTimer.TIMERTYPE_MINIMAL)) {
							timerActionField.setValue(null);
							holdCodeField.setValue(null);
							holdOwnerField.setValue(null);
							holdReasonField.setText(null);
						} 
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		if (super.getField("timerAction") instanceof RefTableField) {
			timerActionField = (RefTableField) super.getField("timerAction");
			timerActionField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object sender, Object newValue) {
					try {
						String timerAction = newValue == null ? "" : String.valueOf(newValue);
						
						if (StringUtils.isEmpty(timerAction) || StringUtils.equals(timerAction, FutureTimer.ACTION_NOTE)) {
							holdCodeField.setValue(null);
							holdOwnerField.setValue(null);
							holdReasonField.setText(null);
						} 
					} catch (Exception e) {
						ExceptionHandlerManager.asyncHandleException(e);
						return;
					}
				}
			});
		}
		
		if (super.getField("holdCode") instanceof RefTableField) {
			holdCodeField = (RefTableField) super.getField("holdCode");
		}
		
		if (super.getField("holdOwner") instanceof SearchMultiField) {
			holdOwnerField = (SearchMultiField) super.getField("holdOwner");
		}
		
		if (super.getField("holdReason") instanceof TextField) {
			holdReasonField = (TextField) super.getField("holdReason");
		}
		initAdObject();
	}

	protected void createToolItemDelete(ToolBar tBar) {
		super.createToolItemDelete(tBar);
	}
	
	protected void deleteAdapter() {
		try {
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
				if (confirmDelete) {
					LotManager entityManager = Framework.getService(LotManager.class);
					entityManager.deleteFutureAction((FutureAction) getAdObject(), Env.getSessionContext());
					setAdObject(createAdObject());
					
					timerField.refreshProcedure();
					timerField.reset();
					refresh();
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}

	protected void timerAdapter() {
		form.getMessageManager().removeAllMessages();
		try {
			boolean saveFlag = true;
			for (IForm detailForm : getDetailForms()) {
				if (!detailForm.saveToObject()) {
					saveFlag = false;
				}
			}
			if (saveFlag) {
				for (IForm detailForm : getDetailForms()) {
					PropertyUtil.copyProperties(getAdObject(), detailForm
							.getObject(), detailForm.getCopyProperties());
				}
				
				FutureMultiProcedureTimer futureTimer = (FutureMultiProcedureTimer)getAdObject();
				if (futureTimer.getTimerDuration() == null || futureTimer.getTimerDuration() <= 0){
					 UI.showError(Message.getString("wip.timer_duration_must_valid"));
					 return;
				}
				
				if (StringUtils.equals(futureTimer.getTimerType(), FutureTimer.TIMERTYPE_MAXIMAL) && StringUtils.isEmpty(futureTimer.getTimerAction())) {
					 UI.showError(Message.getString("wip.timer_action_not_found"));
					 return;
				}
				
				if (StringUtils.isNotEmpty(futureTimer.getTimerAction()) && !StringUtils.equals(futureTimer.getTimerAction(), FutureTimer.ACTION_NOTE) && (StringUtils.isEmpty(futureTimer.getHoldCode()) || StringUtils.isEmpty(futureTimer.getHoldOwner()))) {
					 UI.showError(Message.getString("wip.timer_hold_info_not_complete"));
					 return;
				}
				
				if (futureTimer.getObjectRrn() == null) {
					if (timerField == null 
							|| timerField.getStepEnd() == null
							|| timerField.getStepStart() == null 
							|| timerField.getStepEnd().getObjectRrn() == timerField.getStepStart().getObjectRrn()){
						UI.showError(Message.getString("wip.timer_start_end_must"));
						return;
					}
					PrdManager prdManager = Framework.getService(PrdManager.class);
					int compare = prdManager.compareToStepState(processSection.getProcess(), timerField.getStepStart(), timerField.getStepEnd());
					if (compare < 0) {
						UI.showError(Message.getString("wip.prd_start_before_end"));
						return;
					}
					
					if (FutureTimer.ACTION_STEP_HOLD.equals(futureTimer.getTimerAction())) {
						if (timerField.getActionStep() == null || StringUtil.isEmpty(timerField.getActionStep().getStepName())) {
							UI.showError(Message.getString("wip.timer_action_step_name_is_null"));
							 return;
						}
						compare = prdManager.compareToStepState(processSection.getProcess(), timerField.getStepEnd(), timerField.getActionStep());
						if (compare < 0) {
							UI.showError(Message.getString("wip.prd_start_before_end"));
							return;
						}
						for (Map.Entry<String, StepState> entry : timerField.getAddEndStepStateItems().entrySet()) {
							compare = prdManager.compareToStepState(processSection.getProcess(), entry.getValue(), timerField.getActionStep());
							if (compare < 0) {
								UI.showError(Message.getString("wip.prd_start_before_end"));
								return;
							}
						}
					}
				}
					
				LotManager lotManager = Framework.getService(LotManager.class);
				futureTimer = (FutureMultiProcedureTimer)lotManager.saveFutureAction(futureTimer, Env.getSessionContext());
				UI.showInfo(Message.getString("wip.timer_holdsuccessed"));
				
				timerField.refreshProcedure();
				timerField.reset();
				refresh();
				setAdObject(futureTimer);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}

	public void setAdObject(ADBase adObject) {
		FutureAction futureAction = (FutureAction) adObject;
		try {
			ADManager entityManager = Framework.getService(ADManager.class);
			if (futureAction.getParentRrn() != null) {
				futureAction.setObjectRrn(futureAction.getParentRrn());
				futureAction = (FutureAction)entityManager.getEntity(futureAction);
			}
			if (futureAction.getObjectRrn() != null) {
				if (futureAction.getProcedureVersion() == null) {
					futureAction.setIsAll(true);
				} else {
					futureAction.setIsAll(false);
				}
				if (futureAction.getParentRrn() != null) {
					List<FutureAction> childFutureActions = entityManager.getEntityList(futureAction.getOrgRrn(),
							FutureAction.class, Env.getMaxResult(), " objectRrn != " + futureAction.getObjectRrn() 
							+ " AND parentRrn = " + futureAction.getObjectRrn(), null);
					futureAction.setChildFutureActions(childFutureActions);
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		this.adObject = futureAction;
	}

	public MultiProcedureTreeField getTimerField() {
		return timerField;
	}

	public void setTimerField(MultiProcedureTreeField timerField) {
		this.timerField = timerField;
	}

	public ProcessFlowSection getProcessSection() {
		return processSection;
	}

	public void setProcessSection(ProcessFlowSection processSection) {
		this.processSection = processSection;
	}
	
	
}