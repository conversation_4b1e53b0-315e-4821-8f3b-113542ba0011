package com.glory.mes.wip.advance.future.query;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

public class FutureActionQueryFixTableManager extends FixSizeListTableManager {

	public FutureActionQueryFixTableManager(ADTable adTable) {
		super(adTable);
	}

	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new FutureActionQueryItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
}
