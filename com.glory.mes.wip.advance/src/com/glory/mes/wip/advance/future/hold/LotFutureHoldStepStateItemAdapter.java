package com.glory.mes.wip.advance.future.hold;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.lot.flow.LotStepStateItemAdapter;

public class LotFutureHoldStepStateItemAdapter extends LotStepStateItemAdapter {
	
	private static final Logger logger = Logger.getLogger(LotFutureHoldStepStateItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	
	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState){
			List<Object> list = new ArrayList<Object>();
			StepState stepState = (StepState)object;
			try {
				Object[] children = super.getChildren(object);
				for (Object child : children) {
					list.add(child);
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				List<FutureHold> lotHolds = lotManager.getLotFutureHold(stepState, this.lot, null);
				for (FutureHold lotHold : lotHolds) {
					if (lotHold.getLotRrn() != null) {
						list.add(lotHold);
					}
				}
				return list.toArray();
			} catch (Exception e) {
	        	logger.error(e.getMessage(), e);
	        }
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
        return EMPTY;
	}
	
	public boolean hasChildren(Object object) {
		if (object instanceof StepState){
			boolean hasChild = super.hasChildren(object);
			if (hasChild == true) {
				return true;
			} 
			try {
				StepState stepState = (StepState)object;
				LotManager lotManager = Framework.getService(LotManager.class);
				List<FutureHold> lotHolds = lotManager.getLotFutureHold(stepState, this.lot, null);
				for (FutureHold lotHold : lotHolds) {
					if (lotHold.getLotRrn() != null) {
						return true;
					}
				}
			} catch (Exception e) {
	        	logger.error(e.getMessage(), e);
	        }
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
        return false;
	}

}
