package com.glory.mes.wip.advance.future.flowaction;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.lot.flow.LotStepStateItemAdapter;

public class FutureFlowActionStepStateItemAdapter extends LotStepStateItemAdapter {

	private static final Logger logger = Logger.getLogger(FutureFlowActionStepStateItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];

	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState) {
			StepState stepState = (StepState)object;
			List<Object> list = new ArrayList<Object>();
			ReworkState reworkState = stepState.getReworkState();
			if (reworkState != null) {
				list.add(reworkState);
			}
			
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<FutureAction> actions = lotManager.getLotFutureFlowAction(stepState, lot, null);
				if (CollectionUtils.isNotEmpty(actions)) {
					list.addAll(actions);
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
			return list.toArray();
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
		return EMPTY;
	}

	public boolean hasChildren(Object object) {
		if (object instanceof StepState) {
			boolean hasChild = super.hasChildren(object);
			if (hasChild == true) {
				return true;
			}
			try {
				StepState stepState = (StepState)object;
				LotManager lotManager = Framework.getService(LotManager.class);
				List<FutureAction> actions = lotManager.getLotFutureFlowAction(stepState, lot, null);
				if (CollectionUtils.isNotEmpty(actions)) {
					return true;
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
		return false;
	}

}
