package com.glory.mes.wip.advance.future.timer.multitimer;

import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapter;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.mes.prd.adapter.ElseStateItemAdapter;
import com.glory.mes.prd.adapter.EndIfStateItemAdapter;
import com.glory.mes.prd.adapter.IfStateItemAdapter;
import com.glory.mes.prd.adapter.ProcedureStateItemAdapter;
import com.glory.mes.prd.adapter.ProcessFlowItemAdapter;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.node.ElseState;
import com.glory.mes.prd.workflow.graph.node.EndIfState;
import com.glory.mes.prd.workflow.graph.node.IfState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.lot.flow.LotItemAdapter;
import com.glory.mes.wip.model.Lot;

public class MultiTimerTreeManager extends TreeViewerManager {
	private static final Logger logger = Logger.getLogger(MultiTimerTreeManager.class);
	
	@Override
	protected ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        ItemAdapter lotAdapter = new LotItemAdapter();
        MultiTimerItemAdapter timerAdapter = new MultiTimerItemAdapter();

        ItemAdapter itemAdapter = new ProcessFlowItemAdapter();
        try {
        	factory.registerAdapter(List.class, itemAdapter);
        	factory.registerAdapter(Lot.class, lotAdapter);
        	factory.registerAdapter(Process.class, itemAdapter);
        	factory.registerAdapter(Procedure.class, itemAdapter);
        	factory.registerAdapter(ProcedureState.class, new ProcedureStateItemAdapter());
        	
        	factory.registerAdapter(StepState.class, timerAdapter);
	        factory.registerAdapter(FutureMultiStepTimer.class, timerAdapter);
	        factory.registerAdapter(EndTimer.class, timerAdapter);
	        factory.registerAdapter(FutureHoldTimer.class, timerAdapter);
	        
	        factory.registerAdapter(IfState.class, new IfStateItemAdapter());
	        factory.registerAdapter(ElseState.class, new ElseStateItemAdapter());
	        factory.registerAdapter(EndIfState.class, new EndIfStateItemAdapter());
        } catch (Exception e){
        	logger.error(e.getMessage(), e);
        }
        return factory;
    }
	
}
