package com.glory.mes.wip.advance.future.step;

import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.ras.eqp.Equipment;

public class AvaliableEquipmentAdapter extends ListItemAdapter {
	
	@Override
	public Color getBackground(Object element, String id) {
		Equipment equipment = (Equipment) element;
		if(!equipment.getIsAvailable()) {
			return new Color(Display.getCurrent(), 255, 111, 111);
		}
		return super.getBackground(element, id);
	}
	
	@Override
	public String getText(Object element) {
		return super.getText(element);
	}
	
	@Override
	public Font getFont(Object object) {
		return super.getFont(object);
	}
	
	@Override
	public Color getColor(Object object) {
		return super.getColor(object);
	}
}
