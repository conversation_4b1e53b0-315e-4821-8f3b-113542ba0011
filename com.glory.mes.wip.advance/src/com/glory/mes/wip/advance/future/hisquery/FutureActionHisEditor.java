package com.glory.mes.wip.advance.future.hisquery;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;

public class FutureActionHisEditor extends GlcEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.hisquery.FutureActionHisEditor";

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
	}

}
