package com.glory.mes.wip.advance.future.procedurehold;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.procedure.flow.ProcedureFlowSection;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.framework.core.exception.ExceptionBundle;

public class ProcedureHoldSection extends EntitySection {

	private static final Logger logger = Logger.getLogger(ProcedureHoldSection.class);
	protected AuthorityToolItem itemNote;
	protected AuthorityToolItem itemDelete;
	protected ToolItem refreshItem;
	protected ProcedureFlowSection processSection;
	
	private ProcedureHoldFlowTreeField treeField;
	private StepState currentStepState;
	
	public static String KEY_HOLD = "hold";
	public static String KEY_DELETE = "delete";
	
	public ProcedureHoldSection(ADTable table) {
		super(table);
	}

	public void initAdObject() {
		FutureHold futureNote = new FutureHold();
		futureNote.setOrgRrn(Env.getOrgRrn());
		setAdObject(futureNote);
		refresh();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemHold(ToolBar tBar) {
		itemNote = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_HOLD);
		itemNote.setText(Message.getString("wip.processhold")); 
		itemNote.setImage(SWTResourceCache.getImage("hold"));
		itemNote.setEnabled(false);
		itemNote.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				holdAdapter();
			}
		});
	}
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		final IMessageManager mmng = form.getMessageManager();
		
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		
		MDSashForm sashForm = new MDSashForm(client, SWT.NULL);
		toolkit.adapt(sashForm, false, false);
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
		
		ProcedureHoldTreeManager treeManager = new ProcedureHoldTreeManager();
		treeField = new ProcedureHoldFlowTreeField(ProcedureHoldFlowTreeField.FIELD_ID, "", treeManager);
		treeField.lotSection = this;
		processSection = new ProcedureFlowSection(table, treeField);
		processSection.createContents(form, sashForm);
		
		for (ADTab tab : getTable().getTabs()) {
			EntityForm itemForm = new EntityForm(sashForm, SWT.NULL, tab, mmng);
			getDetailForms().add(itemForm);
		}		
	}

	protected void createToolItemDelete(ToolBar tBar) {
		itemDelete = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_DELETE);
		itemDelete.setText(Message.getString(ExceptionBundle.bundle.CommonDelete())); 
		itemDelete.setImage(SWTResourceCache.getImage("delete"));
		itemDelete.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				deleteAdapter();
			}
		});
	}
	
	protected void deleteAdapter() {
		try {
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message
						.getString("common.confirm_delete"));
				FutureHold hold = (FutureHold)getAdObject();
				if (confirmDelete) {
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.deleteFutureAction(hold, Env.getSessionContext());
					treeField.refresh();

					refresh();
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}

	protected void holdAdapter() {
		form.getMessageManager().removeAllMessages();
		try {
			if(getAdObject() != null) {
				if(this.currentStepState == null || this.currentStepState.getObjectRrn() == null) {
					UI.showError(Message.getString("wip.processhold_nostep"));
					return;
				}
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					FutureHold fn = (FutureHold)getAdObject();
					if (fn.getStepStateName() == null) {
						fn.setStepStateName(currentStepState.getName());
					}
					if (this.currentStepState.getPath() != null){
						fn.setPath(currentStepState.getPath());
					} else if(this.currentStepState.getPath() == null){
						UI.showError("pathisnull");
						return;
					}
					fn.setOrgRrn(Env.getOrgRrn());
					//fn.setUserName(Env.getUserName());
					fn.setProcedureName(currentStepState.getProcessDefinition().getName());
					//����û�����������ȫ�����̣���Ѱ汾��Ϊnull
					if (fn.getIsAll()) {
						fn.setProcedureVersion(null);
					} else {
						fn.setProcedureVersion(currentStepState.getProcessDefinition().getVersion());
					}
					
					fn.setStepName(currentStepState.getUsedStep().getName());
					
					LotManager lotManager = Framework.getService(LotManager.class);
					ADBase obj = lotManager.saveFutureAction((FutureAction)getAdObject(), Env.getSessionContext());

					ADManager entityManager = Framework.getService(ADManager.class);
					obj = entityManager.getEntity(obj);
					
					setAdObject(obj);
					UI.showInfo(Message.getString("wip.processhold_holdsuccessed"));
					treeField.refresh();
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("Error at FutureActionSection : holdAdapter" + e.getStackTrace());
			return;
		}
	}
	
	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}
	
	public void setAdObject(ADBase adObject) {
		FutureAction futureAction = (FutureAction)adObject;
		if (futureAction.getObjectRrn() != null) {
			if (futureAction.getProcedureVersion() == null) {
				futureAction.setIsAll(true);
			} else {
				futureAction.setIsAll(false);
			}
		} else {
			futureAction.setIsAll(false);
		}
		this.adObject = adObject;
	}
	
	public StepState getCurrentStepState() {
		return currentStepState;
	}


	public void setCurrentStepState(StepState currentStepState) {
		this.currentStepState = currentStepState;
	}

}