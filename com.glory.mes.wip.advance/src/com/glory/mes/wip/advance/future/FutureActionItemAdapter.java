package com.glory.mes.wip.advance.future;

import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.AbstractItemAdapter;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureChangeFlow;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureNewPart;
import com.glory.mes.wip.future.FutureNewProcedure;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.future.FutureTimer;

public class FutureActionItemAdapter extends AbstractItemAdapter {
	
	private static final Object[] EMPTY = new Object[0];
		
	@Override
	public Object[] getChildren(Object object) {
		return EMPTY;
	}

	@Override
	public Object[] getElements(Object object) {
		return EMPTY;
	}

	@Override
	public Object getParent(Object object) {
		return null;
	}

	@Override
	public boolean hasChildren(Object object) {
		return false;
	}
	
	@Override
	public String getText(Object element) {
		if (element instanceof FutureNewPart) {
			FutureNewPart action = (FutureNewPart) element;
			String text = "<Future New Part To (" + action.getNewPartName() + "." + action.getNewPartVersion() + ")";
			if (!StringUtil.isEmpty(action.getNewPath())) {
				text += " Path (" + action.getNewPath() + ")";
			}
			text += ">";
			return action.getName() + text;
		} else if (element instanceof FutureNewProcedure) {
			FutureNewProcedure action = (FutureNewProcedure) element;
			String text = "<Future New Procedure To (" + action.getNewProcedureName() + "." + action.getNewProcedureVersion() + ")"; 
			if (!StringUtil.isEmpty(action.getNewStartStepName())) {
				text += " Start (" + action.getNewStartStepName() + ")";
			}
			if (!StringUtil.isEmpty(action.getNewEndStepName())) {
				text += " End (" + action.getNewEndStepName() + ")";
			}
			
			text += " Return Path (" + action.getEndPath() + ")";
			text += ">";
			return action.getName() + text;
		} else if (element instanceof FutureChangeFlow) {
			FutureChangeFlow action = (FutureChangeFlow) element;
			String text = "<Future Change Flow To Path (" + action.getEndPath() + ")>";
			return action.getName() + text;
		} else if(element instanceof FutureAction) {
			FutureAction action = (FutureAction)element;
			String text = null;
			text = (action.getDescription() != null && !"".equals(action.getDescription().trim()) ?
					"<" + action.getDescription() + ">" : "");
			return action.getName() + text;
		}
		return null;
	}
	
	@Override
	public String getText(Object object, String id) {
		return getText(object);
	}
	
	@Override
	public ImageDescriptor getImageDescriptor(Object object, String id) {
		if (object instanceof FutureNote) {
			return SWTResourceCache.getImageDescriptor("note");
		} else if (object instanceof FutureHold) {
			return SWTResourceCache.getImageDescriptor("hold");
		} else if (object instanceof FutureTimer) {
			return SWTResourceCache.getImageDescriptor("timer_start");
		} else if (object instanceof FutureNewPart) {
			return SWTResourceCache.getImageDescriptor("future_new_part");
		} else if (object instanceof FutureNewProcedure) {
			return SWTResourceCache.getImageDescriptor("future_new_procedure");
		} else if (object instanceof FutureChangeFlow) {
			return SWTResourceCache.getImageDescriptor("future_change_flow");
		}
		return SWTResourceCache.getImageDescriptor("note");
	}
	
	@Override
	public Color getForeground(Object element, String id) {
		return null;
	}
	
	public Color getColor(Object object) {
		return new Color(Display.getCurrent(), 0, 67, 255);
	}
	
	@Override
	public Font getFont(Object object, String id) {
		return SWTResourceCache.getFont(SWTResourceCache.FONT_VERDANA_NORMAL);
	}
}
