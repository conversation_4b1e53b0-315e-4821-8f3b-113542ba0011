package com.glory.mes.wip.advance.future.step;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.editor.FixSizeListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

public class FutureStepTableManager extends FixSizeListTableManager{

	public FutureStepTableManager(ADTable adTable) {
		super(adTable);
	}
	
	@Override
	public ItemAdapterFactory createAdapterFactory() {
		ItemAdapterFactory factory = new ItemAdapterFactory();
		factory.registerAdapter(Object.class, new FutureStepAdapter());
		return factory;
	}

}
