package com.glory.mes.wip.advance.future.note;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.advance.future.FutureActionMediator;
import com.glory.mes.wip.advance.future.LotFutureActionSection;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureNote;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotNoteSection extends LotFutureActionSection {
	
	private static final Logger logger = Logger.getLogger(LotNoteSection.class);

	protected AuthorityToolItem itemNote;
	protected AuthorityToolItem itemDelete;

	public static String KEY_NOTE = "note";
	public static String KEY_DELETE = "delete";

	public LotNoteSection() {
		super();
	}

	public LotNoteSection(LotFutureNoteTreeManager manager, ADTable table) {
		super(manager, table);
	}
	
	@Override
	public void initAdObject() {
		FutureNote futureNote = new FutureNote();
		futureNote.setOrgRrn(Env.getOrgRrn());
		setAdObject(futureNote);
		refresh();
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		this.adObject = adObject;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemNote(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemNote(ToolBar tBar) {
		itemNote = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_NOTE);
		itemNote.setText(Message.getString("wip.futurenote")); 
		itemNote.setImage(SWTResourceCache.getImage("note"));
		itemNote.setEnabled(false);
		itemNote.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				noteAdapter();
			}
		});
	}
	
	protected void createToolItemDelete(ToolBar tBar) {
		itemDelete = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_DELETE);
		itemDelete.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
		itemDelete.setImage(SWTResourceCache.getImage("delete"));
		itemDelete.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				deleteAdapter();
			}
		});
	}

	protected void noteAdapter() {
		form.getMessageManager().removeAllMessages();
		try {
			if(getAdObject() != null) {
				if(this.currentStepState == null || this.currentStepState.getObjectRrn() == null) {
					UI.showError(Message.getString("wip.futurenote_nostep"));
					return;
				}
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					FutureNote fn = (FutureNote)getAdObject();
					if (fn.getLotRrn() == null) {
						fn.setLotRrn(currentLot.getObjectRrn());
						fn.setLotId(currentLot.getLotId());
					}
					if (fn.getStepStateName() == null) {
						fn.setStepStateName(currentStepState.getName());
					}
					fn.setOrgRrn(Env.getOrgRrn());
					fn.setPath(currentStepState.getPath());
					fn.setProcessName(currentLot.getProcessName());
					fn.setProcedureName(currentStepState.getProcessDefinition().getName());
					fn.setProcedureVersion(currentStepState.getProcessDefinition().getVersion());
					fn.setStepName(currentStepState.getUsedStep().getName());
					
					LotManager lotManager = Framework.getService(LotManager.class);
					ADBase obj = lotManager.saveFutureAction((FutureAction)getAdObject(), Env.getSessionContext());

					ADManager entityManager = Framework.getService(ADManager.class);
					obj = entityManager.getEntity(obj);

					setAdObject(obj);
					UI.showInfo(Message.getString("wip.futurenote_successed"));
					lotMediator.notifyLotFlowTreeField(FutureActionMediator.REFRESH, obj);
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("Error at FutureActionSection : noteAdapter" + e.getStackTrace());
			return;
		}
	}
	
	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}
	

	@Override
	public void statusChanged(String newStatus){
		if(newStatus.equalsIgnoreCase("FutureNote")||newStatus.trim().equalsIgnoreCase("StepState")){
			itemNote.setEnabled(true);
		}
		else itemNote.setEnabled(false);
	
	}


}
