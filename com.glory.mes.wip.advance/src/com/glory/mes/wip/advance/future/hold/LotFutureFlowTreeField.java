package com.glory.mes.wip.advance.future.hold;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.TreePath;
import org.eclipse.jface.viewers.TreeSelection;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.client.SysParameterManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.config.MesCfMod;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.ProcessDefinition;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.lot.flow.LotFlowForm;
import com.glory.mes.wip.lot.flow.LotFlowTreeField;
import com.glory.mes.wip.model.Lot;

public class LotFutureFlowTreeField extends LotFlowTreeField{

	public LotFutureFlowTreeField(String id, String label, TreeViewerManager manager, LotFlowForm lotFlowForm) {
		super(id, label, manager, lotFlowForm);
	}

	@Override
	public void refresh() {
		super.refresh();
		if (getValue() != null) { 
			Lot lot = (Lot) getValue();
			if (lot.getObjectRrn() == null) {
				viewer.setInput(null);
				return;
			}
			try {
				SysParameterManager sysParamManager = Framework.getService(SysParameterManager.class);
				if (MesCfMod.isLotFutureHoldAutoExpand(Env.getOrgRrn(), sysParamManager)) {
					ADManager adManager =	Framework.getService(ADManager.class);
					PrdManager prdManager = Framework.getService(PrdManager.class);
					List<FutureHold> futureHolds = adManager.getEntityList(Env.getOrgRrn(), FutureHold.class, Integer.MAX_VALUE,
							"lotRrn = " + lot.getObjectRrn(), "created");
					if(futureHolds != null && !futureHolds.isEmpty()) {
						ProcessDefinition pf = prdManager.getProcessInstance(lot.getProcessInstanceRrn()).getProcessDefinition();
						for(FutureHold futureHold : futureHolds) {
							List<Node> nodes = prdManager.getNodeByPath(Env.getOrgRrn(), pf, futureHold.getPath(), 3, null);
							List<ADBase> list = new ArrayList<>();
							list.add(nodes.get(0).getParent());
							list.addAll(nodes);
							list.add(futureHold);
							TreeSelection section = new TreeSelection(new TreePath(list.toArray()));
							viewer.setSelection(section);
						}
						
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			viewer.setInput(null);
		}
	}
}
