package com.glory.mes.wip.advance.future.procedurenote;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.flow.LotStepStateItemAdapter;

public class ProcedureNoteItemAdapter extends LotStepStateItemAdapter {

	private static final Logger logger = Logger.getLogger(ProcedureNoteItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];
	private String currentPath;

	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState) {
			List<Object> list = new ArrayList<Object>();
			StepState stepState = (StepState) object;
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<FutureNote> futureNotes = lotManager.getProcedureFutureNote(stepState);
				list.addAll(futureNotes);
				return list.toArray();
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
		return EMPTY;
	}

	public boolean hasChildren(Object object) {
		if (object instanceof StepState) {
			StepState stepState = (StepState) object;
			try {
				currentPath = stepState.getParent().getName() + "/" + stepState.getName() + "/";
				stepState.setPath(currentPath);
				LotManager lotManager = Framework.getService(LotManager.class);
				List<FutureNote> futureNotes = lotManager.getProcedureFutureNote(stepState);
				if (futureNotes.size() > 0) {
					return true;
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		} else {
			logger.error("Expect StepState, but found " + object.toString());
		}
		return false;
	}

}
