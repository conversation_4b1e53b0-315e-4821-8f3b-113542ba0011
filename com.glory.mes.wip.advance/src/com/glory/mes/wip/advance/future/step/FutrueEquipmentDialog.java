package com.glory.mes.wip.advance.future.step;

import java.util.List;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.dialog.BaseDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.model.Lot;

public class FutrueEquipmentDialog extends BaseDialog {

	private static int MIN_DIALOG_WIDTH = 900;
	private static int MIN_DIALOG_HEIGHT = 400;

	protected static final String TABLE_NAME_STEP = "WIPADVFutureStepQuery";
	protected static final String TABLE_NAME_EQP = "WIPADVFutureEqpQuery";
	public ListTableManager mainListTableManager;
	public ListTableManager subListTableManager;
	
	private List<Lot> lots;

	public FutrueEquipmentDialog(List<Lot> lots) {
		super();
		this.lots = lots;
	}

	@Override
	protected Control buildView(Composite parent) {
		try {
			parent.setLayout(new GridLayout(1, true));
			parent.setLayoutData(new GridData(GridData.FILL_BOTH));

			FormToolkit toolkit = new FormToolkit(parent.getDisplay());

			Section section = toolkit.createSection(parent, Section.TITLE_BAR | Section.EXPANDED);
			section.setLayout(new GridLayout(1, true));
			section.setLayoutData(new GridData(GridData.FILL_BOTH));
			section.setText(Message.getString("wip.adv_future_available_eqp"));

			Composite composite = toolkit.createComposite(section, SWT.NONE);
			composite.setLayout(new GridLayout(2, true));
			composite.setLayoutData(new GridData(GridData.FILL_BOTH));

			Composite mianComposite = toolkit.createComposite(composite, SWT.NONE);
			mianComposite.setLayout(new GridLayout(1, true));
			mianComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

			ADManager adManager = Framework.getService(ADManager.class);
			ADTable mainTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_STEP);
			mainListTableManager = new ListTableManager(mainTable);
			mainListTableManager.newViewer(mianComposite);
			mainListTableManager.setInput(lots);
			mainListTableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					Lot lot = (Lot) mainListTableManager.getSelectedObject();
					if (lot != null) {
						List<Equipment> equipments = (List)lot.getAttribute2();
						subListTableManager.setInput(equipments);
					}
				}
			});
			Composite subComposite = toolkit.createComposite(composite, SWT.NONE);
			subComposite.setLayout(new GridLayout(1, true));
			subComposite.setLayoutData(new GridData(GridData.FILL_BOTH));

			ADTable subTable = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME_EQP);
			subListTableManager = new AvaliableEquipmentTableManager(subTable);
			subListTableManager.setAutoSizeFlag(false);
			subListTableManager.newViewer(subComposite);

			section.setClient(composite);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return parent;
	}

	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.max(convertHorizontalDLUsToPixels(MIN_DIALOG_WIDTH), shellSize.x),
				Math.max(convertVerticalDLUsToPixels(MIN_DIALOG_HEIGHT), shellSize.y));
	}

}
