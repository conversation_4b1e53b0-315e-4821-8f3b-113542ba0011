package com.glory.mes.wip.advance.future.query.procedure;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.SectionPart;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.IRefresh;
import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.I18nUtil;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureMerge;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.future.FutureTimer;
import com.glory.framework.core.exception.ExceptionBundle;

public class ProcedureFutureQuerySection extends QueryEntityListSection implements IRefresh {
	
	private static final Logger logger = Logger.getLogger(ProcedureFutureQuerySection.class);
	
	private static final String FIELD_PROCEDURENAME = "procedureName";
	private static final String FIELD_PROCEDUREVERSION = "procedureVersion";
	
	private ToolItem itemCancelHold;
	private ToolItem itemCancelNote;
	private ToolItem itemCancelTimer;
	private ToolItem itemCancelMerge;
	
	private Button btnNote;
	private Button btnHold;
	private Button btnTimer;
	private Button btnMerge;
	
	private List<String> actionTypes;
	
    public ProcedureFutureQuerySection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createContents(final IManagedForm form, Composite parent, int sectionStyle) {
		final FormToolkit toolkit = form.getToolkit();
		final ADTable table = getADTable();
		managedForm = (ManagedForm) form;
		
		section = toolkit.createSection(parent, sectionStyle);
		section.setText(I18nUtil.getI18nMessage(table, "label"));
		section.marginWidth = 3;
	    section.marginHeight = 4;
	    if ((sectionStyle & Section.NO_TITLE) == 0) {
	    	toolkit.createCompositeSeparator(section);
	    }

	    setLayout(parent);
	    
		GridLayout layout = new GridLayout();
		layout.marginWidth = 5;
		layout.marginHeight = 5;
		section.setLayout(layout);
	    
	    Composite client = toolkit.createComposite(section);    
	    layout = new GridLayout();    
	    layout.numColumns = 1;    
	    client.setLayout(layout);
	    
	    createToolBar(section);
	    
	    spart = new SectionPart(section);    
	    form.addPart(spart);
	    section.setText(String.format(Message.getString(ExceptionBundle.bundle.CommonList()), I18nUtil.getI18nMessage(table, "label")));  

        Composite queryComp = new Composite(client, SWT.NONE);
        GridData gd = new GridData(GridData.FILL_HORIZONTAL);
        queryComp.setLayoutData(gd);
        layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        layout.numColumns = 3;    
        queryComp.setLayout(layout);
        
        setQueryForm(createQueryFrom(form, queryComp));
        gd = new GridData(GridData.FILL_BOTH);
        getQueryForm().setLayoutData(gd);

        Composite buttonBar = toolkit.createComposite(queryComp, SWT.NONE);
        gd = new GridData(GridData.FILL_VERTICAL);
        gd.widthHint = 60;
        gd.horizontalAlignment = SWT.END;
        buttonBar.setLayoutData(gd);
        
		layout = new GridLayout();
		layout.numColumns = 1;
		layout.marginBottom = 15;
		buttonBar.setLayout(layout);
		
		SquareButton btnQuery = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
		btnQuery.setText(Message.getString(ExceptionBundle.bundle.CommonSearch()));
		gd = new GridData(GridData.FILL_VERTICAL);
		gd.verticalAlignment = SWT.END;
		btnQuery.setLayoutData(gd);
		btnQuery.addSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent event) {
				queryAdapter();
			}
		});
		
		Composite right = toolkit.createComposite(queryComp);
		layout = new GridLayout(4, false);
		right.setLayout(layout);
		gd = new GridData(GridData.FILL_HORIZONTAL | GridData.FILL_BOTH);
		gd.horizontalAlignment = SWT.BEGINNING;
		gd.grabExcessHorizontalSpace = true;
		right.setLayoutData(gd);
		
		btnHold = new Button(right, SWT.CHECK | SWT.LEFT);
		btnHold.setSelection(true);
		btnHold.setText("FutureHold");
		
		btnNote = new Button(right, SWT.CHECK);
		btnNote.setText("FutureNote");
		btnNote.setSelection(true);
		
		btnTimer = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnTimer.setText("FutureTimer");
		btnTimer.setSelection(true);
		
		btnMerge = new Button(right, SWT.CHECK | SWT.RIGHT);
		btnMerge.setText("MergeHold");
		btnMerge.setSelection(true);
		
		Composite resultComp = new Composite(client, SWT.NONE);
        layout = new GridLayout(); 
        layout.verticalSpacing = 0;
        layout.marginHeight = 0;
        resultComp.setLayout(layout);
        resultComp.setLayoutData(new GridData(GridData.FILL_BOTH));
	    createNewViewer(resultComp, form);
	    section.setClient(client);
	}

	public List<String> getActionTypes() {
		actionTypes = new ArrayList<String>();
		if (this.btnNote.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_NOTE);
		}
		if (this.btnHold.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_HOLD);
		}
		if (this.btnTimer.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_TIMER);
		}
		if (this.btnMerge.getSelection()) {
			this.actionTypes.add(FutureAction.ACTION_MERGE);
		}
		return actionTypes;
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNote(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemTimer(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemMerge(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	
	private void createToolItemNote(ToolBar tBar) {
		itemCancelNote = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.ProcedureFutureActionQuery_cancelnote");
		itemCancelNote.setText("CancelNote");
		itemCancelNote.setImage(SWTResourceCache.getImage("note"));
		itemCancelNote.setEnabled(false);
		itemCancelNote.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelnote"))){
					cancelNoteAdapter();
				}
			}
		});
	}

	protected void cancelNoteAdapter() {
		try {
			if (this.getSelectedObject() != null &&
					this.getSelectedObject() instanceof FutureNote){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureNote)this.getSelectedObject(), Env.getSessionContext());
				refreshDelete(this.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	/**
	 * ȡ��FutureHold
	 */
	protected void createToolItemHold(ToolBar tBar) {
		itemCancelHold = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.ProcedureFutureActionQuery_cancelhold");
		itemCancelHold.setText("CancelHold");
		itemCancelHold.setImage(SWTResourceCache.getImage("hold"));
		itemCancelHold.setEnabled(false);
		itemCancelHold.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelhold"))){
					cancelHoldAdapter();
				}
			}
		});
	}

	protected void cancelHoldAdapter() {
		try {
			if (this.getSelectedObject() != null &&
					this.getSelectedObject() instanceof FutureHold){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureHold)this.getSelectedObject(), Env.getSessionContext());
				refreshDelete(this.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	/**
	 * ȡ��Timer
	 */
	protected void createToolItemTimer(ToolBar tBar) {
		itemCancelTimer = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.ProcedureFutureActionQuery_canceltimer");
		itemCancelTimer.setText("CancelTimer");
		itemCancelTimer.setImage(SWTResourceCache.getImage("timer_start"));
		itemCancelTimer.setEnabled(false);
		itemCancelTimer.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if(UI.showConfirm(Message.getString("wipadv.future_sure_to_canceltimer"))){
					cancelTimerAdapter();
				}
			}
		});
	}

	protected void cancelTimerAdapter() {
		try {
			if (this.getSelectedObject() != null &&
					this.getSelectedObject() instanceof FutureTimer){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureTimer)this.getSelectedObject(), Env.getSessionContext());
				refreshDelete(this.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	protected void createToolItemMerge(ToolBar tBar) {
		//itemCancelMerge = new AuthorityToolItem(tBar, SWT.PUSH, "wipadv.procedurefuture_cancelmerge");
		itemCancelMerge = new AuthorityToolItem(tBar, SWT.PUSH, "WipAdv.ProcedureFutureActionQuery_cancelmerge");
		itemCancelMerge.setText("CancelMerge");
		itemCancelMerge.setImage(SWTResourceCache.getImage("merge"));
		itemCancelMerge.setEnabled(false);
		itemCancelMerge.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				if (UI.showConfirm(Message.getString("wipadv.future_sure_to_cancelmerge"))){
					cancelMergeAdapter();
				}
			}
		});
	}

	protected void cancelMergeAdapter() {
		try {
			if (this.getSelectedObject() != null &&
					this.getSelectedObject() instanceof FutureMerge){
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteFutureAction((FutureMerge)this.getSelectedObject(), Env.getSessionContext());
				refreshDelete(this.getSelectedObject());
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	@Override
	public void refresh(){
		try {		
			IField fProcedureName = this.getQueryForm().getFields().get(FIELD_PROCEDURENAME);
			if (fProcedureName == null 
					|| fProcedureName.getValue() == null
					|| fProcedureName.getValue().toString().trim().length() == 0) {
				return;
			}
			String procedureName = fProcedureName.getValue().toString().trim();
			Long procedureVersion = null;
			IField fProcedureVersion = this.getQueryForm().getFields().get(FIELD_PROCEDUREVERSION);
			if (fProcedureVersion != null 
					&& fProcedureVersion.getValue() != null
					&& fProcedureVersion.getValue().toString().trim().length() != 0) {
				procedureVersion = Long.parseLong(fProcedureVersion.getValue().toString());
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			List<FutureAction> futureActions = lotManager.getProcedureFutureActions(
					Env.getOrgRrn(), procedureName, procedureVersion, this.getActionTypes());
			
			showNumber = futureActions.size();
			createSectionDesc(section);
			tableManager.setInput(futureActions);
		} catch(Exception e) {
			logger.error("Error at Refresh ", e);
		}
	}

	public void selectedChanged(SelectionChangedEvent event) {
		StructuredSelection ss = (StructuredSelection)event.getSelection();
		if (ss != null && ss.getFirstElement() != null) {
			statusChanged(((FutureAction)ss.getFirstElement()));
		} 
	}
	
	public void statusChanged(FutureAction action) {
		if (action instanceof FutureNote){
			itemCancelNote.setEnabled(true);
			itemCancelHold.setEnabled(false);
			itemCancelTimer.setEnabled(false);
			itemCancelMerge.setEnabled(false);
		} else if(action instanceof FutureHold){
			itemCancelNote.setEnabled(false);
			itemCancelHold.setEnabled(true);
			itemCancelTimer.setEnabled(false);
			itemCancelMerge.setEnabled(false);
		} else if(action instanceof FutureTimer){
			itemCancelNote.setEnabled(false);
			itemCancelHold.setEnabled(false);
			itemCancelTimer.setEnabled(true);
			itemCancelMerge.setEnabled(false);
		} else if(action instanceof FutureMerge){
			itemCancelNote.setEnabled(false);
			itemCancelHold.setEnabled(false);
			itemCancelTimer.setEnabled(false);
			itemCancelMerge.setEnabled(true);
		} else {
			itemCancelNote.setEnabled(false);
			itemCancelHold.setEnabled(false);
			itemCancelTimer.setEnabled(false);
			itemCancelMerge.setEnabled(false);
		}
	}

}
