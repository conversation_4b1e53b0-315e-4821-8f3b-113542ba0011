package com.glory.mes.wip.advance.future.query.startedtimer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.FutureTimerManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.advance.future.timer.color.ColorTimerInstanceQuerySection;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class StartedTimerQuerySection extends ColorTimerInstanceQuerySection {
	
	protected static final String KEY_CHANGE = "Change";
	protected static final String KEY_CLEAR = "Clear";
	protected ToolItem itemChange;
	protected ToolItem itemClear;
	protected ToolItem itemSuspened;
	protected ToolItem itemResumeTimer;
	
	public StartedTimerQuerySection(ListTableManager tableManager) {
		super(tableManager);
	}
	
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSuspened(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemResumeTimer(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemChange(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemClear(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemSuspened(ToolBar tBar) {
		itemSuspened = new ToolItem(tBar, SWT.PUSH);
		itemSuspened.setText(Message.getString("wip.suspened"));
		itemSuspened.setImage(SWTResourceCache.getImage("hold-lot"));
		itemSuspened.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				suspenedAdapter();
			}
		});
	}
	
	protected void createToolItemResumeTimer(ToolBar tBar) {
		itemResumeTimer = new ToolItem(tBar, SWT.PUSH);
		itemResumeTimer.setText(Message.getString("task.resume"));
		itemResumeTimer.setImage(SWTResourceCache.getImage("release-lot"));
		itemResumeTimer.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				resumeTimerAdapter();
			}
		});
	}
	
	protected void createToolItemChange(ToolBar tBar) {
		itemChange = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CHANGE);
		itemChange.setText(Message.getString("wip.change_expire_time"));
		itemChange.setImage(SWTResourceCache.getImage("timer_started"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				changeAdapter();
			}
		});
	}
	
	protected void createToolItemClear(ToolBar tBar) {
		itemClear = new AuthorityToolItem(tBar, SWT.PUSH, getADTable().getAuthorityKey() + "." + KEY_CLEAR);
		itemClear.setText(Message.getString("wip.clear_expire_time"));
		itemClear.setImage(SWTResourceCache.getImage("delete"));
		itemClear.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				clearAdapter();
			}
		});
	}
	
	protected void changeAdapter() {
		try {
			if (getSelectedObject() != null) {
				EntityDialog dialog = new ChangeExpireTimeDialog(tableManager.getADTable(), (ADBase)getSelectedObject());
				if (Dialog.OK == dialog.open()) {
					refreshUpdate(dialog.getAdObject());
				}
			} else {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}
	
	public boolean resumeTimerAdapter() {
		try {
			Object[] elements = null;
			if (getSelectedObject() != null) {
				elements = new Object[] {getSelectedObject()};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else {
				List<ADBase> baseList = new ArrayList<ADBase>();
				for (int i = 0; i < elements.length; i++) {
					ADBase base = (ADBase)elements[i];
					baseList.add(base);
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				FutureTimerManager futureTimerManager = Framework.getService(FutureTimerManager.class);
				
				FutureTimerInstance futureTimerInstance = (FutureTimerInstance) getSelectedObject();
				futureTimerInstance = (FutureTimerInstance) adManager.getEntity(futureTimerInstance);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), futureTimerInstance.getLotId());
				futureTimerManager.resumeTimers(lot.getObjectRrn(), Arrays.asList(futureTimerInstance), Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.qtime_resume_success"));
				this.refresh();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}
	
	public boolean suspenedAdapter() {
		try {
			Object[] elements = null;
			if (getSelectedObject() != null) {
				elements = new Object[] {getSelectedObject()};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else {
				List<ADBase> baseList = new ArrayList<ADBase>();
				for (int i = 0; i < elements.length; i++) {
					ADBase base = (ADBase)elements[i];
					baseList.add(base);
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				FutureTimerManager futureTimerManager = Framework.getService(FutureTimerManager.class);
				
				FutureTimerInstance futureTimerInstance = (FutureTimerInstance) getSelectedObject();
				futureTimerInstance = (FutureTimerInstance) adManager.getEntity(futureTimerInstance);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), futureTimerInstance.getLotId());
				futureTimerManager.suspendTimers(lot.getObjectRrn(), Arrays.asList(futureTimerInstance), Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.qtime_suspened_success"));
				this.refresh();
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}
	
	public boolean clearAdapter() {
		try {
			Object[] elements = null;
			if (getSelectedObject() != null) {
				elements = new Object[] {getSelectedObject()};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()))) {
					List<ADBase> baseList = new ArrayList<ADBase>();
					for (int i = 0; i < elements.length; i++) {
						ADBase base = (ADBase)elements[i];
						baseList.add(base);
					}
					FutureTimerInstance futureTimerInstance = (FutureTimerInstance) getSelectedObject();
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.clearLotFutureTimerInstanceExpireTime(futureTimerInstance, Env.getSessionContext());
					refreshBatchDelete(baseList);
					this.refresh();
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
		return false;
	}
	
}
