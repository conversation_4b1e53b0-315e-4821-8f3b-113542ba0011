package com.glory.mes.wip.advance.future.hisquery;

import org.eclipse.swt.graphics.Image;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.glory.mes.wip.his.FutureActionHis;

public class FutureActionHisQueryItemAdapter extends ListItemAdapter<FutureActionHis> {

	@Override
	public Image getImage(Object object) {
		FutureActionHis his = (FutureActionHis) object;
		if (FutureAction.ACTION_HOLD.equals(his.getAction())) {
			return SWTResourceCache.getImage("hold");
		} else if (FutureAction.ACTION_NOTE.equals(his.getAction())) {
			return SWTResourceCache.getImage("note");
		} else if (FutureAction.ACTION_STEPTIMER.equals(his.getAction()) 
				|| FutureAction.ACTION_MULTISTEPTIMER.equals(his.getAction()) 
				|| FutureAction.ACTION_MULTIPROCEDURETIMER.equals(his.getAction())) {
			return SWTResourceCache.getImage("timer_start");
		} else if (FutureAction.ACTION_MERGE.equals(his.getAction())) {
			return SWTResourceCache.getImage("hold");
		} else if (FutureAction.ACTION_SAMPLING.equals(his.getAction())) {
			return SWTResourceCache.getImage("edc_lotsample");
		} else if (FutureAction.ACTION_CHANGEFLOW.equals(his.getAction())) {
			return SWTResourceCache.getImage("schedule");
		} 
		return null;
	}
	
    @Override
	public String getText(Object object, String id) {
    	if ("action".equals(id)) { 
    		FutureActionHis action = (FutureActionHis) object;
	    	if (FutureAction.ACTION_NOTE.equals(action.getAction())) {
				return "Note";
			} else if (FutureAction.ACTION_HOLD.equals(action.getAction())) {
				if (action.getLotRrn() == null) {
					return "ProcessHold";
				} else {
					return "LotHold";
				} 
			} else if (FutureAction.ACTION_MULTISTEPTIMER.equals(action.getAction())) {
				return "MultiStepTimer";
			} else if (FutureAction.ACTION_MULTIPROCEDURETIMER.equals(action.getAction())) {
				return "MultiProcTimer";
			} else if (FutureAction.ACTION_STEPTIMER.equals(action.getAction())) {
				return "StepTimer";
			} else if (FutureAction.ACTION_MERGE.equals(action.getAction())) {
				return "Merge";
			} else if (FutureAction.ACTION_SAMPLING.equals(action.getAction())) {
				return "Sampling";
			} else if (FutureAction.ACTION_CHANGEFLOW.equals(action.getAction())) {
				return "ChangeFlow";
			}  
    	} else if ("holdCode".equals(id)
    			|| "holdReason".equals(id)) {
    		if (object instanceof FutureHold) {
    			return super.getText(object, id);
			} else if (object instanceof FutureTimer) {
				return super.getText(object, id);
			}
			return "";
    	} else if ("endStepName".equals(id)) {
    		if (object instanceof FutureMultiStepTimer
    				|| object instanceof FutureMultiProcedureTimer) {
    			return super.getText(object, id);
			} 
			return "";
    	} else if ("processName".equals(id) 
    			|| "processVersion".equals(id)
    			|| "endProcedureName".equals(id)
    			|| "endProcedureVersion".equals(id)) {
    		if (object instanceof FutureMultiProcedureTimer) {
    			return super.getText(object, id);
			} 
			return "";
    	} else if ("timerDuration".equals(id)) {
    		if (object instanceof FutureTimer) {
    			return super.getText(object, id);
			} 
			return "";
    	}
    	return super.getText(object, id);
    }
}
