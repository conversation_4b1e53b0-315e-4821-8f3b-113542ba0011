package com.glory.mes.wip.advance.future.query.startedtimer;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.client.LotManager;
import com.glory.framework.core.exception.ExceptionBundle;

public class ChangeExpireTimeDialog extends EntityDialog {

	public ChangeExpireTimeDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}
	
	protected void createFormContent(Composite composite) {
		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		managedForm = new ManagedForm(toolkit, sForm);
		final IMessageManager mmng = managedForm.getMessageManager();
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		
		List<ADField> adfields = new ArrayList<ADField>();
		for (ADField adfield : table.getFields()) {
			if ("expireTime".equals(adfield.getName()) || "actionComment".equals(adfield.getName()) ) {
				adfields.add(adfield);
			}
		}
		
		EntityForm itemForm = new EntityForm(body, SWT.NONE, adObject, adfields, 1, mmng);
		itemForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		getDetailForms().add(itemForm);
		
	}

	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					FutureTimerInstance futureTimerInstance = (FutureTimerInstance) getAdObject();
					
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.changeLotFutureTimerInstanceExpireTime(futureTimerInstance, Env.getSessionContext());
	
					ADManager entityManager = Framework.getService(ADManager.class);
					futureTimerInstance.setActionComment(null);
					setAdObject(entityManager.getEntity(futureTimerInstance));
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}
	
}
