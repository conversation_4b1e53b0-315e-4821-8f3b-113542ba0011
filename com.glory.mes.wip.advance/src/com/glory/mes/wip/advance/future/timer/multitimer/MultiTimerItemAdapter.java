package com.glory.mes.wip.advance.future.timer.multitimer;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.resource.ImageDescriptor;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.adapter.StepStateItemAdapter;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.future.FutureTimer;
import com.google.common.collect.Lists;

public class MultiTimerItemAdapter extends StepStateItemAdapter {

	private static final Logger logger = Logger.getLogger(MultiTimerItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];

	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState) {
			List<Object> children = new ArrayList<Object>();
			
			StepState stepState = (StepState) object;
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<String> timeTypes = new ArrayList<String>();
				timeTypes.add(FutureAction.ACTION_MULTISTEPTIMER);
				
				if (stepState.getOrgRrn() == 0) {
					stepState.setOrgRrn(Env.getOrgRrn());
				}
				
				//Timer��ʼλ�ü��
				List<? extends FutureTimer> startTimers = lotManager.getFutureTimerByStart(stepState, timeTypes, null);
				for (FutureTimer timer : startTimers) {
					if (timer.getParentRrn() == null || timer.getObjectRrn().compareTo(timer.getParentRrn()) == 0) {
						children.add(timer);
					}
				}

				//Timer����λ�ü��
				List<Long> objectRrns = Lists.newArrayList();
				List<? extends FutureTimer> endTimers = lotManager.getFutureTimerByEnd(stepState, timeTypes);
				for (FutureTimer timer : endTimers) {
					if (timer instanceof FutureMultiStepTimer) {
						FutureMultiStepTimer mTimer = (FutureMultiStepTimer)timer;
						EndTimer endTimer = new EndTimer(mTimer);
						children.add(endTimer);
						objectRrns.add(timer.getObjectRrn());
					}
				}
				
				//Timerδ����ͣλ�ü��
				ADManager adManager = Framework.getService(ADManager.class);
				List<FutureMultiStepTimer> futureHoldTimers = adManager.getEntityList(Env.getOrgRrn(), FutureMultiStepTimer.class,
						Env.getMaxResult(), " timerAction = 'SHOLD' AND actionStepStateName = '" + stepState.getName() + "'", null);
				for (FutureMultiStepTimer mTimer : futureHoldTimers) {
					if (mTimer.getParentRrn() == null || mTimer.getObjectRrn().compareTo(mTimer.getParentRrn()) == 0) {
						FutureHoldTimer futureHoldTimer = new FutureHoldTimer(mTimer);
						children.add(futureHoldTimer);
					}
				}
				return children.toArray();
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		} 
		return EMPTY;
	}

	/**
	 * ���StepState���Ƿ��ж�Ӧ��Timer
	 */
	public boolean hasChildren(Object object) {
		if (object instanceof StepState) {
			StepState stepState = (StepState)object;
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<String> timeTypes = new ArrayList<String>();
				timeTypes.add(FutureAction.ACTION_MULTISTEPTIMER);
				//Timer��ʼλ�ü��
				if (stepState.getOrgRrn() == 0) {
					stepState.setOrgRrn(Env.getOrgRrn());
				}
				List<? extends FutureTimer> startTimers = lotManager.getFutureTimerByStart(stepState, timeTypes, null);
				if (startTimers.size() > 0) {
					return true;
				}

				//Timer����λ�ü��
				List<? extends FutureTimer> endTimers = lotManager.getFutureTimerByEnd(stepState, timeTypes);
				if (endTimers.size() > 0) {
					return true;
				}
				
				//Timerδ����ͣλ�ü��
				ADManager adManager = Framework.getService(ADManager.class);
				List<FutureMultiStepTimer> futureHoldTimers = adManager.getEntityList(Env.getOrgRrn(), FutureMultiStepTimer.class,
						Env.getMaxResult(), " timerAction = 'SHOLD' AND actionStepStateName = '" + stepState.getName() + "'", null);
				if (futureHoldTimers.size() > 0) {
					return true;
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		}
		return false;
	}
	
	@Override
	public ImageDescriptor getImageDescriptor(Object object, String id) {
		if (object instanceof StepState) {
			StepState state = (StepState)object;
			if (state.getIsTransitionRework()) {
				return SWTResourceCache.getImageDescriptor("rework");
			}
			return SWTResourceCache.getImageDescriptor("step");
		} else if (object instanceof FutureMultiStepTimer) {
			return SWTResourceCache.getImageDescriptor("timer_start");
		} else if (object instanceof EndTimer) {
			return SWTResourceCache.getImageDescriptor("timer_end");
		} else if (object instanceof FutureHoldTimer) {
			return SWTResourceCache.getImageDescriptor("hold");
		}
		return null;
	}

	@Override
	public String getText(Object element) {
		if (element instanceof StepState) {
			StepState stepState = (StepState) element;
			Step step = getStep(stepState);
			if (step.getDescription() != null) {
				return step.getName() + "<" + step.getDescription() + ">";
			}
			return step.getName();
		} else if (element instanceof FutureMultiStepTimer) {
			FutureMultiStepTimer timer = (FutureMultiStepTimer) element;
			String description = (timer.getDescription() == null
					|| "".equals(timer.getDescription().trim()) ? "" : " <" + timer.getDescription() + ">");
			return timer.getName() + description;
		} else if (element instanceof EndTimer) {
			EndTimer endTime = (EndTimer)element;
			String description = (endTime.getDescription() == null
					|| "".equals(endTime.getDescription().trim()) ? "" : " <" + endTime.getDescription() + ">");
			return ((EndTimer) element).getTimerName() + description;
		} else if (element instanceof FutureHoldTimer) {
			FutureHoldTimer futureHoldTimer = (FutureHoldTimer)element;
			String description = (futureHoldTimer.getDescription() == null
					|| "".equals(futureHoldTimer.getDescription().trim()) ? "" : " <" + futureHoldTimer.getDescription() + ">");
			return ((FutureHoldTimer) element).getTimerName() + description;
		}
		return "";
	}
	
}

/**
 * ��������Timer�Ľ����ڵ�
 */
class EndTimer {
	
	private long timerRrn;
	private String timerName;
	private String endStepStateName;
	private String description;
	
	public EndTimer(FutureMultiStepTimer mTimer) {
		this.timerRrn = mTimer.getObjectRrn();
		this.timerName = mTimer.getName();
		this.description = mTimer.getDescription();
		this.endStepStateName = mTimer.getEndStepStateName();
	}
	
	public long getTimerRrn() {
		return timerRrn;
	}
	
	public void setTimerRrn(long timerRrn) {
		this.timerRrn = timerRrn;
	}
	
	public String getTimerName() {
		return timerName;
	}
	
	public void setTimerName(String timerName) {
		this.timerName = timerName;
	}
	
	public String getEndStepStateName() {
		return endStepStateName;
	}

	public void setEndStepStateName(String endStepStateName) {
		this.endStepStateName = endStepStateName;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
}

/**
 * ��������Timer��δ����ͣ�ڵ�
 */
class FutureHoldTimer {
	
	private long timerRrn;
	private String timerName;
	private String actionStepStateName;
	private String description;
	
	public FutureHoldTimer(FutureMultiStepTimer pTimer) {
		this.timerRrn = pTimer.getObjectRrn();
		this.timerName = pTimer.getName();
		this.description = pTimer.getDescription();
		this.actionStepStateName = pTimer.getActionStepStateName();
	}
	
	public long getTimerRrn() {
		return timerRrn;
	}
	
	public void setTimerRrn(long timerRrn) {
		this.timerRrn = timerRrn;
	}
	
	public String getTimerName() {
		return timerName;
	}
	
	public void setTimerName(String timerName) {
		this.timerName = timerName;
	}
	
	public String getActionStepStateName() {
		return actionStepStateName;
	}

	public void setActionStepStateName(String actionStepStateName) {
		this.actionStepStateName = actionStepStateName;
	}

	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		this.description = description;
	}
}