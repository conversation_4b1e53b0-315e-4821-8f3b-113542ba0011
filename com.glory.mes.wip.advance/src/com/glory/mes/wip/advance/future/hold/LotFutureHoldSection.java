package com.glory.mes.wip.advance.future.hold;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.advance.future.FutureActionMediator;
import com.glory.mes.wip.advance.future.LotFutureActionSection;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotFutureHoldSection extends LotFutureActionSection {
	
	private static final Logger logger = Logger.getLogger(LotFutureHoldSection.class);

	protected AuthorityToolItem itemNote;
	protected AuthorityToolItem itemDelete;

	public static String KEY_HOLD = "hold";
	public static String KEY_DELETE = "delete";
	
	public LotFutureHoldSection() {
		super();
	}

	public LotFutureHoldSection(LotFutureHoldTreeManager manager, ADTable table) {
		super(manager, table);
	}
	
	@Override
	public void initAdObject() {
		FutureHold futureNote = new FutureHold();
		futureNote.setOrgRrn(Env.getOrgRrn());
		setAdObject(futureNote);
		refresh();
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		this.adObject = adObject;
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemHold(ToolBar tBar) {
		itemNote = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_HOLD);
		itemNote.setAuthEventAdaptor(this::holdAdapter);
		itemNote.setText(Message.getString("wip.futurehold")); 
		itemNote.setImage(SWTResourceCache.getImage("hold"));
//		itemNote.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				holdAdapter();
//			}
//		});
	}
	
	protected void createToolItemDelete(ToolBar tBar) {
		itemDelete = new AuthorityToolItem(tBar, SWT.PUSH, getTable().getAuthorityKey() + "." + KEY_DELETE);
		itemDelete.setAuthEventAdaptor(this::deleteAdapter);
		itemDelete.setText(Message.getString(ExceptionBundle.bundle.CommonDelete()));
		itemDelete.setImage(SWTResourceCache.getImage("delete"));
//		itemDelete.addSelectionListener(new SelectionAdapter() {
//			@Override
//			public void widgetSelected(SelectionEvent event) {
//				deleteAdapter();
//			}
//		});
	}

	@Override
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		final IMessageManager mmng = form.getMessageManager();
		
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		
		sashForm = new MDSashForm(client, SWT.NULL);
		toolkit.adapt(sashForm, false, false);
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
 		lotFlowSection = new LotFutureFlowSection(table, lotMediator, manager);
		lotFlowSection.createContents(form, sashForm);
		
		for (ADTab tab : getTable().getTabs()) {
			EntityForm itemForm = new EntityForm(sashForm, SWT.NULL, tab, mmng);
			getDetailForms().add(itemForm);
		}		
	}
	
	protected void holdAdapter(SelectionEvent event) {
		form.getMessageManager().removeAllMessages();
		try {
			if(getAdObject() != null) {
				if(this.currentStepState == null || this.currentStepState.getObjectRrn() == null) {
					UI.showError(Message.getString("wip.futurehold_nostep"));
					return;
				}
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					FutureHold fn = (FutureHold)getAdObject();
					if(fn.getLotRrn() == null) {
						fn.setLotRrn(currentLot.getObjectRrn());
						fn.setLotId(currentLot.getLotId());
					}
					if (fn.getStepStateName() == null) {
						fn.setStepStateName(currentStepState.getName());
					}
										
					fn.setOrgRrn(Env.getOrgRrn());
					fn.setPath(currentStepState.getPath());
					fn.setProcessName(currentLot.getProcessName());
					fn.setProcedureName(currentStepState.getProcessDefinition().getName());
					fn.setProcedureVersion(currentStepState.getProcessDefinition().getVersion());
					fn.setStepName(currentStepState.getUsedStep().getName());
					
					LotManager lotManager = Framework.getService(LotManager.class);
					ADBase obj = lotManager.saveFutureAction((FutureAction)getAdObject(), Env.getSessionContext());

					ADManager entityManager = Framework.getService(ADManager.class);
					obj = entityManager.getEntity(obj);
					
					setAdObject(obj);
					UI.showInfo(Message.getString("wip.futurehold_holdsuccessed"));
					lotMediator.notifyLotFlowTreeField(FutureActionMediator.REFRESH, obj);
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			logger.error("Error at FutureActionSection : holdAdapter" + e.getStackTrace());
			return;
		}
	}
	
	protected void deleteAdapter(SelectionEvent event) {
		if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
			FutureHold futureHold = (FutureHold)getAdObject();
			if(futureHold.getRunCardId() != null) {
				UI.showInfo(Message.getString("wip.ein_future_hold_no_delete"));
				return;
			}
		}
		super.deleteAdapter();
	}
	
	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}
	

	@Override
	public void statusChanged(String newStatus){
		if (newStatus.equalsIgnoreCase("FutureHold")||newStatus.trim().equalsIgnoreCase("StepState")){
			itemNote.setEnabled(true);
		} else {
			itemNote.setEnabled(false);
		}
	
	}

}
