package com.glory.mes.wip.advance.future.timer.multitimer;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.exception.WipExceptionBundle;

public class MultiTimerAddEndEntityDialog extends EntityDialog {
	
	public MultiTimerAddEndEntityDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}
	
	@Override
    protected Control buildView(Composite parent) {
		setMessage(Message.getString(WipExceptionBundle.bundle.ImproveTheDetailsOfTheMultiStepTimer()));
		return super.buildView(parent); 
	}
	
	@Override
	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}
}
