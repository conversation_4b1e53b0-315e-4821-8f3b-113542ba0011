package com.glory.mes.wip.advance.future.timer.color;

import java.util.Date;

import org.eclipse.swt.graphics.Color;

import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.advance.future.query.TimerInstanceQueryItemAdapter;

public class ColorTimerInstanceItemAdapter extends TimerInstanceQueryItemAdapter {
	 
	
	@Override
	public Color getBackground(Object element, String id) {
		try {
			FutureTimerInstance futureTimerInstance = (FutureTimerInstance) element;
			//�����뵱ǰʱ����������м���
			Date sysDate = new Date();
			Long time = futureTimerInstance.getExpireTime().getTime() - sysDate.getTime();
			return ColorTimerInstance.calculateTimeDiff(time);
		} catch(Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    	return null;
	}
	
}
