package com.glory.mes.wip.advance.future.multihold;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.AuthorityToolItem;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.nattable.CheckBoxTableViewerManager;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.part.PartFlowSection;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.advance.future.multihold.FutureHoldDialog;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotReserved;
import com.glory.framework.core.exception.ExceptionBundle;

public class MultiLotFutureHoldSection extends EntitySection {

	protected AuthorityToolItem itemNote;
	protected AuthorityToolItem itemDelete;

	public final static String ADTBALE = "WIPADVLotFutureHold";
	private QueryTableForm queryTableForm;

	protected PartFlowSection partFlowSection;
	public final static String FUTURE_HOLD_TABLE = "WIPLotFutureHoldMultiList";
	private ListTableManager futureHoldLotManager;
	private CheckBoxTableViewerManager tableManager;

	public static String KEY_HOLD = "hold";
	public static String KEY_DELETE = "delete";
	
	public MultiLotFutureHoldSection(ADTable adTable,CheckBoxTableViewerManager tableManager) {
		super(adTable);
		setAdObject(new LotReserved());
		this.tableManager = tableManager;
	}
	
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemHold(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemHold(ToolBar tBar) {
		itemNote = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_HOLD);
		itemNote.setText(Message.getString("wip.futurehold")); 
		itemNote.setImage(SWTResourceCache.getImage("hold"));
		itemNote.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				holdAdapter();
			}
		});
	}
	
	protected void createToolItemDelete(ToolBar tBar) {
		itemDelete = new AuthorityToolItem(tBar, SWT.PUSH, tableManager.getADTable().getAuthorityKey() + "." + KEY_DELETE);
		itemDelete.setText(Message.getString(ExceptionBundle.bundle.CommonDelete())); 
		itemDelete.setImage(SWTResourceCache.getImage("delete"));
		itemDelete.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				deleteAdapter();
			}
		});
	}

	@Override
	protected void createSectionContent(Composite client) {
		
		final FormToolkit toolkit = form.getToolkit();
		mmng = getMessageManager();

		MDSashForm sashForm = new MDSashForm(client, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);

		partFlowSection = new PartFlowSection(table);
		partFlowSection.createContents(form, sashForm);
		partFlowSection.getForm().addSelectionChangedListener(new ISelectionChangedListener() {
			@Override
			public void selectionChanged(SelectionChangedEvent e) {
				partFlowSection.getFlowList();
				if(partFlowSection.getFlowList() != null && partFlowSection.getFlowList().size() > 0 ) {
					queryAdapterByClick();
				}
			}
		});
		
		ADManager admanager;
		try {
			admanager = Framework.getService(ADManager.class);
			ADTable futoreHoldTable = admanager.getADTable(Env.getOrgRrn(), FUTURE_HOLD_TABLE);
			futureHoldLotManager = new ListTableManager(futoreHoldTable, true);
			futureHoldLotManager.newViewer(sashForm);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
	}
	
	@SuppressWarnings("unused")
	protected void holdAdapter() {
		form.getMessageManager().removeAllMessages();
		try {
			StepState stepState = null;
			ProcedureState procedureState;
			if (partFlowSection.getForm().saveToObject()){
				List<Node> nodes =  partFlowSection.getForm().getFlowList();
				if (nodes != null) {
					for (Node node : nodes) {
						if (node instanceof ProcedureState) {
							procedureState = (ProcedureState)node;
						}
						if (node instanceof StepState) {
							stepState = (StepState)node;
						}
					}
				}
			}
			if (stepState == null) {
				UI.showError(Message.getString("wip_not_select_step"));
				return;
			}
			
			List<Object> objects = tableManager.getCheckedObject();
			if (objects.size() == 0) {
				UI.showError(Message.getString("wip_not_select_lot"));
				return;
			}
			FutureHold futureHold = new FutureHold();
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adtable = adManager.getADTable(Env.getOrgRrn(), ADTBALE);
			FutureHoldDialog dialog = new FutureHoldDialog(UI.getActiveShell(),adtable);
			if (dialog.open()==0){
				 futureHold= (FutureHold)dialog.getObject();
			} else {
				return;
			}
			List<FutureAction> futureHolds = new ArrayList<FutureAction>();
			for (Object object : objects) {
				Lot lot = (Lot)object;
				FutureAction futureHoldlist = new FutureHold();
				futureHoldlist =(FutureAction)futureHold.clone();
				futureHoldlist.setLotRrn(lot.getObjectRrn());
				futureHoldlist.setLotId(lot.getLotId());
				futureHoldlist.setOrgRrn(Env.getOrgRrn());
				futureHoldlist.setStepStateName(stepState.getStepName());
				futureHoldlist.setPath(stepState.getPath());
				futureHoldlist.setProcessName(lot.getProcessName());
				futureHoldlist.setProcedureName((stepState.getProcessDefinition().getName()));
				futureHoldlist.setProcedureVersion(stepState.getProcessDefinition().getVersion());
				futureHoldlist.setStepName(stepState.getUsedStep().getName());
				
				futureHolds.add(futureHoldlist);
			}
			LotManager lotManager = Framework.getService(LotManager.class);
			futureHolds = lotManager.saveMultiFutureAction(futureHolds,Env.getSessionContext());
			if (futureHolds.size() != 0) {
				UI.showInfo(Message.getString("wip.futurehold_holdsuccessed"));
				queryAdapterByClick();
			}
			
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void deleteAdapter() {
		try {
			List<Object> objects = futureHoldLotManager.getCheckedObject();
			if (objects.size() == 0) {
				UI.showError(Message.getString("wip.futurehold_not_select_lot"));
				return;
			}
			boolean flag = UI.showConfirm(Message.getString("wip.futurehold_is_delete_select_lot"));
			if(flag) {
				List<FutureAction> futureHolds = new ArrayList<FutureAction>();
				for (Object object : objects) {
					FutureHold futureHoldlist = (FutureHold)object;
					futureHolds.add(futureHoldlist);
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				lotManager.deleteMultiFutureAction(futureHolds,Env.getSessionContext());
				UI.showInfo(Message.getString("wip.futurehold_delete_secussed"));
				queryAdapterByClick();
			}else {
				return;
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void queryAdapterByClick() {
		form.getMessageManager().removeAllMessages();
		try {
			
			ADManager adManager = Framework.getService(ADManager.class);
			if (partFlowSection.getForm().saveToObject()){
				List<Node> nodes = partFlowSection.getForm().getFlowList();
				for (Node node : nodes) {
					if (node instanceof StepState) {
						String stepName = ((StepState)node).getName();
						List<FutureHold> futureHoldLots = adManager.getEntityList(Env.getOrgRrn(), FutureHold.class, Env.getMaxResult(), 
								" stepName = '" + stepName + "' AND lotRrn != null ", "");
						
						futureHoldLotManager.setInput(null);
						futureHoldLotManager.setInput(futureHoldLots);
					} else {
						futureHoldLotManager.setInput(null);
					}
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	public void setObject(LotReserved reserved, Long capa) {
		setAdObject(reserved);
		refresh();
	}
	
	public void removeQueryTableFormSelection() {
		queryTableForm.getTableManager().refresh();
	}

	public void setQueryTableForm(QueryTableForm queryTableForm) {
		this.queryTableForm = queryTableForm;
	}
}
