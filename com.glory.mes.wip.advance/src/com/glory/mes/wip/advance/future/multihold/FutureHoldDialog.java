package com.glory.mes.wip.advance.future.multihold;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.mes.wip.future.FutureHold;

public class FutureHoldDialog extends BaseTitleDialog {
	
	public ADTable adtable;
	public EntityForm form;
	public Object object;

	public FutureHoldDialog(Shell parentShell) {
		super(parentShell);
	}

	public FutureHoldDialog(Shell parent, ADTable adtable){
		this(parent);
		this.adtable = adtable;
	}
		
	@Override
	protected Control buildView(Composite parent) {
		FormToolkit toolkit = new FormToolkit(UI.getActiveShell().getDisplay());
		
		setTitleImage(SWTResourceCache.getImage("trackin-dialog"));
	    setTitle(Message.getString("wip.hold_lots"));
	    setMessage(Message.getString("wip.hold_lots_Info"));
	    
	    Composite content = toolkit.createComposite(parent);
	    content.setLayoutData(new GridData(GridData.FILL_BOTH));
	    content.setLayout(new GridLayout(1, false));
	    
	    form = new FutureHoldDialogForm(content, SWT.NULL, new FutureHold());  
	    form.setLayoutData(new GridData(GridData.FILL_BOTH));
	    return parent;
	}
	
	public Object getObject() {
		return form.getObject();
	}
	
	@Override
	protected void okPressed() {
		if (form.saveToObject()) {
			super.okPressed();
		} else {
			return;
		}
	}
	
}