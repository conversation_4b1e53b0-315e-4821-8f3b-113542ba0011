package com.glory.mes.wip.advance.future.timer.step;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

/**
 * ��������ʱ��,������������Ч
 */
public class StepTimerEditor extends EntityEditor {

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.timer.step.StepTimerEditor";
	

	@Override
	protected void createBlock(ADTable adTable) {
		block = new StepTimerBlock(new ListTableManager(adTable));
	}
	
}
