package com.glory.mes.wip.advance.future;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.exception.ExceptionBundle;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.LotSection;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.lot.flow.LotFlowTreeManager;
import com.glory.mes.wip.model.Lot;

public abstract class LotFutureActionSection extends LotSection {
	
	private static final Logger logger = Logger.getLogger(LotFutureActionSection.class);
	protected FutureActionMediator lotMediator;
	protected SashForm sashForm;
	
	protected LotFlowTreeManager manager;
	protected LotFlowSection lotFlowSection;
	
	protected StepState currentStepState;
	protected Lot currentLot;
	
	public LotFutureActionSection() {
		super();
	}
	
	public LotFutureActionSection(ADTable table) {
		this(null, table);
	}

	public LotFutureActionSection(LotFlowTreeManager manager, ADTable table) {
		super(table);
		this.manager = manager;
		createMeditor();
	}
	
	protected void createMeditor() {
		lotMediator = new FutureActionMediator(this);		
	}
	
	@Override
	public void initAdObject() {
		FutureNote futureNote = new FutureNote();
		futureNote.setOrgRrn(Env.getOrgRrn());
		setAdObject(futureNote);
		refresh();
	}
	
	// �½�LotFutureNoteTreeManager����
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		final IMessageManager mmng = form.getMessageManager();
		
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		
		sashForm = new MDSashForm(client, SWT.NULL);
		toolkit.adapt(sashForm, false, false);
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);
 		lotFlowSection = new LotFlowSection(table, lotMediator, manager);
		lotFlowSection.createContents(form, sashForm);
		
		for (ADTab tab : getTable().getTabs()) {
			EntityForm itemForm = new EntityForm(sashForm, SWT.NULL, tab, mmng);
			getDetailForms().add(itemForm);
		}		
	}

	protected void createToolItemDelete(ToolBar tBar) {
		super.createToolItemDelete(tBar);
	}
	
	protected void deleteAdapter() {
		try {
			if (getAdObject() != null && getAdObject().getObjectRrn() != null) {
//				boolean confirmDelete = UI.showConfirm(Message
//						.getString("common.confirm_delete"));
//				if (confirmDelete) {
				ADManager entityManager = Framework
						.getService(ADManager.class);
				FutureAction futureAction = (FutureAction) entityManager.getEntity(getAdObject());
				if (futureAction == null) {
					UI.showInfo(Message.getString(ExceptionBundle.bundle.EntityNotExistOrDelete().getErrorCode()));
					return;
				}
				entityManager.deleteEntity(getAdObject(),Env.getSessionContext());
				lotMediator.notifyLotFlowTreeField(FutureActionMediator.DELETE, getAdObject());
				setAdObject(createAdObject());
				refresh();
//				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}

	protected void refreshAdapter() {
		try {
			form.getMessageManager().removeAllMessages();
			ADBase adBase = getAdObject();
			if (adBase != null && adBase.getObjectRrn() != null) {
				ADManager entityManager = Framework.getService(ADManager.class);
				setAdObject(entityManager.getEntity(adBase));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
		refresh();
	}
	
	public void setCurrentStepState(StepState stepState ) {
		currentStepState = stepState;
//		initAdObject();
	}
	
	public StepState getCurrentStepState() {
		return currentStepState;
	}

	public void setCurrentLot(Lot lot) {
		this.currentLot = lot;
		lotFlowSection.setAdObject(currentLot);
		initAdObject();
	}
	
	public Lot getCurrentLot() {
		return currentLot;
	}

	@Override
	protected void createSectionTitle(Composite client) {}
	
	@Override
	public void setFocus() {
		if(lotFlowSection != null) {
			lotFlowSection.setFocus();
		}
	}
}
