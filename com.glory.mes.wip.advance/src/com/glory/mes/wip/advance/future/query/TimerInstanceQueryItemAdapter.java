package com.glory.mes.wip.advance.future.query;

import org.eclipse.swt.graphics.Image;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.prd.workflow.action.exe.FutureMultiProcedureTimerInstance;
import com.glory.mes.prd.workflow.action.exe.FutureMultiStepTimerInstance;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.future.FutureAction;

public class TimerInstanceQueryItemAdapter extends ListItemAdapter<FutureAction> {
	 
	@Override
	public Image getImage(Object object) {
		if (object instanceof FutureTimerInstance) {
			return SWTResourceCache.getImage("timer_started");
		}
		return null;
	}
	
    @Override
	public String getText(Object object, String id) {
    	if ("endStepName".equals(id)) {
    		if (object instanceof FutureMultiStepTimerInstance) {
     			return super.getText(object, id);
 			} 
     		if (object instanceof FutureMultiProcedureTimerInstance) {
     			return super.getText(object, id);
 			} 
 			return "";
     	} 
    	if ("endProcedureName".equals(id)
     			|| "endProcedureVersion".equals(id)) {
     		if (object instanceof FutureMultiProcedureTimerInstance) {
     			return super.getText(object, id);
 			} 
 			return "";
     	} 
    	
    	return super.getText(object, id);
    }
	
}
