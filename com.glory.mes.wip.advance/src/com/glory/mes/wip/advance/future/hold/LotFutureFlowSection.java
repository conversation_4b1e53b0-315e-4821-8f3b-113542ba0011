package com.glory.mes.wip.advance.future.hold;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.flow.LotFlowSection;

public class LotFutureFlowSection extends LotFlowSection{

	public LotFutureFlowSection(ADTable table, LotMediator lotMediator, TreeViewerManager treeManager) {
		super(table, lotMediator, treeManager);
	}

	@Override
	protected void createSectionContent(Composite client) {
		final IMessageManager mmng = form.getMessageManager();
		itemForm = new LotFutureFlowForm(client, SWT.NONE, table,
				lotMediator, mmng, treeManager);
		itemForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		getDetailForms().add(itemForm);
	}
}
