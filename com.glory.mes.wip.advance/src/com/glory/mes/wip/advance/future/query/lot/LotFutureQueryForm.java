package com.glory.mes.wip.advance.future.query.lot;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.FieldType;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TableListField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.security.model.ADUser;
import com.glory.mes.ras.eqp.Equipment;
import com.glory.mes.wip.advance.future.query.FutureActionQueryFixTableManager;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.model.Lot;

public class LotFutureQueryForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(LotFutureQueryForm.class);

	private LotFutureQuerySection futureSection;
	private Lot lot;
	private ListTableManager tableManager;
	private TableListField field;
	
	public LotFutureQueryForm(LotFutureQuerySection futureSection, Composite parent, int style, ADTab tab,
			IMessageManager mmng) {
		super(parent, style, tab, mmng);
		this.futureSection = futureSection;
	}

	@Override
	public IField getField(ADField adField) {
		String displayType = adField.getDisplayType();
		String name = adField.getName();

		if (FieldType.TABLELIST.equalsIgnoreCase(displayType)) {
			try {
				ADManager entityManager = Framework.getService(ADManager.class);
				ADRefTable refTable = new ADRefTable();
				refTable.setObjectRrn(adField.getReftableRrn());
				refTable = (ADRefTable)entityManager.getEntity(refTable);
				if (refTable == null || refTable.getTableRrn() == null) {
					return null;
				}
				ADTable adTable = entityManager.getADTable(refTable.getTableRrn());
				tableManager = new FutureActionQueryFixTableManager(adTable);
				field = createTableListField(name, null, tableManager);
//				field.setHeight(20);
				
				addField(name, field);
				
				return field;
				
			} catch (Exception e) {
				logger.error("LotFutureQueryForm : Init tablelist", e);
			}
		} 
		return super.getField(adField);
		
	}

	@Override
    public void createForm(){
		super.createForm();
		
		if (tableManager != null) {
			tableManager.addSelectionChangedListener(new ISelectionChangedListener() {
				@Override
				public void selectionChanged(SelectionChangedEvent event) {
					StructuredSelection ss = (StructuredSelection)event.getSelection();
					if (ss != null && ss.getFirstElement() != null) {
						futureSection.statusChanged(((FutureAction)ss.getFirstElement()));
					} 
				}
			});
		}
	}
	
	@Override
	public void refresh() {
		super.refresh();
		try {
			if (lot != null && lot.getObjectRrn() != null) {
				LotManager lotManager = Framework.getService(LotManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				List<FutureAction> futureActions = lotManager.getLotFutureActions(
						this.getLot(), Integer.valueOf(this.getLot().getAttribute5().toString()), futureSection.getActionTypes());
				if(CollectionUtils.isNotEmpty(futureActions)) {
					//������е��û�
					List<ADUser> toUsers = adManager.getEntityList(Env.getOrgRrn(), ADUser.class);
					Map<String, ADUser> department = new HashMap<>(); 
					if(CollectionUtils.isNotEmpty(toUsers)) {
						//�û���Ψһ�����map
						for(ADUser aDUser : toUsers) {
					        if(!department.containsKey(aDUser.getUserName())) {
					        	department.put(aDUser.getUserName(), aDUser);
					        }
						}
						//�жϲ������û����Ų��������벿��
						for(FutureAction futureAction : futureActions) {
							if(department.containsKey(futureAction.getUpdatedBy())) {
								//д�����Ա�û��벿��
								futureAction.setAttribute1(department.get(futureAction.getUpdatedBy()).getDescription().toString());
								if (!StringUtil.isEmpty(department.get(futureAction.getUpdatedBy()).getDepartment())) {
									futureAction.setAttribute2(department.get(futureAction.getUpdatedBy()).getDepartment().toString());
								}								
							}
						}
					}
				}
				tableManager.setInput(futureActions);
			} else {
				tableManager.setInput(new ArrayList<FutureAction>());
			}
		} catch (Exception e) {
			logger.error("LotFutureQueryForm : refresh()", e);
		}
	}

	public Object getSelectedObject() {
		return tableManager.getSelectedObject();
	}
	
	public void refreshDelete(Object object) {
		tableManager.remove(object);
	}
	
	public Lot getLot() {
		return lot;
	}

	public void setLot(Lot lot) {
		this.lot = lot;
	}
}
