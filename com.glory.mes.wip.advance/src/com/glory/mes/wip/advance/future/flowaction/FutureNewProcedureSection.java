package com.glory.mes.wip.advance.future.flowaction;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.procedure.flow.ProcedureFlowSection;
import com.glory.mes.prd.procedure.flow.ProcedureFlowTreeField;

public class FutureNewProcedureSection extends ProcedureFlowSection {

	public FutureNewProcedureSection(ADTable table) {
		super(table);
	}
	
	public FutureNewProcedureSection(ADTable table, ProcedureFlowTreeField treeField) {
		super(table, treeField);
	}
	
	public Procedure getProcedure() {
		RefTableField procedureField = (RefTableField) getProcedureIField();
		if (procedureField == null) {
			return null;
		}
		
		XCombo combo = procedureField.getComboControl();
		return (Procedure) combo.getData();
	}

}
