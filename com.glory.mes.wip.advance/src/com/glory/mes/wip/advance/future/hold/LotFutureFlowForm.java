package com.glory.mes.wip.advance.future.hold;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.viewers.TreeViewerManager;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.lot.LotMediator;
import com.glory.mes.wip.lot.flow.LotFlowForm;

public class LotFutureFlowForm extends LotFlowForm{

	private static final Logger logger = Logger.getLogger(LotFlowForm.class);
	
	public LotFutureFlowForm(Composite parent, int style, ADTable table, LotMediator lotMediator, IMessageManager mmng,
			TreeViewerManager treeManager) {
		super(parent, style, table, lotMediator, mmng, treeManager);
	}

	@Override
	public void addFields() {
		try {
			field = new LotFutureFlowTreeField(FIELD_ID, null, getTreeManager(), this);
			addField(FIELD_ID, field);
		} catch (Exception e) {
			logger.error("LotFlowForm : addFields", e);
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
}
