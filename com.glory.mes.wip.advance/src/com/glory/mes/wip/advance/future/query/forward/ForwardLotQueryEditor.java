package com.glory.mes.wip.advance.future.query.forward;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.compress.utils.Lists;
import org.osgi.service.event.Event;

import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.RefTableMultiField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Step;
import com.glory.mes.wip.client.FutureQueryManager;
import com.glory.mes.wip.model.Lot;

public class ForwardLotQueryEditor extends GlcEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.query.forward.ForwardLotQueryEditor";
	
	public static final String CONTROL_QUERY_FORM = "queryInfo";
	public static final String CONTROL_EQUIPMENT_ID = "equipmentId";
	public static final String CONTROL_ATTRIBUTE1 = "attribute1";
	public static final String CONTROL_ATTRIBUTE2 = "attribute2";
	protected QueryFormField queryFormField;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		queryFormField = form.getFieldByControlId(CONTROL_QUERY_FORM, QueryFormField.class);
		subscribeAndExecute(eventBroker, queryFormField.getFullTopic(GlcEvent.EVENT_QUERY), this::queryAdapter);
		RefTableField textField = (RefTableField) queryFormField.getQueryForm().getFields().get("equipmentId");
		RefTableMultiField attribute2Field = (RefTableMultiField) queryFormField.getQueryForm().getFields().get("attribute2");
		
		textField.addValueChangeListener(new IValueChangeListener(){
			@Override
			public void valueChanged(Object arg0, Object arg1) {
				try {
					List<Step> steps =  new ArrayList<Step>();
					if(arg1 != null && !arg1.equals("")) {
						FutureQueryManager futureQueryManager = Framework.getService(FutureQueryManager.class);
						steps = futureQueryManager.getStepsByEquipment(Long.parseLong(arg1.toString()));
					}
					attribute2Field.setValue(null);
					attribute2Field.setInput(steps);
					attribute2Field.refresh();
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}					
			}   		
		});
	}
	
	public void queryAdapter(Object object) {
		Event event = (Event)object;
		try {
			if (!queryFormField.getQueryForm().getQueryForm().validate()){
				return;
			}
			int forwardSteps = 3;
			List<Step> steps = Lists.newArrayList();
			List<Lot> lots = Lists.newArrayList();
			LinkedHashMap<String, IField> fields = queryFormField.getQueryForm().getFields();
			for(IField f : fields.values()) {
	        	Object value = f.getValue();
	        	if (CONTROL_ATTRIBUTE1.equals(f.getId())) {
	        		forwardSteps = Integer.valueOf(value.toString());
	        	} else if (CONTROL_ATTRIBUTE2.equals(f.getId())) {
	        		steps = ((RefTableMultiField)f).getCheckList();
	        		if (steps == null || steps.isEmpty()) {
	        			steps = ((RefTableMultiField)f).getInput();
	        		}
	        		steps = steps.stream().collect(Collectors.toList());
				}
	        }
			if (forwardSteps > 10) {
				UI.showError(Message.getString("wip.forward_step_error"));
				return;
			}
			FutureQueryManager futureQueryManager = Framework.getService(FutureQueryManager.class);
			if (steps != null && !steps.isEmpty()) {
				lots = futureQueryManager.getForwardLotsByStep(Env.getSessionContext().getOrgRrn(), steps, forwardSteps);
			}
			queryFormField.getQueryForm().getTableManager().setInput(lots);
			queryFormField.getQueryForm().getQueryForm().removeAllMessages();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		} finally {
			if (event.getProperty(GlcEvent.PROPERTY_RESPONSE_TOPIC) != null) {
				queryFormField.responseDefaultEvent((String) event.getProperty(GlcEvent.PROPERTY_RESPONSE_TOPIC), null);
			}
		}
	}
}
