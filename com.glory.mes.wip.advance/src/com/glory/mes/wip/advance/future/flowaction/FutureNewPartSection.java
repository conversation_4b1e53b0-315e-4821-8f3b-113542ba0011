package com.glory.mes.wip.advance.future.flowaction;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.custom.XCombo;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.part.PartFlowSection;

public class FutureNewPartSection extends PartFlowSection {

	public FutureNewPartSection(ADTable table) {
		super(table);
	}
	
	public Part getPart() {
		RefTableField partField = (RefTableField) getPartRefTableField();
		if (partField == null) {
			return null;
		}
		
		XCombo combo = partField.getComboControl();
		return (Part) combo.getData();
	}

}
