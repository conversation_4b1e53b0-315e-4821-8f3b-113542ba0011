package com.glory.mes.wip.advance.future.multihold;


import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.mes.wip.future.FutureHold;

public class FutureHoldDialogForm extends EntityForm {

    private static final Logger logger = Logger.getLogger(FutureHoldDialogForm.class);
	public final static String ADTBALE = "WIPADVLotFutureHold";
	public FutureHold futureHold;
    
    public FutureHoldDialogForm(Composite parent, int style, Object object) {
        super(parent, style, object, null);
        try {
            ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), ADTBALE);
			this.table = table;
            createForm();
        } catch (Exception e) {
            logger.error("Constructor in ScrapIdentifiedLotDialog", e);
        }
    }
}
