package com.glory.mes.wip.advance.future.query.startedtimer.glc;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.jface.dialogs.Dialog;
import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.export.NatExporter;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.widgets.Display;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADRefList;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.nattable.TableLegend;
import com.glory.framework.base.ui.nattable.color.ICellBackgroundFunc;
import com.glory.framework.base.ui.nattable.color.ICellForegroundFunc;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.FutureTimerManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.glory.mes.wip.advance.future.query.startedtimer.ChangeExpireTimeDialog;
import com.glory.mes.wip.advance.future.timer.color.ColorTimerInstance;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.custom.LotListComposite;
import com.glory.mes.wip.model.Lot;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class WIPLotStartedTimerQueryEditor extends GlcEditor{ 

	public static final String EDITOR_ID = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.query.startedtimer.glc.WIPLotStartedTimerQueryEditor";

	private static final String FIELD_LOTQUERY = "lotQuery";

	private static final String BUTTON_QUERY = "query";
	private static final String BUTTON_HANGUP = "hangUp";
	private static final String BUTTON_RESUME = "resume";
	private static final String BUTTON_MODIFY = "modify";
	private static final String BUTTON_CLEAR = "clear";
	private static final String BUTTON_EXPORT = "export";
	private static final String BUTTON_REFRESH = "refresh";

	protected CustomField lotQueryField;
	private LotListComposite listComposite;

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		lotQueryField = form.getFieldByControlId(FIELD_LOTQUERY, CustomField.class);
		listComposite = (LotListComposite) lotQueryField.getCustomComposite();
		listComposite.clearColor();
		
		initColor();
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_QUERY), this::queryAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_HANGUP), this::hangUpAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_RESUME), this::resumeAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MODIFY), this::modifyAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_EXPORT), this::exportAdapter);
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_REFRESH), this::refreshAdapter);
	}

	private void initColor() {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			List<ADRefList> adRefLists =  adManager.getADRefList(Env.getOrgRrn(), "WIPFutureTimeInstanceColor");
			
			List<TableLegend> legends = Lists.newArrayList();
			String calCulate = null;
			if (!CollectionUtils.isEmpty(adRefLists)) {
				int i = 1;
				for (ADRefList adRefList : adRefLists) {
					String[] color = adRefList.getText().split(",");
					
					TableLegend legend = new TableLegend();
					legend.setSeqNo(i);
					legend.setColor(new Color(Display.getCurrent(), Integer.parseInt(color[0]), Integer.parseInt(color[1]), Integer.parseInt(color[2])));
					
					if (StringUtil.isEmpty(calCulate)) {
						legend.setLegendText(Message.getString("wip.expire_date") + "<=" + adRefList.getKey() + "min");
					} else {
						legend.setLegendText(calCulate + "min" + "<" + Message.getString("wip.expire_date") + "<="
								+ adRefList.getKey() + "min");
					}
					calCulate = adRefList.getKey();
					
					legends.add(legend);
					i++;
				}
			}
			
			listComposite.setTableLegends(legends);
			listComposite.addBackgroundFunc(new Color1());
			listComposite.addForegroundFunc(new Color1());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}

	private void queryAdapter(Object object) {
		listComposite.queryAdapter();
	}

	private void hangUpAdapter(Object object) {
		try {
			Object selected = listComposite.getSelectedObject();
			
			Object[] elements = null;
			if (selected != null) {
				elements = new Object[] {selected};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else {
				List<ADBase> baseList = new ArrayList<ADBase>();
				for (int i = 0; i < elements.length; i++) {
					ADBase base = (ADBase)elements[i];
					baseList.add(base);
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				FutureTimerManager futureTimerManager = Framework.getService(FutureTimerManager.class);
				
				FutureTimerInstance futureTimerInstance = (FutureTimerInstance) selected;
				futureTimerInstance = (FutureTimerInstance) adManager.getEntity(futureTimerInstance);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), futureTimerInstance.getLotId());
				futureTimerManager.suspendTimers(lot.getObjectRrn(), Arrays.asList(futureTimerInstance), Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.qtime_suspened_success"));
				this.queryAdapter(object);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	private void resumeAdapter(Object object) {
		try {
			Object selected = listComposite.getSelectedObject();
			
			Object[] elements = null;
			if (selected != null) {
				elements = new Object[] {selected};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else {
				List<ADBase> baseList = new ArrayList<ADBase>();
				for (int i = 0; i < elements.length; i++) {
					ADBase base = (ADBase)elements[i];
					baseList.add(base);
				}
				LotManager lotManager = Framework.getService(LotManager.class);
				ADManager adManager = Framework.getService(ADManager.class);
				FutureTimerManager futureTimerManager = Framework.getService(FutureTimerManager.class);
				
				FutureTimerInstance futureTimerInstance = (FutureTimerInstance) selected;
				futureTimerInstance = (FutureTimerInstance) adManager.getEntity(futureTimerInstance);
				Lot lot = lotManager.getLotByLotId(Env.getOrgRrn(), futureTimerInstance.getLotId());
				futureTimerManager.resumeTimers(lot.getObjectRrn(), Arrays.asList(futureTimerInstance), Env.getSessionContext());
				
				UI.showInfo(Message.getString("wip.qtime_resume_success"));
				this.queryAdapter(object);
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	private void modifyAdapter(Object object) {
		try {
			Object selected = listComposite.getSelectedObject();
			if (selected != null) {
				EntityDialog dialog = new ChangeExpireTimeDialog(listComposite.getTableManager().getADTable(), (ADBase)selected);
				if (Dialog.OK == dialog.open()) {
					listComposite.refreshUpdate(dialog.getAdObject());
				}
			} else {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	private void clearAdapter(Object object) {
		try {
			Object selected = listComposite.getSelectedObject();
			
			Object[] elements = null;
			if (selected != null) {
				elements = new Object[] {selected};
			}
			if (elements == null || elements.length == 0) {
				UI.showWarning(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
			} else {
				if (UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()))) {
					List<ADBase> baseList = new ArrayList<ADBase>();
					for (int i = 0; i < elements.length; i++) {
						ADBase base = (ADBase)elements[i];
						baseList.add(base);
					}
					FutureTimerInstance futureTimerInstance = (FutureTimerInstance) selected;
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.clearLotFutureTimerInstanceExpireTime(futureTimerInstance, Env.getSessionContext());
					listComposite.refreshBatchDelete(baseList);
					this.queryAdapter(object);
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
		}
	}

	private void exportAdapter(Object object) {
		NatTable natTable = listComposite.getTableManager().getNatTable();
		try {
			new NatExporter(natTable.getShell()).exportSingleLayer(listComposite.getTableManager().getLayer(),
					natTable.getConfigRegistry());
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	private void refreshAdapter(Object object) {
		this.queryAdapter(object);
	}

	private class Color1 implements ICellBackgroundFunc, ICellForegroundFunc {

		@Override
		public Color getForegroundColor(Object element, String id) {
			return null;
		}

		@Override
		public Color getBackgroundColor(Object element, String id) {
			try {
				FutureTimerInstance futureTimerInstance = (FutureTimerInstance) element;
				//�����뵱ǰʱ����������м���
				Date sysDate = new Date();
				Long time = futureTimerInstance.getExpireTime().getTime() - sysDate.getTime();
				return ColorTimerInstance.calculateTimeDiff(time);
			} catch(Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
			}
	    	return null;
		}
	}
	
}