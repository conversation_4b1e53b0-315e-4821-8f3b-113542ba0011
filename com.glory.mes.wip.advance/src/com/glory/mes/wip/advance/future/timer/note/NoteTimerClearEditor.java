package com.glory.mes.wip.advance.future.timer.note;


import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.nebula.widgets.nattable.export.NatExporter;

import com.glory.framework.activeentity.model.ADButtonDefault;
import com.glory.framework.base.entitymanager.glc.GlcEditor;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.FutureTimerManager;
import com.glory.mes.prd.workflow.action.exe.FutureTimerInstance;
import com.google.common.collect.Lists;
import com.glory.framework.core.exception.ExceptionBundle;

public class NoteTimerClearEditor extends GlcEditor {

	public static final String CONTRIBUTION_URL = "bundleclass://com.glory.mes.wip.advance/com.glory.mes.wip.advance.future.timer.note.NoteTimerClearEditor";
	
	private QueryFormField queryFormField;
	
	private static final String FIELD_QUERY = "queryForm";
	
	private static final String BUTTON_CLEAR = "clear";
	
	protected NatTable natTable;
	
	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);
		
		queryFormField = form.getFieldByControlId(FIELD_QUERY, QueryFormField.class);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_CLEAR), this::clearAdapter);
		
		subscribeAndExecute(eventBroker, form.getFullTopic(ADButtonDefault.BUTTON_NAME_EXPORT), this::exprotAdapter);
		
	}
	
	public void clearAdapter(Object object) {
		try {
			Object selectedObject = queryFormField.getQueryForm().getSelectedObject();

			if (selectedObject != null) {
				FutureTimerManager futureTimerManager = Framework.getService(FutureTimerManager.class);
				
				FutureTimerInstance timerInstance = (FutureTimerInstance)selectedObject;
				
				futureTimerManager.clearTimers(timerInstance.getLotRrn(), Lists.newArrayList(timerInstance), true);
				
				UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
				queryFormField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	private void exprotAdapter(Object object) {
    	try {
    		this.natTable = this.queryFormField.getQueryForm().getTableManager().getNatTable();
    		(new NatExporter(this.natTable.getShell())).exportSingleLayer(
    		 this.queryFormField.getQueryForm().getTableManager().getLayer(), this.natTable.getConfigRegistry());
    	} catch (Exception e) {
    		ExceptionHandlerManager.asyncHandleException(e);
    		return;
    	}    
	}
	
}
