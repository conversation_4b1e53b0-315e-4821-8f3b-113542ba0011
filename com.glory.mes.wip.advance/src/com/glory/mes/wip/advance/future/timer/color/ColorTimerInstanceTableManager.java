package com.glory.mes.wip.advance.future.timer.color;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;

public class ColorTimerInstanceTableManager  extends ListTableManager {

	public ColorTimerInstanceTableManager(ADTable adTable) {
		super(adTable);
	}

	@Override
    public ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        try {
	        factory.registerAdapter(Object.class, new ColorTimerInstanceItemAdapter());
        } catch (Exception e){
        	e.printStackTrace();
        }
        return factory;
    }
}
