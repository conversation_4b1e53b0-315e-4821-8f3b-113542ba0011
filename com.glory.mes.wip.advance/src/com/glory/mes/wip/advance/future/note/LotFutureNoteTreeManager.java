package com.glory.mes.wip.advance.future.note;

import org.apache.log4j.Logger;

import com.glory.framework.base.ui.viewers.adapter.ItemAdapter;
import com.glory.framework.base.ui.viewers.adapter.ItemAdapterFactory;
import com.glory.mes.prd.adapter.ElseStateItemAdapter;
import com.glory.mes.prd.adapter.EndIfStateItemAdapter;
import com.glory.mes.prd.adapter.IfStateItemAdapter;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.model.Process;
import com.glory.mes.prd.workflow.graph.node.ElseState;
import com.glory.mes.prd.workflow.graph.node.EndIfState;
import com.glory.mes.prd.workflow.graph.node.IfState;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.ReworkState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.advance.future.FutureActionItemAdapter;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.lot.flow.LotFlowTreeManager;
import com.glory.mes.wip.lot.flow.LotItemAdapter;
import com.glory.mes.wip.lot.flow.LotProcedureStateItemAdapter;
import com.glory.mes.wip.lot.flow.LotReworkStateItemAdapter;
import com.glory.mes.wip.model.Lot;

public class LotFutureNoteTreeManager extends LotFlowTreeManager {
	
	private static final Logger logger = Logger.getLogger(LotFutureNoteTreeManager.class);
	
	LotNoteStepStateItemAdapter lotNoteAdapter = new LotNoteStepStateItemAdapter();
	
	@Override
	protected ItemAdapterFactory createAdapterFactory() {
        ItemAdapterFactory factory = new ItemAdapterFactory();
        ItemAdapter itemAdapter = new LotItemAdapter();
        try{
        	factory.registerAdapter(Lot.class, itemAdapter);
        	factory.registerAdapter(Process.class, itemAdapter);
        	factory.registerAdapter(Procedure.class, itemAdapter);
        	factory.registerAdapter(ReworkState.class, new LotReworkStateItemAdapter());
        	factory.registerAdapter(ProcedureState.class, new LotProcedureStateItemAdapter());
	        factory.registerAdapter(StepState.class, lotNoteAdapter);
	        factory.registerAdapter(FutureNote.class, new FutureActionItemAdapter());
	        factory.registerAdapter(IfState.class, new IfStateItemAdapter());
	        factory.registerAdapter(ElseState.class, new ElseStateItemAdapter());
	        factory.registerAdapter(EndIfState.class, new EndIfStateItemAdapter());
        } catch (Exception e){
        	logger.error(e.getMessage(), e);
        }
        return factory;
    }
	
}
