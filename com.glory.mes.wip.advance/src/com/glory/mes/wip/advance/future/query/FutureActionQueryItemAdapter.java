package com.glory.mes.wip.advance.future.query;

import org.eclipse.swt.graphics.Image;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.viewers.adapter.ListItemAdapter;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureHold;
import com.glory.mes.wip.future.FutureMerge;
import com.glory.mes.wip.future.FutureMultiProcedureTimer;
import com.glory.mes.wip.future.FutureMultiStepTimer;
import com.glory.mes.wip.future.FutureNote;
import com.glory.mes.wip.future.FutureSkip;
import com.glory.mes.wip.future.FutureTimer;

public class FutureActionQueryItemAdapter extends ListItemAdapter<FutureAction> {
	 
	@Override
	public Image getImage(Object object) {
		if (object instanceof FutureHold) {
			return SWTResourceCache.getImage("hold");
		} else if (object instanceof FutureNote) {
			return SWTResourceCache.getImage("note");
		} else if (object instanceof FutureTimer) {
			return SWTResourceCache.getImage("timer_start");
		} else if (object instanceof FutureMerge) {
			return SWTResourceCache.getImage("hold");
		} else if (object instanceof FutureSkip) {
			return SWTResourceCache.getImage("note");
		} 
		return null;
	}
	
    @Override
	public String getText(Object object, String id) {
    	if ("action".equals(id)) { 
	    	FutureAction action = (FutureAction) object;
	    	if (FutureAction.ACTION_NOTE.equals(action.getAction())) {
				return "Note";
			} else if (FutureAction.ACTION_HOLD.equals(action.getAction())) {
				if (action.getLotRrn() == null) {
					return "ProcessHold";
				} else {
					return "LotHold";
				} 
			} else if (FutureAction.ACTION_MULTISTEPTIMER.equals(action.getAction())) {
				return "MultiStepTimer";
			} else if (FutureAction.ACTION_MULTIPROCEDURETIMER.equals(action.getAction())) {
				return "MultiProcTimer";
			} else if (FutureAction.ACTION_STEPTIMER.equals(action.getAction())) {
				return "StepTimer";
			} else if (FutureAction.ACTION_MERGE.equals(action.getAction())) {
				return "Merge";
			} else if (FutureAction.ACTION_SKIP.equals(action.getAction())) {
				return "Skip";
			} 
    	} else if ("holdCode".equals(id)
    			|| "holdReason".equals(id)) {
    		if (object instanceof FutureHold) {
    			return super.getText(object, id);
			} else if (object instanceof FutureTimer) {
				return super.getText(object, id);
			}
			return "";
    	} else if ("endStepName".equals(id)) {
    		if (object instanceof FutureMultiStepTimer
    				|| object instanceof FutureMultiProcedureTimer) {
    			return super.getText(object, id);
			} 
			return "";
    	} else if ("processName".equals(id) 
    			|| "processVersion".equals(id)
    			|| "endProcedureName".equals(id)
    			|| "endProcedureVersion".equals(id)) {
//    		if (object instanceof FutureMultiProcedureTimer) {
    			return super.getText(object, id);
//			} 
//			return "";
    	} else if ("timerDuration".equals(id)) {
    		if (object instanceof FutureTimer) {
    			return super.getText(object, id);
			} 
			return "";
    	}
    	return super.getText(object, id);
    }
	
}