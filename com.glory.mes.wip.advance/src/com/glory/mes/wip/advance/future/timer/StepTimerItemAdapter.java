package com.glory.mes.wip.advance.future.timer;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.resource.ImageDescriptor;

import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.runtime.Framework;
import com.glory.mes.prd.adapter.StepStateItemAdapter;
import com.glory.mes.prd.model.Step;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureStepTimer;
import com.glory.mes.wip.future.FutureTimer;

public class StepTimerItemAdapter extends StepStateItemAdapter {

	private static final Logger logger = Logger.getLogger(StepTimerItemAdapter.class);
	private static final Object[] EMPTY = new Object[0];

	@Override
	public Object[] getChildren(Object object) {
		if (object instanceof StepState) {
			List<Object> children = new ArrayList<Object>();
			
			StepState stepState = (StepState) object;
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<String> timeTypes = new ArrayList<String>();
				timeTypes.add(FutureAction.ACTION_STEPTIMER);
				
				//Timer��ʼλ�ü��
				List<? extends FutureTimer> startTimers = lotManager.getFutureTimerByStart(stepState, timeTypes, null);
				for (FutureTimer timer : startTimers) {
					children.add(timer);
				}
			
				return children.toArray();
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		} 
		return EMPTY;
	}

	/**
	 * ���StepState���Ƿ��ж�Ӧ��Timer
	 */
	public boolean hasChildren(Object object) {
		if (object instanceof StepState) {
			StepState stepState = (StepState)object;
			try {
				LotManager lotManager = Framework.getService(LotManager.class);
				List<String> timeTypes = new ArrayList<String>();
				timeTypes.add(FutureAction.ACTION_STEPTIMER);
				//Timer��ʼλ�ü��
				List<? extends FutureTimer> startTimers = lotManager.getFutureTimerByStart(stepState, timeTypes, null);
				if (startTimers.size() > 0) {
					return true;
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		}
		return false;
	}
	
	@Override
	public ImageDescriptor getImageDescriptor(Object object, String id) {
		if (object instanceof StepState) {
			return SWTResourceCache.getImageDescriptor("step");
		} else if (object instanceof FutureStepTimer) {
			return SWTResourceCache.getImageDescriptor("timer_start");
		}
		return null;
	}

	@Override
	public String getText(Object element) {
		if (element instanceof StepState) {
			StepState stepState = (StepState) element;
			Step step = getStep(stepState);
			if (step.getDescription() != null) {
				return step.getName() + "<" + step.getDescription() + ">";
			}
			return step.getName();
		} else if (element instanceof FutureStepTimer) {
			FutureStepTimer timer = (FutureStepTimer) element;
			String description = (timer.getDescription() == null
					|| "".equals(timer.getDescription().trim()) ? "" : " <" + timer.getDescription() + ">");
			return timer.getName() + description;
		}
		return "";
	}
	
}

