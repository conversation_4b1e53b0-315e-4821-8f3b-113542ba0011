package com.glory.mes.wip.advance.future.flowaction;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StackLayout;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.model.Part;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.prd.workflow.graph.def.Node;
import com.glory.mes.prd.workflow.graph.node.ProcedureState;
import com.glory.mes.prd.workflow.graph.node.StepState;
import com.glory.mes.wip.advance.future.FutureActionMediator;
import com.glory.mes.wip.advance.future.LotFutureActionSection;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.future.FutureAction;
import com.glory.mes.wip.future.FutureChangeFlow;
import com.glory.mes.wip.future.FutureNewPart;
import com.glory.mes.wip.future.FutureNewProcedure;
import com.glory.mes.wip.model.Lot;
import com.glory.framework.core.exception.ExceptionBundle;

public class FutureFlowActionSection extends LotFutureActionSection {

	private static final String TABLE_FUTURE_NEW_PART = "WIPADVFutureNewPart";
	private static final String TABLE_FUTURE_NEW_PROCEDURE = "WIPADVFutureNewProcedure";
	private static final String TABLE_FUTURE_CHANGE_FLOW = "WIPADVFutureChangeFlow";

	private static final String TABLE_NEW_PROCEDURE = "WIPADVRecovery";
	
	protected ToolItem itemChange;
	protected ToolItem refreshItem;

	private FutureNewPartSection newPartSection;
	private FutureNewProcedureSection newProcedureSection;

	private EntityForm newPartForm;
	private EntityForm newProcedureForm;
	private EntityForm changeFlowForm;
	
	private Button btnNewPart;
	private Button btnChangeFlow;
	private Button btnNewProcedure;

	public FutureFlowActionSection(ADTable table) {
		super(table);
	}

	@Override
	public void initAdObject() {
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.FILL);
		createToolItemChange(tBar);
		createToolItemDelete(tBar);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemChange(ToolBar tBar) {
		itemChange = new ToolItem(tBar, SWT.PUSH);
		itemChange.setEnabled(false);
		itemChange.setText(Message.getString("wipadv.future_action"));
		itemChange.setImage(SWTResourceCache.getImage("newpart"));
		itemChange.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				flowActionAdapter(event);
			}
		});
	}

	protected void flowActionAdapter(SelectionEvent event) {
		try {
			getMessageManager().setAutoUpdate(true);
			getMessageManager().removeAllMessages();
			
			FutureAction futureAction = null;
			if (btnNewPart.getSelection()) {
				futureAction = getNewPartAction();
			} else if (btnNewProcedure.getSelection()) {
				futureAction = getNewProcedureAction();
			} else if (btnChangeFlow.getSelection()) {
				futureAction = getChangeFlowAction();
			}
			
			if (futureAction == null) {
				return;
			}
			
			LotManager lotManager = Framework.getService(LotManager.class);
			ADBase obj = lotManager.saveFutureAction(futureAction, Env.getSessionContext());

			ADManager entityManager = Framework.getService(ADManager.class);
			obj = entityManager.getEntity(obj);

			newPartForm.setObject(new FutureNewPart());
			newPartForm.loadFromObject();
			newProcedureForm.setObject(new FutureNewProcedure());
			newProcedureForm.loadFromObject();
			changeFlowForm.setObject(new FutureChangeFlow());
			changeFlowForm.loadFromObject();
			
			UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonOperationSuccessed()));
			lotMediator.notifyLotFlowTreeField(FutureActionMediator.REFRESH, obj);
			
			FutureFlowActionLotFlowSection flowSection = (FutureFlowActionLotFlowSection) lotFlowSection;
			flowSection.refreshSelection();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			getMessageManager().setAutoUpdate(false);
		}
	}
	
	private FutureChangeFlow getChangeFlowAction() throws Exception {
		changeFlowForm.removeAllMessages();
		boolean saveFlag = true;
		if (!changeFlowForm.saveToObject()) {
			saveFlag = false;
		}
		
		if (saveFlag) {
			FutureChangeFlow futureChange = (FutureChangeFlow) changeFlowForm.getObject();
			if (futureChange.getObjectRrn() == null) {
				if (!validate()) {
					return null;
				} else {
					Lot lot = (Lot) getCurrentLot();
					
					FutureFlowActionLotFlowSection flowSection = (FutureFlowActionLotFlowSection) lotFlowSection;
					StepState startStepState = flowSection.getStartStep();
					futureChange.setOrgRrn(Env.getOrgRrn());
					if(futureChange.getLotRrn() == null) {
						futureChange.setLotRrn(lot.getObjectRrn());
						futureChange.setLotId(lot.getLotId());
						futureChange.setPartName(lot.getPartName());
						futureChange.setPartVersion(lot.getPartVersion());
						futureChange.setProcessName(lot.getProcessName());
						futureChange.setProcessVersion(lot.getProcessVersion());
					}
					
					if (futureChange.getStepStateName() == null) {
						futureChange.setStepStateName(startStepState.getName());
					}
					
					futureChange.setPath(startStepState.getPath());
					futureChange.setProcedureName(startStepState.getProcessDefinition().getName());
					futureChange.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
					futureChange.setStepName(startStepState.getUsedStep().getName());
					
					StepState returnState = flowSection.getReturnStep();
					
					futureChange.setEndProcedureName(returnState.getProcessDefinition().getName());
					futureChange.setEndProcedureVersion(returnState.getProcessDefinition().getVersion());
					futureChange.setEndStepName(returnState.getUsedStep().getName());
					futureChange.setEndStepStateName(returnState.getName());
					futureChange.setEndPath(returnState.getPath());
				}
			}
			return futureChange;
		}
		return null;
	}

	private FutureAction getNewProcedureAction() throws Exception {
		newProcedureForm.removeAllMessages();
		boolean saveFlag = true;
		if (!newProcedureForm.saveToObject()) {
			saveFlag = false;
		}
		
		if (saveFlag) {
			FutureNewProcedure futureChange = (FutureNewProcedure) newProcedureForm.getObject();
			if (futureChange.getObjectRrn() == null) {
				if (!validate()) {
					return null;
				} else {
					Lot lot = (Lot) getCurrentLot();
					
					FutureFlowActionLotFlowSection flowSection = (FutureFlowActionLotFlowSection) lotFlowSection;
					StepState startStepState = flowSection.getStartStep();
					futureChange.setOrgRrn(Env.getOrgRrn());
					if(futureChange.getLotRrn() == null) {
						futureChange.setLotRrn(lot.getObjectRrn());
						futureChange.setLotId(lot.getLotId());
						futureChange.setPartName(lot.getPartName());
						futureChange.setPartVersion(lot.getPartVersion());
						futureChange.setProcessName(lot.getProcessName());
						futureChange.setProcessVersion(lot.getProcessVersion());
					}
					
					if (futureChange.getStepStateName() == null) {
						futureChange.setStepStateName(startStepState.getName());
					}
					
					futureChange.setPath(startStepState.getPath());
					futureChange.setProcedureName(startStepState.getProcessDefinition().getName());
					futureChange.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
					futureChange.setStepName(startStepState.getUsedStep().getName());
					
					StepState returnState = flowSection.getReturnStep();
					futureChange.setEndPath(returnState.getPath());
					futureChange.setEndProcedureName(returnState.getProcessDefinition().getName());
					futureChange.setEndProcedureVersion(returnState.getProcessDefinition().getVersion());
					futureChange.setEndStepName(returnState.getUsedStep().getName());
					futureChange.setEndStepStateName(returnState.getName());
					
					Procedure procedure = newProcedureSection.getProcedure();
					futureChange.setNewProcedureName(procedure.getName());
					futureChange.setNewProcedureVersion(procedure.getVersion());
					
					StepState start = newProcedureSection.getStepStart();
					if (start != null) {
						futureChange.setNewStartStepName(start.getUsedStep().getName());
						futureChange.setNewStartStepStateName(start.getName());
					}
					
					StepState end = newProcedureSection.getStepEnd();
					if (end != null) {
						futureChange.setNewEndStepName(end.getUsedStep().getName());
						futureChange.setNewEndStepStateName(end.getName());
					}
					
					StepState startEnd = newProcedureSection.getStartEnd();
					if (startEnd != null) {
						futureChange.setNewStartStepName(startEnd.getUsedStep().getName());
						futureChange.setNewStartStepStateName(startEnd.getName());
						futureChange.setNewEndStepName(startEnd.getUsedStep().getName());
						futureChange.setNewEndStepStateName(startEnd.getName());
					}
				}
			}
			return futureChange;
		}
		return null;
	}

	private FutureNewPart getNewPartAction() throws Exception {
		newPartForm.removeAllMessages();
		boolean saveFlag = true;
		if (!newPartForm.saveToObject()) {
			saveFlag = false;
		}
		
		if (saveFlag) {
			FutureNewPart futureNewPart = (FutureNewPart) newPartForm.getObject();
			if (futureNewPart.getObjectRrn() == null) {
				if (!validate()) {
					return null;
				} else {
					Lot lot = (Lot) getCurrentLot();
					FutureFlowActionLotFlowSection flowSection = (FutureFlowActionLotFlowSection) lotFlowSection;
					StepState startStepState = flowSection.getStartStep();
					futureNewPart.setOrgRrn(Env.getOrgRrn());
					if(futureNewPart.getLotRrn() == null) {
						futureNewPart.setLotRrn(lot.getObjectRrn());
						futureNewPart.setLotId(lot.getLotId());
						futureNewPart.setPartName(lot.getPartName());
						futureNewPart.setPartVersion(lot.getPartVersion());
						futureNewPart.setProcessName(lot.getProcessName());
						futureNewPart.setProcessVersion(lot.getProcessVersion());
					}
					
					if (futureNewPart.getStepStateName() == null) {
						futureNewPart.setStepStateName(startStepState.getName());
					}
					
					futureNewPart.setPath(startStepState.getPath());
					futureNewPart.setProcedureName(startStepState.getProcessDefinition().getName());
					futureNewPart.setProcedureVersion(startStepState.getProcessDefinition().getVersion());
					futureNewPart.setStepName(startStepState.getUsedStep().getName());
					
					Part newPart = newPartSection.getPart();
					futureNewPart.setNewPartName(newPart.getName());
					futureNewPart.setNewPartVersion(newPart.getVersion());
					
					List<Node> flowList = newPartSection.getFlowList();
					if (CollectionUtils.isNotEmpty(flowList)) {
						for (Node node : flowList) {
							if (node instanceof ProcedureState) {
								ProcedureState procedureState = (ProcedureState) flowList.get(0);
								futureNewPart.setNewProcedureName(procedureState.getUsedProcedure().getName());
								futureNewPart.setNewProcedureVersion(procedureState.getUsedProcedure().getVersion());
								futureNewPart.setNewPath(procedureState.getPath());
							}
							if (node instanceof StepState) {
								StepState stepState = (StepState) flowList.get(1);
								futureNewPart.setNewStepName(stepState.getUsedStep().getName());
								futureNewPart.setNewStepStateName(stepState.getName());
								futureNewPart.setNewPath(stepState.getPath());
							}
						}
					}
				}
			}
			return futureNewPart;
		}
		return null;
	}
	
	private boolean validate() {
		FutureFlowActionLotFlowSection flowSection = (FutureFlowActionLotFlowSection) lotFlowSection;
		Lot lot = (Lot) getCurrentLot();
		if (lot == null) {
			UI.showInfo(Message.getString("wipadv.future_flow_action_scan_lot_id"));
			return false;
		}
		
		StepState startStepState = flowSection.getStartStep();
		if (startStepState == null) {
			UI.showInfo(Message.getString("wipadv.future_flow_action_select_start"));
			return false;
		}
		
		if (btnNewPart.getSelection()) {
			Part newPart = newPartSection.getPart();
			if (newPart == null) {
				UI.showInfo(Message.getString("wipadv.future_flow_action_select_part"));
				return false;
			}
		}
		
		if (btnNewProcedure.getSelection()) {
			StepState returnState = flowSection.getReturnStep();
			if (returnState == null) {
				UI.showInfo(Message.getString("wipadv.future_flow_action_select_return"));
				return false;
			}
			
			if (!startStepState.getProcessDefinition().equals(returnState.getProcessDefinition())) {
				UI.showInfo(Message.getString("wipadv.future_flow_action_start_return_same_procedure"));
				return false;
			}
			
			Procedure procedure = newProcedureSection.getProcedure();
			if (procedure == null) {
				UI.showInfo(Message.getString("wipadv.future_flow_action_select_procedure"));
				return false;
			}
		}
		
		if (btnChangeFlow.getSelection()) {
			StepState returnState = flowSection.getReturnStep();
			if (returnState == null) {
				UI.showInfo(Message.getString("wipadv.future_flow_action_select_return"));
				return false;
			}
		}

		return true;
	}

	@Override
	protected void deleteAdapter() {
		try {
			FutureAction futureAction = null;
			if (btnNewPart.getSelection()) {
				futureAction = (FutureAction) newPartForm.getObject();
			} else if (btnNewProcedure.getSelection()) {
				futureAction = (FutureAction) newProcedureForm.getObject();
			} else if (btnChangeFlow.getSelection()) {
				futureAction = (FutureAction) changeFlowForm.getObject();
			}
			
			if (futureAction != null && futureAction.getObjectRrn() != null) {
				boolean confirmDelete = UI.showConfirm(Message
						.getString("common.confirm_delete"));
				if (confirmDelete) {
					LotManager lotManager = Framework.getService(LotManager.class);
					lotManager.deleteFutureAction(futureAction, Env.getSessionContext());
					lotMediator.notifyLotFlowTreeField(FutureActionMediator.DELETE, futureAction);
					
					if (btnNewPart.getSelection()) {
						newPartForm.setObject(new FutureNewPart());
						newPartForm.loadFromObject();
					} else if (btnNewProcedure.getSelection()) {
						newProcedureForm.setObject(new FutureNewProcedure());
						newProcedureForm.loadFromObject();
					} else if (btnChangeFlow.getSelection()) {
						changeFlowForm.setObject(new FutureChangeFlow());
						changeFlowForm.loadFromObject();
					}
				}
			}
		} catch (Exception e1) {
			ExceptionHandlerManager.asyncHandleException(e1);
			return;
		}
	}
	
	public void createContents(IManagedForm form, Composite parent) {
		this.form = form;
		final FormToolkit toolkit = form.getToolkit();
		section = toolkit.createSection(parent, Section.TITLE_BAR);
		section.setText(Message.getString("wipadv.future_flow_action_detail"));
		section.marginWidth = 0;
		section.marginHeight = 0;
		toolkit.createCompositeSeparator(section);
		createToolBar(section);
		
		GridLayout gridLayout = new GridLayout();
		gridLayout.numColumns = 1;

		GridData td = new GridData(GridData.FILL_BOTH);
		section.setLayoutData(td);

		Composite client = toolkit.createComposite(section);
		client.setLayout(gridLayout);
		client.setLayoutData(new GridData(GridData.FILL_BOTH));

		createSectionContent(client);
		toolkit.paintBordersFor(section);
		section.setClient(client);
		statusChanged(null);
	}
	
	@Override
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		
		GridData gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.horizontalAlignment = SWT.RIGHT;
		final Composite compButton = toolkit.createComposite(client);
		compButton.setLayout(new GridLayout(3, false));
		compButton.setLayoutData(gd);
		
		btnNewPart = toolkit.createButton(compButton, Message.getString("wipadv.future_flow_action_new_part"), SWT.RADIO);
		btnChangeFlow = toolkit.createButton(compButton, Message.getString("wipadv.future_flow_action_change_flow"), SWT.RADIO);
		btnNewProcedure = toolkit.createButton(compButton, Message.getString("wipadv.future_flow_action_new_procedure"), SWT.RADIO);
		btnNewPart.setSelection(true);
		
		/*gd = new GridData(GridData.FILL_HORIZONTAL);
		gd.heightHint = 300;*/
		gd = new GridData(GridData.FILL_BOTH);
		final Composite compFlow = toolkit.createComposite(client);
		compFlow.setLayout(new GridLayout(2, false));
		compFlow.setLayoutData(gd);
		
		
		final Composite compLeft = toolkit.createComposite(compFlow);
		compLeft.setLayout(new GridLayout(1, false));
		compLeft.setLayoutData(gd);
		
		MDSashForm sashForm = new MDSashForm(compLeft, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		configureBody(sashForm);

		final FutureFlowActionLotFlowSection lotFlowSection = new FutureFlowActionLotFlowSection(
				table, lotMediator, new FutureFlowActionTreeManager(), this);
		lotFlowSection.createContents(form, sashForm);
		lotFlowSection.addTreeViewerMouseListener();
		this.lotFlowSection = lotFlowSection;
		
		final Composite compRight = toolkit.createComposite(compFlow);
		compRight.setLayout(new GridLayout(1, false));
		compRight.setLayoutData(gd);
		
		final Composite compStackFlow = toolkit.createComposite(compRight);
		final StackLayout stackLayout = new StackLayout();
		compStackFlow.setLayout(stackLayout);
		compStackFlow.setLayoutData(gd);

		final Composite sForm = createNewPartFlow(toolkit, compStackFlow);
		final Composite sForm1 = createChangeFlowFlow(toolkit, compStackFlow);
		final Composite sForm2 = createNewProcedureFlow(toolkit, compStackFlow);
		stackLayout.topControl = sForm;
		
		gd = new GridData(GridData.FILL_HORIZONTAL);
		final Composite compStackForm = toolkit.createComposite(client);
		final StackLayout stackLayout1 = new StackLayout();
		compStackForm.setLayout(stackLayout1);
		compStackForm.setLayoutData(gd);
		
		final Composite comp = createNewPartForm(toolkit, compStackForm);
		final Composite comp1 = createChangeFlowForm(toolkit, compStackForm);
		final Composite comp2 = createNewProcedureForm(toolkit, compStackForm);
		stackLayout1.topControl = comp;
		
		btnNewPart.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				Button button = (Button) e.getSource();
				if (button.getSelection()) {
					stackLayout.topControl = sForm;
					stackLayout1.topControl = comp;
					compStackFlow.layout();
					compStackForm.layout();
					lotFlowSection.clearSelections();
				}
				
			}
		});

		btnChangeFlow.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				Button button = (Button) e.getSource();
				if (button.getSelection()) {
					stackLayout.topControl = sForm1;
					stackLayout1.topControl = comp1;
					compStackFlow.layout();
					compStackForm.layout();
					lotFlowSection.clearSelections();
				}
			}
		});

		btnNewProcedure.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				Button button = (Button) e.getSource();
					if (button.getSelection()) {
					stackLayout.topControl = sForm2;
					stackLayout1.topControl = comp2;
					compStackFlow.layout();
					compStackForm.layout();
					lotFlowSection.clearSelections();
				}
			}
		});
	}

	private Composite createNewPartFlow(FormToolkit toolkit, Composite compStack) {
		Composite formComp = toolkit.createComposite(compStack);
		configureBody(formComp);

		MDSashForm sashForm = new MDSashForm(formComp, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		configureBody(sashForm);

		newPartSection = new FutureNewPartSection(table);
		newPartSection.createContents(form, sashForm);
		return formComp;
	}
	
	private Composite createNewPartForm(FormToolkit toolkit, Composite compForm) {
		Composite formComp = toolkit.createComposite(compForm);
		configureBody(formComp);
		
		ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), TABLE_FUTURE_NEW_PART);
		newPartForm = new EntityForm(formComp, SWT.NONE, adTable, getMessageManager());
		newPartForm.setObject(new FutureNewPart());
		configureBody(newPartForm);
		return formComp;
	}
	
	private Composite createNewProcedureFlow(FormToolkit toolkit, Composite compStack) {
		Composite formComp = toolkit.createComposite(compStack);
		configureBody(formComp);

		MDSashForm sashForm = new MDSashForm(formComp, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		configureBody(sashForm);

		ADTable procedureAdTable = getADManger().getADTable(Env.getOrgRrn(), TABLE_NEW_PROCEDURE);
		procedureAdTable = getADManger().getADTableDeep(procedureAdTable.getObjectRrn());
		newProcedureSection = new FutureNewProcedureSection(procedureAdTable);
		newProcedureSection.createContents(form, sashForm);
		return formComp;
	}
	
	private Composite createNewProcedureForm(FormToolkit toolkit, Composite compForm) {
		Composite formComp = toolkit.createComposite(compForm);
		configureBody(formComp);
		
		ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), TABLE_FUTURE_NEW_PROCEDURE);
		newProcedureForm = new EntityForm(formComp, SWT.NONE, adTable, getMessageManager());
		newProcedureForm.setObject(new FutureNewProcedure());
		configureBody(newProcedureForm);
		return formComp;
	}
	
	private Composite createChangeFlowFlow(FormToolkit toolkit, Composite compStack) {
		Composite formComp = toolkit.createComposite(compStack);
		configureBody(formComp);
		return formComp;
	}
	
	private Composite createChangeFlowForm(FormToolkit toolkit, Composite compForm) {
		Composite formComp = toolkit.createComposite(compForm);
		configureBody(formComp);
		
		ADTable adTable = getADManger().getADTable(Env.getOrgRrn(), TABLE_FUTURE_CHANGE_FLOW);
		changeFlowForm = new EntityForm(formComp, SWT.NONE, adTable, getMessageManager());
		changeFlowForm.setObject(new FutureChangeFlow());
		configureBody(changeFlowForm);
		return formComp;
	}

	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.horizontalSpacing = 0;
		layout.verticalSpacing = 0;
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.marginLeft = 0;
		layout.marginRight = 0;
		layout.marginTop = 0;
		layout.marginBottom = 0;
		body.setLayout(layout);
		body.setLayoutData(new GridData(GridData.FILL_BOTH));
	}
	
	@Override
	public void setAdObject(ADBase adObject) {
		if (adObject instanceof Lot) {
			return;
		}
		changeFlowForm.setObject(new FutureChangeFlow());
		newPartForm.setObject(new FutureNewPart());
		newProcedureForm.setObject(new FutureNewProcedure());
		btnNewPart.setSelection(false);
		btnNewProcedure.setSelection(false);
		btnChangeFlow.setSelection(false);
		if (adObject instanceof FutureChangeFlow) {
			// �л���ʾ����
			btnChangeFlow.setSelection(true);
			btnChangeFlow.notifyListeners(SWT.Selection, new Event());
			// ��������
			changeFlowForm.setObject(adObject);
		} else if (adObject instanceof FutureNewPart) {
			// �л���ʾ����
			btnNewPart.setSelection(true);
			btnNewPart.notifyListeners(SWT.Selection, new Event());
			// ��������
			newPartForm.setObject(adObject);
		} else if (adObject instanceof FutureNewProcedure) {
			// �л���ʾ����
			btnNewProcedure.setSelection(true);
			btnNewProcedure.notifyListeners(SWT.Selection, new Event());
			// ��������
			newProcedureForm.setObject(adObject);
		}
		newProcedureForm.loadFromObject();
		newPartForm.loadFromObject();
		changeFlowForm.loadFromObject();
	}
	
	public void selectionChange() {
		if (btnNewPart.getSelection()) {
			newPartForm.setObject(new FutureNewPart());
			newPartForm.loadFromObject();
		} else if (btnNewProcedure.getSelection()) {
			newProcedureForm.setObject(new FutureNewProcedure());
			newProcedureForm.loadFromObject();
		} else if (btnChangeFlow.getSelection()) {
			changeFlowForm.setObject(new FutureChangeFlow());
			changeFlowForm.loadFromObject();
		}
		itemChange.setEnabled(true);
	}

	@Override
	public void statusChanged(String newStatus) {
		if (newStatus != null && !"".equals(newStatus.trim())) {
			if ("FutureFlowAction".equalsIgnoreCase(newStatus)) {
				itemChange.setEnabled(true);
			}
		}
	}
	
	public boolean isChangeFlow() {
		return btnChangeFlow.getSelection();
	}
	
	public boolean isNewPart() {
		return btnNewPart.getSelection();
	}
	
	public boolean isNewProcedure() {
		return btnNewProcedure.getSelection();
	}

	@Override
	public void refresh() {
	}
	
}
