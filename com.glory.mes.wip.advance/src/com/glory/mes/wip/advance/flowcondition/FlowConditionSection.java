package com.glory.mes.wip.advance.flowcondition;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.eclipse.nebula.widgets.nattable.NatTable;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityAttributeForm;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntitySection;
import com.glory.framework.base.entitymanager.forms.QueryTableForm;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.forms.MDSashForm;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.prd.client.PrdManager;
import com.glory.mes.prd.model.FlowCondition;
import com.glory.mes.prd.model.FlowConditionDetail;
import com.glory.mes.prd.model.Procedure;
import com.glory.mes.wip.lot.condition.ConditionDialog;
import com.glory.mes.wip.lot.flow.LotFlowSection;
import com.glory.mes.wip.lot.flow.LotFlowTreeManager;

import org.eclipse.nebula.widgets.nattable.selection.action.AbstractMouseSelectionAction;
import com.glory.framework.core.exception.ExceptionBundle;

public class FlowConditionSection extends EntitySection {

	public static final String FIELD_PART_NAME = "partName";
	public static final String FIELD_PROCESS_NAME = "processName";
	public static final String FIELD_PROCEDURE_NAME = "procedureName";
	public static final String FIELD_STEP_NAME = "stepName";
	public static final String FIELD_IS_ENABLE = "isEnable";
	
	public static final String FIELD_CONDITION_DETAIL = "conditionDetails";

	private QueryTableForm queryTableForm;

	private EntityForm flowForm;
	private EntityForm conditionForm;

	protected LotFlowTreeManager manager;
	protected LotFlowSection lotFlowSection;
	
	private List<String> flowFields = Arrays.asList(FIELD_PART_NAME, FIELD_PROCESS_NAME, FIELD_PROCEDURE_NAME, FIELD_STEP_NAME, FIELD_IS_ENABLE);
	private List<String> conditionFields = Arrays.asList(FIELD_CONDITION_DETAIL);

	private ADTable conditionTable;

	
	public FlowConditionSection(ADTable adTable) {
		super(adTable);
		initAdObject();
	}
	
	public void initAdObject() {
		setAdObject(new FlowCondition());
		refresh();
	}
	
	@Override
	protected void createSectionContent(Composite client) {
		final FormToolkit toolkit = form.getToolkit();
		mmng = getMessageManager();

		MDSashForm sashForm = new MDSashForm(client, SWT.NONE);
		toolkit.adapt(sashForm, false, false);
		GridData gd = new GridData(GridData.FILL_BOTH);
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		sashForm.setLayoutData(gd);
		sashForm.setLayout(layout);

		flowForm = new EntityForm(sashForm, SWT.NONE, this.getAdObject(), getFlowFields(), 1, mmng);
		
//		Composite queryComp = new Composite(flowForm, SWT.NONE);
//        queryComp.setLayoutData(gd);
//        
//        layout = new GridLayout(3, false);
//		layout.marginWidth = 0;
//		layout.marginHeight = 0;
//        queryComp.setLayout(layout);
//        
//        Label label = UIControlsFactory.createLabel(queryComp, "");
//        gd = new GridData();
//		gd.horizontalAlignment = SWT.RIGHT;
//		gd.grabExcessHorizontalSpace = true;
//		gd.widthHint = 100;
//		label.setLayoutData(gd);
		
//		SquareButton btnFlow = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
//		btnFlow.setText(Message.getString("common.flow"));
//		gd = new GridData();
//		gd.horizontalAlignment = SWT.RIGHT;
//		gd.grabExcessHorizontalSpace = true;
//		btnFlow.setLayoutData(gd);
//		btnFlow.addSelectionListener(new SelectionAdapter() {
//			public void widgetSelected(SelectionEvent event) {
//				flowAdapter();
//			}
//		});
//		
//        SquareButton btnView = UIControlsFactory.createButton(queryComp, UIControlsFactory.BUTTON_DEFAULT);
//        btnView.setText(Message.getString("common.condition"));
//        btnView.setLayoutData(gd);
//        btnView.addSelectionListener(new SelectionAdapter() {
//			public void widgetSelected(SelectionEvent event) {
//				viewAdapter();
//			}
//		});
        
		flowForm.setADManager(getADManger());
		detailForms.add(flowForm);
		
		conditionForm = new EntityForm(sashForm, SWT.NONE, this.getAdObject(), getConditionFields(), 1, mmng);
		conditionForm.setADManager(getADManger());
		TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);
		conditionField.setAddSelectionListener(new SelectionAdapter() {
			public void widgetSelected(SelectionEvent e) {
				addConditionAdapter();
			}
		});
		conditionField.setDoubleClickListener(new AbstractMouseSelectionAction() {
			public void run(NatTable natTable, MouseEvent event) {
				viewConditionAdapter();
		    }
		});
		conditionTable = conditionField.getTableManager().getADTable();
		detailForms.add(flowForm);
		
		sashForm.setWeights(new int[] { 3, 7 });
	}
	
	@Override
	protected void newAdapter() {
		super.newAdapter();
		TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);
		conditionField.setValue(null);
	}
	
	protected void flowAdapter() {
		
	}
	
	protected void viewAdapter() {
		
	}
	
	protected void addConditionAdapter() {
		try {
			FlowConditionDetail flowConditionDetail = new FlowConditionDetail();
			Procedure procedure = (Procedure) getField("procedureName").getData();
			ConditionDialog dialog = new ConditionDialog(UI.getActiveShell(), flowConditionDetail, procedure, conditionTable, false);
			if (dialog.open() == 0) {
				TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);
				List<ADBase> list = (List<ADBase>)conditionField.getValue();
				if (list != null && list.size() != 0) {
					
					list.add(flowConditionDetail);
					conditionField.filter(list);
				} else {
					 List<ADBase> ls = new ArrayList<ADBase>();
					 ls.add(flowConditionDetail);
					 conditionField.filter(ls);
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	protected void viewConditionAdapter() {
		try {
			TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);
			FlowConditionDetail flowConditionDetail = (FlowConditionDetail)conditionField.getTableManager().getSelectedObject();
			Procedure procedure = (Procedure) getField("procedureName").getData();
			ConditionDialog dialog = new ConditionDialog(UI.getActiveShell(), flowConditionDetail, procedure, conditionTable, false);
			if (dialog.open() == 0) {
				conditionField.refresh();
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
	
	@Override
	protected void saveAdapter() {
		try {
			getMessageManager().setAutoUpdate(true); //����Ϊtrueʱ�Ż�ˢ�½������icon
			getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						if (detailForm instanceof EntityAttributeForm) {
							getAdObject().setAttributeValues(((EntityAttributeForm)detailForm).getAttributeValues());
						} 
					}

					//�������
					if (!checkObject(getAdObject())) {
						return;
					}

					ADBase obj = save(getAdObject());
					
					ADManager entityManager = getADManger();
					ADBase newBase = entityManager.getEntity(obj);
					if (getTable().isContainAttribute()) {
						newBase.setAttributeValues(entityManager.getEntityAttributeValues(getTable().getModelName(), newBase.getObjectRrn()));
					}
					setAdObject(newBase);
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSaveSuccessed()));// ������ʾ��
					refresh();
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		} finally {
			getMessageManager().setAutoUpdate(false);
		}
	}

	@Override
	public ADBase save(ADBase obj) throws Exception {
		try {
			FlowCondition flowCondition = (FlowCondition) obj;
			
			TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);
			List<FlowConditionDetail> list = (List<FlowConditionDetail>)conditionField.getValue();
			flowCondition.setConditionDetails(list);
			
			PrdManager prdManager = Framework.getService(PrdManager.class);
			flowCondition = prdManager.saveFlowCondition(flowCondition, Env.getSessionContext());
			
			//ˢ��
			if (queryTableForm != null) {
				queryTableForm.refresh();
			}
			
			return flowCondition;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return obj;
		}		
	}

	@Override
	public boolean delete() {
		try {
			boolean confirmDelete = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
			if (confirmDelete) {
				if (getAdObject().getObjectRrn() != null) {
					FlowCondition flowCondition = (FlowCondition) getAdObject();
					
					PrdManager prdManager = Framework.getService(PrdManager.class);
					prdManager.deleteFlowCondition(flowCondition, Env.getSessionContext());
					
					//ˢ��
					UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonDeleteSuccessed()));// ������ʾ��
					setAdObject(createAdObject());
					refresh();
					if (queryTableForm != null) {
						queryTableForm.refresh();
					}
					
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		return false;
	}
	
	@Override
	public void refresh() {
		try {
			FlowCondition condition = (FlowCondition)getAdObject();
			List<FlowConditionDetail> details = condition.getConditionDetails();
			if (conditionForm != null) {
				TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);	
				conditionField.setValue(details);
				List<ADBase> list = (List<ADBase>)conditionField.getValue();
				conditionField.filter(list);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
		
		super.refresh();
	}
	
	public List<ADField> getFlowFields() {
		List<ADField> fields = new ArrayList<ADField>();
		for (ADField field : table.getFields()) {
			if (flowFields.contains(field.getName())) {
				fields.add(field);
			}
		}
		return fields;
	}
	
	public List<ADField> getConditionFields() {
		List<ADField> fields = new ArrayList<ADField>();
		for (ADField field : table.getFields()) {
			if (conditionFields.contains(field.getName())) {
				fields.add(field);
			}
		}
		return fields;
	}

	public void setQueryTableForm(QueryTableForm queryTableForm) {
		this.queryTableForm = queryTableForm;
	}
	
	private boolean checkObject(ADBase obj) {		
		try {
//			FlowCondition flowCondition = (FlowCondition) obj;
			
			//��Ʒ�����ա����̡����� �ĸ���������ͬʱΪ��
//			if (StringUtil.isEmpty(flowCondition.getPartName()) &&
//				StringUtil.isEmpty(flowCondition.getProcessName()) &&
//				StringUtil.isEmpty(flowCondition.getProcedureName()) &&
//				StringUtil.isEmpty(flowCondition.getStepName())) {
//				//UI.showError(Message.getString("prd.empty_at_same_time"));
//				return false;
//			}
			
			//�������������ϸ
			TableSelectField conditionField = (TableSelectField)conditionForm.getFields().get(FIELD_CONDITION_DETAIL);
			List<FlowConditionDetail> list = (List<FlowConditionDetail>)conditionField.getValue();
			if (list == null || list.size() == 0) {
				UI.showError(Message.getString("prd.flow_condition_detail_list_is_empty"));
				return false;
			}
			
			return true;
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
	}
}
