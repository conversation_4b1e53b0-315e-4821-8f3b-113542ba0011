package com.glory.mes.wip.advance.custom;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADFormAttribute;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.custom.CustomCompsite;
import com.glory.framework.base.ui.util.Env;
import com.glory.mes.wip.advance.future.flowtimer.FlowTimerUtils;
import com.glory.mes.wip.advance.future.timer.multitimer.MultiTimerSection;

public class MultiTimerCustomComposite extends CustomCompsite {

	private static final Logger logger = Logger.getLogger(MultiTimerCustomComposite.class);

	public static final String TABLE_NAME = "WIPADVMultiStepTimer";

	private MultiTimerSection section;
   
	@Override
	public Composite createForm(FormToolkit toolkit, Composite parent) {
		try{
			ADTable adTable = getADManger().getADTableDeep(Env.getOrgRrn(), TABLE_NAME);
			adTable = FlowTimerUtils.tableHandle(adTable);
			createSection(adTable);
			
			ScrolledForm form = toolkit.createScrolledForm(parent);
			ManagedForm mform = new ManagedForm(toolkit, form);
			configureBody(form);
			Composite body = form.getBody();
			configureBody(body);
			section.createContents(mform, body);
		} catch (Exception e){
			logger.error("MultiTimerCustomComposite : createForm ", e);
		}
		
		return parent;
	}
	
	protected void createSection(ADTable adTable) {
		section = new MultiTimerSection(adTable);
	}

	@Override
	public void refresh() {
	}
	

	@Override
	public void setValue(Object value) {
	}

	@Override
	public Object getValue() {
		return null;
	}


	@Override
	public void preDestory() {
		super.preDestory();
	}

	@Override
	public void setAttributes(List<ADFormAttribute> attributes) {
		
	}

	public MultiTimerSection getSection() {
		return section;
	}

	public void setSection(MultiTimerSection section) {
		this.section = section;
	}

	
}
