<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
   <extension
         point="org.eclipse.ui.editors">
      <editor
            class="com.glory.mes.wip.advance.future.note.LotNoteEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.note.LotNoteEditor"
            name="LotNoteEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.hold.LotFutureHoldEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.hold.LotFutureHoldEditor"
            name="LotFutureHoldEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.procedurehold.ProcedureHoldEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.procedurehold.ProcedureHoldEditor"
            name="ProcedureHoldEditor">
      </editor>  
      <editor
            class="com.glory.mes.wip.advance.future.timer.StepTimerEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.timer.StepTimerEditor"
            name="StepTimerEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.timer.multitimer.MultiTimerEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.timer.multitimer.MultiTimerEditor"
            name="MutiTimerEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.timer.multiproc.MultiProcedureEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.timer.multiproc.MultiProcedureEditor"
            name="MultiProcedureEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.query.lot.LotFutureQueryEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.query.lot.LotFutureQueryEditor"
            name="LotFutureQueryEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.query.procedure.ProcedureFutureQueryEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.query.procedure.ProcedureFutureQueryEditor"
            name="ProcedureFutureQueryEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.query.procedurehold.ProcedureHoldQueryEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.query.procedurehold.ProcedureHoldQueryEditor"
            name="ProcedureHoldQueryEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.query.timer.TimerQueryEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.query.timer.TimerQueryEditor"
            name="TimerQueryEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.query.startedtimer.StartedTimerQueryEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.query.startedtimer.StartedTimerQueryEditor"
            name="StartedTimerQueryEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.step.FutureStepEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.step.FutureStepEditor"
            name="FutureStepEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.proceduresample.ProcedureSampleEditor"
            icon="icons/entity.gif"
            id="com.glory.mes.wip.advance.future.proceduresample.ProcedureSampleEditor"
            name="ProcedureSampleEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.pilotgroup.PilotGroupEditor"
            id="com.glory.mes.wip.advance.pilotgroup.PilotGroupEditor"
            name="PilotGroupEditor">
      </editor>
      <editor      
            class="com.glory.mes.wip.advance.future.procedurepilothold.process.ProcedureProcessPilotHoldEditor"
            id="com.glory.mes.wip.advance.future.procedurepilothold.process.ProcedureProcessPilotHoldEditor"
            name="ProcedurePilotHoldEditor">
      </editor>
      <editor
            class="com.glory.mes.wip.advance.future.procedurepilot.measure.ProcedureMeasurePilotEditor"
            id="com.glory.mes.wip.advance.future.procedurepilot.measure.ProcedureMeasurePilotEditor"
            name="ProcedureMeasurePilotEditor">
      </editor>
   </extension>

	<!--<extension
         point="com.glory.framework.base.managers">
         <manager
         	object="com.glory.mes.wip.model.PilotPlan"
         	class="com.glory.mes.wip.advance.pilotplan.PilotPlanProperties">
         </manager>
    </extension>-->
    
    <extension
         point="com.glory.framework.base.customfields">
		<composite 
         	name="MultiProcedureTimerCustomComposite"
         	class="com.glory.mes.wip.advance.custom.MultiProcedureTimerCustomComposite"
         	description="跨流程多工步定时器"
         	category="WIPADV">
 		</composite>
 		<composite 
     		name="MultiTimerCustomComposite"
         	class="com.glory.mes.wip.advance.custom.MultiTimerCustomComposite"
         	description="多工步定时器"
         	category="WIPADV">
 		</composite>
 		<composite 
         	name="ProcedureStepTimerCustomComposite"
         	class="com.glory.mes.wip.advance.custom.ProcedureStepTimerCustomComposite"
         	description="流程单工步定时器"
         	category="WIPADV">
 		</composite>
    </extension>
    
	<extension
         point="com.glory.framework.base.images">
      <image
            id="note"
            src="icons/note.gif">
      </image>
      <image
            id="hold"
            src="icons/hold.gif">
      </image>
      <image
            id="timer_start"
            src="icons/timer_start.png">
      </image>
      <image
            id="timer_end"
            src="icons/timer_end.jpg">
      </image>
      <image
            id="timer_started"
            src="icons/timer_started.png">
      </image>
      <image
            id="timer_qurey"
            src="icons/timer_query.png">
     </image>
     <image
            id="procedure_hold"
            src="icons/procedure_hold.png">
      </image>
      <image
            id="procedure_hold_query"
            src="icons/procedure_hold_query.png">
     </image>
     <image
            id="sample-setup"
            src="icons/sample-setup.png">
     </image>
     <image
            id="sample_start"
            src="icons/sample_start.png">
     </image>
     <image
            id="sample_end"
            src="icons/sample_end.png">
     </image>
     
     <image
            id="future_change_flow"
            src="icons/future_change_flow.png">
     </image>
     <image
            id="future_new_part"
            src="icons/future_new_part.png">
     </image>
     <image
            id="future_new_procedure"
            src="icons/future_new_procedure.png">
     </image>
     <image
            id="future_cancel_skip"
            src="icons/future_cancel_skip.gif">
     </image>
    </extension>
    
    <extension
      	point="com.glory.framework.base.tableitemadapters">
      	<adapter
        	name="FutureActionHis"
        	objectclass="com.glory.mes.wip.his.FutureActionHis"
        	adapterclass="com.glory.mes.wip.advance.future.hisquery.FutureActionHisQueryItemAdapter">
      	</adapter>
   </extension>
</plugin>
