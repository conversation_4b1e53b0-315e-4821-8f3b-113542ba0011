package com.glory.mes.prd.designer.command;

import java.util.ArrayList;
import java.util.List;

import com.glory.mes.prd.designer.common.command.AbstractEdgeCreateCommand;
import com.glory.mes.prd.designer.common.model.NamedElement;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.NotationElement;
import com.glory.mes.prd.designer.model.ConditionTransition;
import com.glory.mes.prd.designer.model.NodeElement;
import com.glory.mes.prd.designer.model.Transition;

public class EdgeCreateCommand extends AbstractEdgeCreateCommand {
	
	public void execute() {
		if(getTransitionSource().equals(getTransitionTarget())){
			return;//������Լ����Լ���ִ��
		}
		super.execute();
		
		getTransition().setSource(getTransitionSource());
		getTransition().setTo(getTransitionTarget().getName());
		getTransitionSource().addTransition(getTransition());
		initializeOrReadTransitionAttributes();
	}
	

	public void undo() {
		getTransitionSource().removeTransition(getTransition());
		getTransition().setSource(null);
	}
	
	protected void initializeOrReadTransitionAttributes() {
		initializeToAttribute();
		// �˴��Ѿ���֤˳����ȷ
		Transition[] transitions = getTransitionSource().getTransitions();
		int i = 0;
		for (Transition transition : transitions) {
			transition.setFromSeqNo(String.valueOf(i));
			i++;
		}
		
		refreshConditionTransitionName();
		
		edge.getLabel().setText(getTransition().getName());
	}
	
	/**
	 * ÿ������Transitionʱ����Ϊ���ǵ�����ɾ������������˳��ű仯��ˢ������ConditionTransitions��Label��ʾ
	 */
	private void refreshConditionTransitionName() {
		Transition[] transitions = getTransitionSource().getTransitions();
		List<ConditionTransition> conditionTransitions = new ArrayList<ConditionTransition>();
		for(Transition tran : transitions){
			if(tran instanceof ConditionTransition && !tran.equals(getTransition())){
				conditionTransitions.add((ConditionTransition) tran);
			}
		}
		
		for (ConditionTransition conditionTransition : conditionTransitions) {
			conditionTransition.setName(conditionTransition.getFromSeqNo() + ":" + conditionTransition.getCondition().getExpression());
		}
	}
	
	protected void initializeToAttribute() {
		List sourcePath = getPathToRootFrom(source);
		List targetPath = getPathToRootFrom(target);
		NotationElement common = findFirstCommonElement(sourcePath, targetPath);
		getTransition().setTo(getPrefix(sourcePath, common) + getSuffix(common, targetPath));
	}
	
	protected List getPathToRootFrom(NotationElement notationElement) {
		List result = new ArrayList();
		while (notationElement != null) {
			result.add(notationElement);
			if (notationElement instanceof Node) {
				notationElement = (NotationElement)((Node)notationElement).getContainer();
			} else {
				notationElement = null;
			}
		}
		return result;
	}
	
	protected NotationElement findFirstCommonElement(List sourcePath, List targetPath) {
		NotationElement result = null;
		int i = 1;
		while (i <= sourcePath.size() && i <= targetPath.size()) {
			if (sourcePath.get(sourcePath.size() - i) == targetPath.get(targetPath.size() - i)) {
				result = (NotationElement)sourcePath.get(sourcePath.size() - i);
			}
			i++;
		}
		return result;
	}
	
	protected String getPrefix(List sourcePath, NotationElement common) {
		StringBuffer result = new StringBuffer("");
		if (source != target) {
			int i = 1;
			while (i < sourcePath.size() && sourcePath.get(i) != common) {
				result.append("../");
				i++;
			}
		}
		return result.toString();
	}
	
	protected String getSuffix(NotationElement common, List targetPath) {
		StringBuffer result = new StringBuffer();
		if (source != target) {
			int i = 0;
			while (i < targetPath.size() && targetPath.get(i) != common) {
				result.insert(0, getSemanticElementName((NotationElement)targetPath.get(i++)));
				if (i < targetPath.size() && targetPath.get(i) != common) {
					result.insert(0, "/");
				}
			}
		} else {
			result.append(getSemanticElementName(common));
		}
		return result.toString();
	}
	
	protected String getSemanticElementName(NotationElement notationElement) {
		return ((NamedElement)notationElement.getSemanticElement()).getName();
	}
	
	protected NodeElement getTransitionSource() {
		return (NodeElement)source.getSemanticElement();
	}
	
	protected NodeElement getTransitionTarget() {
		return (NodeElement)target.getSemanticElement();
	}
	
	protected Transition getTransition() {
		return (Transition)edge.getSemanticElement();
	}
}

