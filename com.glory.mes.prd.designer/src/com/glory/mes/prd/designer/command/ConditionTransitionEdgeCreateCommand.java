package com.glory.mes.prd.designer.command;

import org.eclipse.draw2d.geometry.Dimension;
import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.mes.prd.designer.common.notation.BendPoint;
import com.glory.mes.prd.designer.dialog.ConditionTransitionDialog;
import com.glory.mes.prd.designer.model.Condition;
import com.glory.mes.prd.designer.model.ConditionTransition;

public class ConditionTransitionEdgeCreateCommand extends EdgeCreateCommand {
	
	IEventBroker eventBroker;
	
	public ConditionTransitionEdgeCreateCommand(IEventBroker eventBroker) {
		super();
		this.eventBroker = eventBroker;
	}
	
	@Override
	public void execute() {
		super.execute();
		
		if (getTransition() instanceof ConditionTransition) {
			ConditionTransition conditionTransition = (ConditionTransition) getTransition();

			String expression = "";
			if (conditionTransition.getCondition() != null) {
				expression = conditionTransition.getCondition().getExpression();
			}
			
			ConditionTransitionDialog propertyDialog = new ConditionTransitionDialog("ConditionTransitionDialog", expression, eventBroker);
			int result = propertyDialog.open();
			if(result == Dialog.OK) {
				if (getTransition().getCondition() == null) {
					getTransition().setCondition(new Condition());
				}
				
				getTransition().getCondition().setExpression(propertyDialog.getValue().getName());
				getTransition().setName(getTransition().getFromSeqNo() + ":" + getTransition().getCondition().getExpression());
				getTransition().getDescription().setDescription(propertyDialog.getValue().getDescription());
			} else {
				undo();
			}
		}
	}
	
	protected void addBendPoints() {
		Rectangle sourceConstraint = source.getConstraint();
		Rectangle targeConstraint = target.getConstraint();

		int sourceX = sourceConstraint.x + sourceConstraint.width;
		int sourceY = sourceConstraint.y + sourceConstraint.height;
		int targetX = targeConstraint.x + targeConstraint.width;
		int targetY = targeConstraint.y + targeConstraint.height;

		int horizontal = (targetX - sourceX) / 2;
		int vertical = (targetY - sourceY) / 2;

		int offsetH = 25 * vertical / (Math.abs(horizontal) + Math.abs(vertical));
		if (horizontal != 0) {
			offsetH = offsetH * horizontal / Math.abs(horizontal);
		}
		
		int offsetV = 25 * horizontal / (Math.abs(horizontal) + Math.abs(vertical));
		if (vertical != 0) {
			offsetV = offsetV * vertical / Math.abs(vertical);
		}

		int dimH = 0;
		if (horizontal > 0) {
			dimH = horizontal + offsetH;
		} else {
			dimH = horizontal - offsetH;
		}

		int dimV = 0;
		if (vertical > 0) {
			dimV = vertical - offsetV;
		} else {
			dimV = vertical + offsetV;
		}

		BendPoint first = new BendPoint();
		first.setRelativeDimensions(new Dimension(dimH, dimV), new Dimension(dimH, dimV));
		edge.addBendPoint(first);
	}
}
