package com.glory.mes.prd.designer.command;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.gef.commands.Command;

import com.glory.mes.prd.designer.common.command.AbstractEdgeDeleteCommand;
import com.glory.mes.prd.designer.common.command.AbstractNodeDeleteCommand;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.notation.Edge;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.NodeContainer;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.NodeElement;
import com.glory.mes.prd.designer.model.NodeElementContainer;
import com.glory.mes.prd.designer.model.ProcessDefinition;
import com.glory.mes.prd.designer.model.StartState;


public class NodeDeleteCommand extends AbstractNodeDeleteCommand {

	protected void doAdd() {
		SemanticElement toAdd = node.getSemanticElement();
		NodeElementContainer nodeElementContainer = (NodeElementContainer)parent.getSemanticElement();
		if (toAdd instanceof StartState) {
			((ProcessDefinition)nodeElementContainer).addStartState((StartState)toAdd);
		} else {
			nodeElementContainer.addNodeElement((NodeElement)toAdd);
		}
	}
	
	protected void doRemove() {
		SemanticElement toRemove = node.getSemanticElement();
		NodeElementContainer nodeElementContainer = (NodeElementContainer)parent.getSemanticElement();
		if (toRemove instanceof StartState) {
			((ProcessDefinition)nodeElementContainer).removeStartState((StartState)toRemove);
		} else {
			if(toRemove instanceof IfState){
				IfState ifState = (IfState)toRemove;
				ElseState elseState = (ElseState) findSemanticElement(nodeElementContainer, ifState.getElseState().getName());
				EndIfState endIfState = (EndIfState) findSemanticElement(nodeElementContainer, elseState.getEndIfState().getName());
				
				//ɾ��������
				Node elseNode = findSemanticElementNode(node.getContainer(), elseState);
				deleteEdges(elseNode.getArrivingEdges());
				deleteEdges(elseNode.getLeavingEdges());
				
				Node endIfNode = findSemanticElementNode(node.getContainer(), endIfState);
				deleteEdges(endIfNode.getArrivingEdges());
				deleteEdges(endIfNode.getLeavingEdges());
				
				//ɾ���ڵ�
				nodeElementContainer.removeNodeElement(ifState);
				nodeElementContainer.removeNodeElement(elseState);
				nodeElementContainer.removeNodeElement(endIfState);
			}else if(toRemove instanceof ElseState){
				ElseState elseState = (ElseState)toRemove;				
				IfState ifState = new IfState();
				String elseName = elseState.getName();
				ifState.setName("IF"+elseName.charAt(elseName.length()-1));
				ifState = (IfState) findSemanticElement(nodeElementContainer, ifState.getName());
				EndIfState endIfState = (EndIfState) findSemanticElement(nodeElementContainer, elseState.getEndIfState().getName());
				
				//ɾ��������
				Node ifNode = findSemanticElementNode(node.getContainer(), ifState);
				deleteEdges(ifNode.getArrivingEdges());
				deleteEdges(ifNode.getLeavingEdges());
				
				Node endIfNode = findSemanticElementNode(node.getContainer(), endIfState);
				deleteEdges(endIfNode.getArrivingEdges());
				deleteEdges(endIfNode.getLeavingEdges());
				
				//ɾ���ڵ�
				nodeElementContainer.removeNodeElement(elseState);
				nodeElementContainer.removeNodeElement(ifState);
				nodeElementContainer.removeNodeElement(endIfState);
			} else if(toRemove instanceof EndIfState){
				EndIfState endIfState = (EndIfState)toRemove;
				String endIfName = endIfState.getName();
				
				IfState ifState = new IfState();
				ifState.setName("IF"+endIfName.charAt(endIfName.length()-1));
				ifState = (IfState) findSemanticElement(nodeElementContainer, ifState.getName());
				
				ElseState elseState = new ElseState();			
				elseState.setName("ELSE"+endIfName.charAt(endIfName.length()-1));
				elseState = (ElseState) findSemanticElement(nodeElementContainer, elseState.getName());
				
				//ɾ��������
				Node ifNode = findSemanticElementNode(node.getContainer(), ifState);
				deleteEdges(ifNode.getArrivingEdges());
				deleteEdges(ifNode.getLeavingEdges());
				
				Node elseNode = findSemanticElementNode(node.getContainer(), elseState);
				deleteEdges(elseNode.getArrivingEdges());
				deleteEdges(elseNode.getLeavingEdges());
				
				//ɾ���ڵ�
				nodeElementContainer.removeNodeElement(elseState);
				nodeElementContainer.removeNodeElement(ifState);
				nodeElementContainer.removeNodeElement(endIfState);
			} else{
				nodeElementContainer.removeNodeElement((NodeElement)toRemove);
			}
		}
	}

	private void deleteEdges(List<Edge> edges) {
		List<Command> edgeDeleteCommands = new ArrayList<Command>();
		for (int i = 0; i < edges.size(); i++) {
			AbstractEdgeDeleteCommand command = createEdgeDeleteCommand();
			command.setEdge(edges.get(i));
			edgeDeleteCommands.add(command);
		}
		executeCommands(edgeDeleteCommands);
	}
	
	private void executeCommands(List<Command> commands) {
		for (int i = 0; i < commands.size(); i++) {
			((Command)commands.get(i)).execute();
		}
	}

	protected AbstractEdgeDeleteCommand createEdgeDeleteCommand() {
		return new EdgeDeleteCommand();
	}
	
	private Node findSemanticElementNode(NodeContainer nodeContainer, SemanticElement element){
		List<Node> nodes = nodeContainer.getNodes();
		for(Node nod : nodes){
			SemanticElement nodeSemanticElement = nod.getSemanticElement();
			if(nodeSemanticElement.getClass() == element.getClass()){
				String name1 = ((AbstractNode)nodeSemanticElement).getName();
				String name2 = ((AbstractNode)element).getName();
				if(name1 != null && name2 != null){
					if(name1.trim().equals(name2.trim())){
						return nod;
					}
				}
			}
		}
		return null;
	}
	
	private SemanticElement findSemanticElement(NodeElementContainer nodeElementContainer, String name){
		return nodeElementContainer.getNodeElementByName(name);
	}
}
