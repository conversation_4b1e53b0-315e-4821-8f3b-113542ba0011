package com.glory.mes.prd.designer.command;

import java.util.List;

import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.framework.base.ui.util.UI;
import com.glory.mes.prd.designer.common.command.AbstractNodeCreateCommand;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.NodeContainer;
import com.glory.mes.prd.designer.dialog.AvailablePrdDialog;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.EndState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.NodeElementContainer;
import com.glory.mes.prd.designer.model.ProcessDefinition;
import com.glory.mes.prd.designer.model.StartState;


public class NodeCreateCommand extends AbstractNodeCreateCommand {
		
	public void execute() {
		super.execute();
		if(getAbstractNode() instanceof StartState 
				|| getAbstractNode() instanceof EndState || getAbstractNode() instanceof ElseState 
				|| getAbstractNode() instanceof EndIfState){
		    setName();
            addAbstractNode(getNodeElementContainer(), getAbstractNode());
            if(getAbstractNode() instanceof ElseState){
            	addAbstractNode(getNodeElementContainer(), ((ElseState)getAbstractNode()).getEndIfState());
            }
		} else {
			getAbstractNode().initializeName(getNodeElementContainer());
			node.setContainer((NodeContainer) parent);
			AvailablePrdDialog propertyDialog = new AvailablePrdDialog(UI.getActiveShell(),	node);
			int result = propertyDialog.open();
			if(result == Dialog.OK) {
				if (getAbstractNode() instanceof IfState){
					IfState ifState = (IfState)getAbstractNode();
					ElseState elseState = ((IfState)getAbstractNode()).getElseState();
					elseState.initializeName(getNodeElementContainer());
					ifState.setElseState(elseState);
					if(elseState != null && elseState.getEndIfState() != null){
						EndIfState endIfState = elseState.getEndIfState();
						endIfState.initializeName(getNodeElementContainer());
						elseState.setEndIfState(endIfState);
						addAbstractNode(getNodeElementContainer(), ifState);
						addAbstractNode(getNodeElementContainer(), elseState);
						addAbstractNode(getNodeElementContainer(), endIfState);
						
						changeSemanticElementNodeConstraintByLastNode(node.getContainer(), elseState);
						changeSemanticElementNodeConstraintByLastNode(node.getContainer(), endIfState);
					}
				} else {
					addAbstractNode(getNodeElementContainer(), getAbstractNode());
				}
			}
			
		}
	}
	
	public void addAbstractNode(NodeElementContainer nodeElementContainer, AbstractNode abstractNode) {
		if (abstractNode instanceof StartState && nodeElementContainer instanceof ProcessDefinition) {
			((ProcessDefinition)nodeElementContainer).addStartState((StartState)abstractNode);
		} else {
			nodeElementContainer.addNodeElement(abstractNode);
		}
	}
	
	private void removeAbstractNode(NodeElementContainer nodeElementContainer, AbstractNode abstractNode) {
		if (abstractNode instanceof StartState && nodeElementContainer instanceof ProcessDefinition) {
			((ProcessDefinition)nodeElementContainer).removeStartState((StartState)abstractNode);
		} else {
			nodeElementContainer.removeNodeElement(abstractNode);
		}
	}
	
	public NodeElementContainer getNodeElementContainer() {
		return (NodeElementContainer)parent.getSemanticElement();
	}
	
	public AbstractNode getAbstractNode() {
		return (AbstractNode)node.getSemanticElement();
	}
	protected void setAbstractNode(AbstractNode abNode){
		node.setSemanticElement(abNode);
	}
	
	public boolean canExecute() {
		return getNodeElementContainer().canAdd(getAbstractNode());
	}
	
	protected void setName() {
		if (getAbstractNode().getName() == null) {
			if(getAbstractNode() instanceof StartState){
				getAbstractNode().setName("START");
			} else if(getAbstractNode() instanceof EndState) {
				getAbstractNode().setName("END");
			} else if(getAbstractNode() instanceof ElseState 
					|| getAbstractNode() instanceof EndIfState) {
				getAbstractNode().initializeName(getNodeElementContainer());
			}
		}		
	}
	
	private void changeSemanticElementNodeConstraintByLastNode(NodeContainer nodeContainer, SemanticElement element){
		List<Node> nodes = nodeContainer.getNodes();
		Node lastNode = null;
		for(Node nod : nodes){
			SemanticElement nodeSemanticElement = nod.getSemanticElement();
			if(nodeSemanticElement.getClass() == element.getClass()){
				String name1 = ((AbstractNode)nodeSemanticElement).getName();
				String name2 = ((AbstractNode)element).getName();
				if(name1 != null && name2 != null){
					if(name1.trim().equals(name2.trim()) && lastNode != null){
						Rectangle constraint = lastNode.getConstraint().getCopy();
						constraint.setY(constraint.y + constraint.height + 10);
						nod.setConstraint(constraint);
					}
				}
			}
			lastNode = nod;
		}
	}
	
	public void undo() {
		removeAbstractNode(getNodeElementContainer(), getAbstractNode());
	}
	
}
