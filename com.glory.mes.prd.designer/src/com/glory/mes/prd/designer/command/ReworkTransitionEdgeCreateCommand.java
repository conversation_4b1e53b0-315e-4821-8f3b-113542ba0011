package com.glory.mes.prd.designer.command;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.draw2d.geometry.Dimension;
import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.jface.dialogs.Dialog;

import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.designer.common.notation.BendPoint;
import com.glory.mes.prd.designer.dialog.EdgePropertyEditDialog;
import com.glory.mes.prd.designer.model.ReworkTransition;
import com.glory.mes.prd.designer.model.Transition;

public class ReworkTransitionEdgeCreateCommand extends EdgeCreateCommand {
	
	@Override
	public void execute() {
		super.execute();
		
		if(getTransition() instanceof ReworkTransition){
			EdgePropertyEditDialog propertyDialog = new EdgePropertyEditDialog(UI.getActiveShell(), edge);
			int result = propertyDialog.open();
			if(result == Dialog.OK) {
			} else {
				undo();
			}
		}
	}
	
	@Override
	protected void initializeOrReadTransitionAttributes() {
		super.initializeOrReadTransitionAttributes();
		
		Transition[] transitions = getTransitionSource().getTransitions();
		List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
		for(Transition tran : transitions){
			if(tran instanceof ReworkTransition){
				reworkTransitions.add((ReworkTransition) tran);
			}
		}
		
		if (getTransition() instanceof ReworkTransition && reworkTransitions.size() > 0) {//transition����Ҫname
			if(StringUtil.isEmpty(getTransition().getName())){
				getTransition().setName(getTransition().getTo());
			}
		}
	}
	
	protected void addBendPoints() {
		Rectangle sourceConstraint = source.getConstraint();
		Rectangle targeConstraint = target.getConstraint();
		
		int sourceX = sourceConstraint.x  + sourceConstraint.width;
		int sourceY = sourceConstraint.y  + sourceConstraint.height;
		int targetX = targeConstraint.x  + targeConstraint.width;
		int targetY = targeConstraint.y  + targeConstraint.height;
		
		int horizontal = (targetX - sourceX)/2;
		int vertical = (targetY - sourceY)/2;
		
		int offsetH = 25 * vertical / (Math.abs(horizontal) + Math.abs(vertical));
		if (horizontal != 0) {
			offsetH = offsetH * horizontal / Math.abs(horizontal);
		}
		
		int offsetV = 25 * horizontal / (Math.abs(horizontal) + Math.abs(vertical));
		if (vertical != 0) {
			offsetV = offsetV * vertical / Math.abs(vertical);
		}
		
		int dimH = 0;
		if (horizontal > 0) {
			dimH = horizontal + offsetH;;
		} else {
			dimH = horizontal - offsetH;;
		}
		
		int dimV = 0;
		if (vertical > 0) {
			dimV = vertical - offsetV;
		} else {
			dimV = vertical + offsetV;
		}
		
		BendPoint first = new BendPoint();
		first.setRelativeDimensions(new Dimension(dimH, dimV), new Dimension(dimH, dimV));
		edge.addBendPoint(first);
	}
}
