package com.glory.mes.prd.designer.model;

public class ReworkState extends AbstractNode {

	private SubProcedure procedure;
	private String isDynamic = "N";
	
	private String objectType = null;
	private String target = null;
	
	public SubProcedure getProcedure() {
		if(procedure == null){
			procedure = (SubProcedure) getFactory().createById("SubProcedure");
		}
		return procedure;
	}

	public void setProcedure(SubProcedure procedure) {
		this.procedure = procedure;
	}
	
	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String newObjectType) {
		String oldObjectType = objectType;
		this.objectType = newObjectType;
		firePropertyChange("objectType", oldObjectType, newObjectType);
	}

	public String getTarget() {
		return target;
	}

	public void setTarget(String newTarget) {
		String oldTarget = target;
		this.target = newTarget;
		firePropertyChange("target", oldTarget, newTarget);
	}

	public void setIsDynamic(String newIsDynamic) {
		String oldIsDynamic = isDynamic;
		this.isDynamic = newIsDynamic;
		firePropertyChange("isdynamic", oldIsDynamic, newIsDynamic);
	}

	public String getIsDynamic() {
		return this.isDynamic; 
	}

	@Override
	public String getElementType() {
		return ELEMENT_TYPE_REWORKSTATE;
	}
}
