package com.glory.mes.prd.designer.model;

public class ElseState extends AbstractNode {
	protected EndIfState endIfState;
	
	@Override
	public boolean isPossibleChildOf(NodeElementContainer nodeElementContainer) {
		int i=0;
		int j=0;
		for(NodeElement element : nodeElementContainer.getNodeElements()){
			if(element instanceof IfState){
				i++;
			}else if(element instanceof ElseState){
				j++;
			}
		}
		return (j<i);
	}

	
	public void setEndIfState(EndIfState endIfState) {
		this.endIfState = endIfState;
	}

	public EndIfState getEndIfState() {
		if(endIfState==null){
			endIfState=(EndIfState)getFactory().createById("EndIfState");
		}
		return endIfState;
	}
}
