package com.glory.mes.prd.designer.model;


public class EndState extends AbstractNode {
	
	private String isTermiate;
	
	@Override
	public boolean isPossibleChildOf(NodeElementContainer nodeElementContainer) {
		return nodeElementContainer instanceof ProcessDefinition && ((ProcessDefinition)nodeElementContainer).getEndState() == null;
	}
	
	public void setIsTermiate(Boolean isTermiate) {
		if(isTermiate == null) {
			this.isTermiate = "N";
		}else {
			this.isTermiate = isTermiate ? "Y" : "N";
		}
	}
	
	public Boolean getIsTermiate() {
		return "Y".equalsIgnoreCase(this.isTermiate) ? true : false; 
	}

//	public void addTransition(Transition transition) {
//		// No transitions can be added to a decision node
//	}
//	
//	public void removeTransition(Transition transition) {
//		// No transitions can be added to a decision node
//	}
//	
//	public Transition[] getTransitions() {
//		// No transitions can be added to a decision node
//		return new Transition[0];
//	}

}
