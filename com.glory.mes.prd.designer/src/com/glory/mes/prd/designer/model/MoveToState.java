package com.glory.mes.prd.designer.model;

public class MoveToState extends AbstractNode {

	private String target;

	private String isDynamic = "N";
	
	public String getTarget() {
		return target;
	}
	
	public void setTarget(String newTarget) {
		String oldTarget = target;
		this.target = newTarget;
		firePropertyChange("target", oldTarget, newTarget);
	}
	
	public String getIsDynamic() {
		return this.isDynamic; 
	}
	
	public void setIsDynamic(String newIsDynamic) {
		String oldIsDynamic = isDynamic;
		this.isDynamic = newIsDynamic;
		firePropertyChange("isdynamic", oldIsDynamic, newIsDynamic);
	}

}
