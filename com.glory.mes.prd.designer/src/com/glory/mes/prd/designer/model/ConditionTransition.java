package com.glory.mes.prd.designer.model;

import com.glory.framework.core.util.StringUtil;

public class ConditionTransition extends Transition {
	
	public ConditionTransition() {
		super();
	}
	
	public void setName(String newName) {
		// ���ʽ����24λ��ʡ�Ժű�ʾ
		if (!StringUtil.isEmpty(newName) && newName.length() > 24) {
			newName = newName.substring(0, 24) + "...";
		}
		
		String oldName = name;
		name = newName;
		firePropertyChange("name", oldName, newName);
	}
	
	public String getName() {
		return name;
	}
	
	@Override
	public String getType() {
		return "conditionTransition-take";
	}
	
	@Override
	public String getElementType() {
		return ELEMENT_TYPE_CONDITIONTRANSITION;
	}
}
