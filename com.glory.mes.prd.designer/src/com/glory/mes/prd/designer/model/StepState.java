package com.glory.mes.prd.designer.model;

import java.util.ArrayList;
import java.util.List;

import com.glory.mes.prd.designer.model.AbstractNode;

public class StepState extends AbstractNode {
	
	private SubStep step;
	private List reworkTransitions = new ArrayList();
	
	public void setStep(SubStep newStep) {
		step = newStep;
	}
	
	public SubStep getStep() {
		if (step == null) {
			step = (SubStep)getFactory().createById("SubStep");
		}
		return step;
	}

	public List getReworkTransitions() {
		return reworkTransitions;
	}

	public void setReworkTransitions(List reworkTransitions) {
		this.reworkTransitions = reworkTransitions;
	}
	
}
