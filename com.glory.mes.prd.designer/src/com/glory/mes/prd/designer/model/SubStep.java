package com.glory.mes.prd.designer.model;

import com.glory.mes.prd.designer.common.model.AbstractNamedElement;

public class SubStep extends AbstractNamedElement {
	
	private String version;
	private String stageId;
	private String reworkName;
	private String reworkVersion;
	private String isBack = "N";
	
	public void setVersion(String newVersion) {
		String oldVersion = version;
		version = newVersion;
		firePropertyChange("version", oldVersion, newVersion);
	}
	
	public String getVersion() {
		return version;
	}
	
	public void setStageId(String newStageId) {
		String oldStageId = stageId;
		stageId = newStageId;
		firePropertyChange("stageid", oldStageId, newStageId);
	}
	
	public String getStageId() {
		return stageId;
	}
	
	public void setReworkName(String newReworkName) {
		String oldReworkName = reworkName;
		reworkName = newReworkName;
		firePropertyChange("reworkname", oldReworkName, newReworkName);
	}
	
	public String getReworkName() {
		return reworkName;
	}
	
	public void setReworkVersion(String newReworkVersion) {
		String oldReworkVersion = reworkVersion;
		reworkVersion = newReworkVersion;
		firePropertyChange("reworkversion", oldReworkVersion, newReworkVersion);
	}
	
	public String getReworkVersion() {
		return reworkVersion;
	}
	
	public void setIsBack(String newIsBack) {
		String oldIsBack = isBack;
		isBack = newIsBack;
		firePropertyChange("isback", oldIsBack, newIsBack);
	}
	
	public String getIsBack() {
		return isBack;
	}
	public String getName() {
		if (super.getName() == null) {
			setName("");
		}
		return super.getName();
	}
}
