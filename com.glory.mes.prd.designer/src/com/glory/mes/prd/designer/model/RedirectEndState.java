package com.glory.mes.prd.designer.model;


public class RedirectEndState extends AbstractNode {
	
	private String hiSuperInstruction = null;
	
	public void setHiSuperInstruction(String newHiSuperInstruction) {
		String oldHiSuperInstruction = hiSuperInstruction;
		this.hiSuperInstruction = newHiSuperInstruction;
		firePropertyChange("hiSuperInstruction", oldHiSuperInstruction, newHiSuperInstruction);
	}
	
	public String getHiSuperInstruction() {
		return hiSuperInstruction;
	}
	
	@Override
	public String getElementType() {
		return ELEMENT_TYPE_REDIRECT_END_STAET;
	}

}
