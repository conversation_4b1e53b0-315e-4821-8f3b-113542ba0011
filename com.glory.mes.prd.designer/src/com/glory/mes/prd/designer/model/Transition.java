package com.glory.mes.prd.designer.model;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.designer.common.model.NamedElement;

public class Transition extends Event implements NamedElement, DescribableElement {
	
	private List exceptionHandlers = new ArrayList();
	
	private Condition condition;
	
	private String to;
	protected String name;
	private String fromSeqNo;
	private String desc;
	private Description description;
	
	
	private NodeElement source;
	
	public void setDescription(Description newDescription) {
		Description oldDescription = description;
		description = newDescription;
		firePropertyChange("description", oldDescription, newDescription);
	}
	
	public Description getDescription() {
		if(description == null){
			description = (Description) getFactory().createById("Description");
		}
		return description;
	}
	
	public Condition getCondition() {
		return condition;
	}
	
	public void setCondition(Condition newCondition) {
		Condition oldCondition = condition;
		condition = newCondition;
		firePropertyChange("condition", oldCondition, newCondition);
	}

	public void setType(String newType) {
		// Transitions cannot change their type.
	}
	
	public String getType() {
		return "transition-take";
	}
	
	public void setTo(String newTo) {
		String oldTo = to;
		to = newTo;
		firePropertyChange("to", oldTo, newTo);
	}
	
	public String getTo() {
		return to;
	}
	
	public void setName(String newName) {
		String oldName = name;
		name = newName;
		firePropertyChange("name", oldName, newName);
	}
	
	public String getName() {
		return name;
	}
	
	public boolean isNameMandatory() {
		return false;
	}
	
	public void setSource(NodeElement newSource) {
		this.source = newSource;
	}
	
	public NodeElement getSource() {
		return source;
	}
	
	@Override
	public String getElementType() {
		return "transition";
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String newDesc) {
		String oldDesc = this.desc;
		this.desc = newDesc;
		firePropertyChange("desc", oldDesc, newDesc);
	}

	public String getFromSeqNo() {
		return fromSeqNo;
	}

	public void setFromSeqNo(String fromSeqNo) {
		String oldFromSeqNo = this.fromSeqNo;
		this.fromSeqNo = fromSeqNo;
		firePropertyChange("fromSeqNo", oldFromSeqNo, fromSeqNo);
	}

	@Override
	public String getToolTip() {
		return null;
	}
}
