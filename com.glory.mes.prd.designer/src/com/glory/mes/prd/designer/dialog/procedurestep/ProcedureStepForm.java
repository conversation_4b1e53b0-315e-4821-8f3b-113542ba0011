package com.glory.mes.prd.designer.dialog.procedurestep;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.nattable.TableViewerManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class ProcedureStepForm extends Composite {

	public static final String TABLE_PROCEDURE_STEP = "PRDDesignerProcedureStep";

    protected ManagedForm mform;
    protected TableViewerManager tableManager;
    private List<ProcedureStepModel> procedureStepModels;
    
    public ProcedureStepForm(Composite parent, int style, List<ProcedureStepModel> procedureStepModels) {
        super(parent, style);
        this.procedureStepModels = procedureStepModels;
        createForm();
    }

    public void createForm() {
        FormToolkit toolkit = new FormToolkit(getDisplay());

        this.setLayoutData(new GridData(GridData.FILL_BOTH));
        GridLayout layout = new GridLayout(1, false);
        layout.verticalSpacing = 0;
        layout.horizontalSpacing = 0;
        layout.marginWidth = 0;
        layout.marginHeight = 0;
        setLayout(layout);

        ScrolledForm sform = toolkit.createScrolledForm(this);
        sform.setLayoutData(new GridData(GridData.FILL_BOTH));
        mform = new ManagedForm(toolkit, sform);

        Composite body = sform.getBody();
        layout = new GridLayout();
        body.setLayout(layout);
        body.setLayoutData(new GridData(GridData.FILL_BOTH));

        createTableComponent(body, toolkit);
    }

    protected void createTableComponent(Composite composite, FormToolkit toolkit) {
        Composite tableContainer = toolkit.createComposite(composite, SWT.NULL);
        tableContainer.setLayout(new GridLayout());
        tableContainer.setLayoutData(new GridData(GridData.FILL_BOTH));
        try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), TABLE_PROCEDURE_STEP);

			tableManager = new TableViewerManager(adTable);
			tableManager.newViewer(tableContainer);
			tableManager.setInput(procedureStepModels);
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
    }
}
