package com.glory.mes.prd.designer.dialog;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADRefTable;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.activeentity.model.ADURefList;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.forms.field.RefTableField;
import com.glory.framework.base.ui.forms.field.TextField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.runtime.Framework;
import com.glory.mes.base.model.ParameterDefinition;
import com.glory.mes.prd.workflow.graph.node.IfState;

public class AvailableIfExpressionAddDialog extends BaseTitleDialog {

	protected RefTableField objectTypeField;
	protected RefTableField ifParameterField;
	protected RefTableField ifParameterComparisonField;	
	protected TextField ifParameterValueField;

	protected void createFormContent(Composite composite) {
		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		
		Composite conditionComposite = new Composite(body, SWT.NONE);
		conditionComposite.setLayout(new GridLayout(2, false));
		conditionComposite.setBackground(new Color(null, 255, 255, 255));
		conditionComposite.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_type"));
		objectTypeField = createObjectTypeField(conditionComposite, toolkit);			
		toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_name"));
		ifParameterField = createIfParameterField(conditionComposite, toolkit);			
		toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_comparison"));
		ifParameterComparisonField = createIfParameterComparisonField(conditionComposite, toolkit);			
		toolkit.createLabel(conditionComposite, Message.getString("prd.if_parameter_value"));
		ifParameterValueField = createIfParameteValueField(conditionComposite, toolkit);
		
	}
	
	public RefTableField createObjectTypeField(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			
			ListTableManager tableManager = new ListTableManager(adTable);
			List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(), 
					Env.getMaxResult(), "referenceName = 'IfStatusObjectType'", adTable.getOrderByClause());	
			list.add(new ADURefList());
			tableManager.setInput(list);
			
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("key");
			adRefTable.setTextField("text");
			objectTypeField = new RefTableField("objectType", tableManager, adRefTable, SWT.NONE);
			objectTypeField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						ADManager adManager = Framework.getService(ADManager.class);
						if (objectTypeField.getValue() != null && !"".equals(objectTypeField.getValue().toString())) {		
							if (IfState.OBJECTTYPE_PARAMETERS.equals(objectTypeField.getValue())) {						
								List<ParameterDefinition> parameters = adManager.getEntityList(Env.getOrgRrn(), ParameterDefinition.class, 
										Env.getMaxResult(), "", "");	
								List<ADField> Fields = new ArrayList<ADField>();
								for (ParameterDefinition parameter : parameters) {
									ADField field = new ADField();
									field.setName(parameter.getName());
									field.setDescription(parameter.getDescription());
									field.setDataType(parameter.getType());
									Fields.add(field);
								}
								ifParameterField.setInput(Fields);								
							} else if (IfState.OBJECTTYPE_LOT.equals(objectTypeField.getValue())) {
								ADTable lotTable = adManager.getADTable(Env.getOrgRrn(), "PRDIfStatusParameterLot");
								List<ADField> Fields = lotTable.getFields();
								ifParameterField.setInput(Fields);								
							} else if (IfState.OBJECTTYPE_PART.equals(objectTypeField.getValue())) {							
								ADTable partTable = adManager.getADTable(Env.getOrgRrn(), "PRDIfStatusParameterPart");	
								List<ADField> Fields = partTable.getFields();
								ifParameterField.setInput(Fields);
							}
							ifParameterField.setValue(null);
							ifParameterComparisonField.setValue(null);
							ifParameterValueField.setValue(null);
							ifParameterValueField.setText("");
						}	
					} catch (Exception e) {
						e.printStackTrace();
					}
				}				
			});			
			objectTypeField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return objectTypeField;
	}
	
	public RefTableField createIfParameterField(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADField");
					
			ListTableManager tableManager = new ListTableManager(adTable);
			
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("name");
			adRefTable.setTextField("name");
			ifParameterField = new RefTableField("ifParameter", tableManager, adRefTable, SWT.NONE);
			ifParameterField.addValueChangeListener(new IValueChangeListener() {
				@Override
				public void valueChanged(Object arg0, Object arg1) {
					try {
						ifParameterComparisonField.setValue(null);
						ifParameterValueField.setValue(null);
						ifParameterValueField.setText("");
					} catch (Exception e) {
						e.printStackTrace();
					}
				}				
			});			
			ifParameterField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifParameterField;
	}
	
	public RefTableField createIfParameterComparisonField(Composite top, FormToolkit toolkit) {
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable adTable = adManager.getADTable(Env.getOrgRrn(), "ADRefListView");
			
			ListTableManager tableManager = new ListTableManager(adTable);
			List<ADBase> list = adManager.getEntityList(Env.getOrgRrn(), adTable.getObjectRrn(), 
					Env.getMaxResult(), "referenceName = 'IfStatusParameterComparison'", adTable.getOrderByClause());	
			list.add(new ADURefList());
			tableManager.setInput(list);
			
			ADRefTable adRefTable = new ADRefTable();
			adRefTable.setTableRrn(adTable.getObjectRrn());
			adRefTable.setKeyField("key");
			adRefTable.setTextField("text");
			ifParameterComparisonField = new RefTableField("ifParameterComparison", tableManager, adRefTable, SWT.NONE);				
			ifParameterComparisonField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifParameterComparisonField;
	}
	
	public TextField createIfParameteValueField(Composite top, FormToolkit toolkit) {
		try {
			ifParameterValueField = new TextField("ifParameterValue", SWT.NONE);
			ifParameterValueField.createContent(top, toolkit);
		} catch (Exception e) {
			e.printStackTrace();
		}		
		return ifParameterValueField;
	}

	@Override
    protected void okPressed() {
		if (validate()) {
			super.okPressed();
		}
    }
	
	public boolean validate() {
		if (ifParameterField.getValue() == null || "".equals(ifParameterField.getValue())) {
    		UI.showError(Message.getString("prd.if_parameter_is_required"));
    		return false;
    	}
    	if (ifParameterValueField.getValue() == null || "".equals(ifParameterValueField.getValue())) {
    		UI.showError(Message.getString("prd.if_parametervalue_is_required"));
    		return false;
    	}
    	return true;
	}
	
	public RefTableField getObjectTypeField() {
		return objectTypeField;
	}

	public void setObjectTypeField(RefTableField objectTypeField) {
		this.objectTypeField = objectTypeField;
	}

	public RefTableField getIfParameterField() {
		return ifParameterField;
	}

	public void setIfParameterField(RefTableField ifParameterField) {
		this.ifParameterField = ifParameterField;
	}

	public RefTableField getIfParameterComparisonField() {
		return ifParameterComparisonField;
	}

	public void setIfParameterComparisonField(
			RefTableField ifParameterComparisonField) {
		this.ifParameterComparisonField = ifParameterComparisonField;
	}

	public TextField getIfParameterValueField() {
		return ifParameterValueField;
	}

	public void setIfParameterValueField(TextField ifParameterValueField) {
		this.ifParameterValueField = ifParameterValueField;
	}

	@Override
	protected Control buildView(Composite parent) {
	//	Composite composite = (Composite) super.createDialogArea(parent);
        setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        setTitle(Message.getString("prd.if_parameter_expression"));
        createFormContent(parent);
        return parent;
	}
	
}
