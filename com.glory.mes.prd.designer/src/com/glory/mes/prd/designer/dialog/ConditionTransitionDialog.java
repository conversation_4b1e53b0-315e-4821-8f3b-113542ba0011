package com.glory.mes.prd.designer.dialog;


import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.jface.dialogs.IDialogConstants;

import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.field.CustomField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;

public class ConditionTransitionDialog extends GlcBaseDialog { 

	private static final String FORM_NAME = "Form";
	private static final String FIELD_NAME = "name";
	
	private SelectedPrdModel value;
	private String expression;

	protected CustomField nameField;

	public ConditionTransitionDialog(String adFormName, String expression, IEventBroker eventBroker) {
		super(adFormName, null, eventBroker);
		this.expression = expression;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		nameField = form.getFieldByControlId(FIELD_NAME, CustomField.class);

		value = new SelectedPrdModel();
		
		if (!StringUtil.isEmpty(expression)) {
			value.setName(expression);
		}
		
		Form subForm = this.getForm().getSubFormById(FORM_NAME);
		subForm.setObject(value);
		subForm.loadFromObject();
	}
	
	protected void buttonPressed(int buttonId) {
        if (buttonId == IDialogConstants.OK_ID) {
        	Form form = this.getForm().getSubFormById(FORM_NAME);
        	form.saveToObject();
        	
        	value = (SelectedPrdModel) form.getObject();
        	if (StringUtil.isEmpty(value.getName())) {
        		UI.showInfo(Message.getString("prd.designer_condition_transition_expression_null"));
        		return;
        	}
        } else {
            value = null;
        }
        super.buttonPressed(buttonId);
    }

	public SelectedPrdModel getValue() {
		return value;
	}

	public void setValue(SelectedPrdModel value) {
		this.value = value;
	}
	
}