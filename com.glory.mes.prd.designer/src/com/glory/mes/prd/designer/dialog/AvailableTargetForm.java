package com.glory.mes.prd.designer.dialog;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;


public class AvailableTargetForm extends AbstractPrdDialogForm {
	
	private static final String TABLE_NAME = "PRDDesignerMoveTarget";
	
	public AvailableTargetForm(Composite parent, int style, Object object) {
		super(parent, style, object);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			this.table = table;
			createForm();
		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}

	@Override
	String getDialogTitle() {
		return Message.getString("prd.designer_move_to_dialog_title");
	}

	@Override
	String getDialogMessage() {
		return Message.getString("prd.designer_move_to_dialog_message");
	}

	
}

