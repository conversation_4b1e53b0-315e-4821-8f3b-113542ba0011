package com.glory.mes.prd.designer.dialog;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.SquareButton;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.swt.UIControlsFactory;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.designer.common.notation.Edge;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.NodeElement;
import com.glory.mes.prd.designer.model.ReworkTransition;
import com.glory.mes.prd.designer.model.Transition;

public class EdgePropertyEditDialog extends BaseTitleDialog {
	
	private SelectedPrdModel value;
    private EntityForm form;
    
    private Node transitionSource;
    private Object model;
    private Transition transition;
    
    protected ManagedForm managedForm;
	
	public EdgePropertyEditDialog(Shell parent, Edge model) {
		super(parent);
		this.setModel(model);
		this.setTransitionSource(model.getSource());
		this.transition = (Transition) model.getSemanticElement();
		value = new SelectedPrdModel();
		value.setName(transition.getName());
		value.setDescription(transition.getDescription().getDescription());
	}
	
	protected void createFormContent(Composite composite) {
		FormToolkit toolkit = new FormToolkit(getShell().getDisplay());
		ScrolledForm sForm = toolkit.createScrolledForm(composite);
		managedForm = new ManagedForm(toolkit, sForm);
		sForm.setLayoutData(new GridData(GridData.FILL_BOTH));
		Composite body = sForm.getForm().getBody();
		configureBody(body);
		form = new AvailableTransitionForm(body, SWT.NONE, value);
		form.setLayoutData(new GridData(GridData.FILL_BOTH));	

	}
	
	protected void buttonPressed(int buttonId) {
        if (buttonId == IDialogConstants.OK_ID) {
            managedForm.getMessageManager().removeAllMessages();
			if (form != null) {
				boolean saveFlag = true;
				if (!form.saveToObject()) {
					saveFlag = false;
				}
				if (saveFlag) {
					PropertyUtil.copyProperties(value, form.getObject(), form.getCopyProperties());
				}
			}
			
            if (value == null) {
            	return;
            }
            
            if(StringUtil.isEmpty(value.getName())){
            	UI.showWarning(Message.getString("prd.transition_name_null"));
            	return;
            }
            NodeElement element = (AbstractNode)transitionSource.getSemanticElement();
            for(Transition tran : element.getTransitions()){
            	if(tran instanceof ReworkTransition){
            		if(value.getName().equals(tran.getName())){
            			if(!tran.equals(transition)){
            				UI.showWarning(String.format(Message.getString("prd.transition_name_already_exists"), value.getName(), element.getName()));
            				return;
            			}
            		}
            	}
            }
			transition.setName(value.getName());
			transition.getDescription().setDescription(value.getDescription());
        } else {
            value = null;
        }
        super.buttonPressed(buttonId);
    }

	public Object getModel() {
		return model;
	}

	public void setModel(Object model) {
		this.model = model;
	}

	public Node getTransitionSource() {
		return transitionSource;
	}

	public void setTransitionSource(Node transitionSource) {
		this.transitionSource = transitionSource;
	}

	@Override
	protected Control buildView(Composite parent) {
        setTitleImage(SWTResourceCache.getImage("entity-dialog"));
        getShell().setText(Message.getString("prd.transition_creating"));
        setTitle(Message.getString("prd.transition_property_setting"));
        createFormContent(parent);
        return parent;
	}
}
