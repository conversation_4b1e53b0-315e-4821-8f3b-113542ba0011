package com.glory.mes.prd.designer.dialog;

import org.eclipse.swt.widgets.Composite;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class AvailableTransitionForm extends EntityForm {
	
	private static final String TABLE_NAME = "PRDDesignerReworkTransition";

	public AvailableTransitionForm(Composite parent, int style, Object object) {
		super(parent, style, object, null);
		try {
			ADManager adManager = Framework.getService(ADManager.class);
			ADTable table = adManager.getADTable(Env.getOrgRrn(), TABLE_NAME);
			this.table = table;
			createForm();
		}  catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return;
		}
	}
}
