package com.glory.mes.prd.designer.dialog.procedurestep;

import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.base.ui.dialog.BaseTitleDialog;
import com.glory.framework.base.ui.util.UI;

public class ProcedureStepDialog extends BaseTitleDialog {

	ProcedureStepForm form;
	
	private List<ProcedureStepModel> procedureStepModels;
	
    public ProcedureStepDialog(Shell parentShell, List<ProcedureStepModel> procedureStepModels) {
        super(parentShell);
        this.procedureStepModels = procedureStepModels;
    }
    
    @Override
	protected Control buildView(Composite parent) {
	    createTableContent(parent);
		return parent;
	}
    
    protected Control createTableContent(Composite parent) {
		FormToolkit toolkit = new FormToolkit(UI.getActiveShell().getDisplay());
		
	    Composite content = toolkit.createComposite(parent);
	    content.setLayoutData(new GridData(GridData.FILL_BOTH));
	    content.setLayout(new GridLayout(1, false));
	    
	    createFrom(content, toolkit);    
	    return parent;
	}

    public void createFrom(Composite composite, FormToolkit toolkit) {
        form = new ProcedureStepForm(composite, SWT.FILL, procedureStepModels);
        form.setBackground(new Color(null, 255, 255, 255));
    }

    @Override
    public Point getMinSize() {
        return new Point(600, 400);
    }
}
