package com.glory.mes.prd.designer.dialog;

import java.util.Locale;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;

public class AvailableRedirectEndReworkProceduresDialog extends EntityDialog {
		
	public AvailableRedirectEndReworkProceduresDialog(ADTable table, ADBase adObject) {
		super(table, adObject);
	}
	
	@Override
	protected Control buildView(Composite parent) {
        super.buildView(parent);
		String message = "";
		String title = "";
		if (this.table != null) {
			if ("en".equals(Locale.getDefault().getLanguage())) {
				if (StringUtils.isNotEmpty(this.table.getLabel())) {
					title = this.table.getLabel().split("#")[0];
					if (this.table.getLabel().split("#").length > 1) {
						message = this.table.getLabel().split("#")[1];
					}
				}
			} else if ("zh".equals(Locale.getDefault().getLanguage())) {
				if (StringUtils.isNotEmpty(this.table.getLabel_zh())) {
					title = this.table.getLabel_zh().split("#")[0];
					if (this.table.getLabel_zh().split("#").length > 1) {
						message = this.table.getLabel_zh().split("#")[1];
					}
				}

			}
		}
		setTitleImage(SWTResourceCache.getImage("entity-dialog"));
		setTitle(title);
        setMessage(message);
        return parent;
	}
	
	protected boolean saveAdapter() {
		try {
			managedForm.getMessageManager().removeAllMessages();
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (Form detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
					}
					setAdObject(getAdObject());
					return true;
				}
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
			return false;
		}
		return false;
	}
}
