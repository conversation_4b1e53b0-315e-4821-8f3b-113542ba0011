package com.glory.mes.prd.designer.dialog;

import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.widgets.Composite;

import com.glory.framework.base.entitymanager.forms.EntityForm;

public abstract class AbstractPrdDialogForm extends EntityForm {

	public AbstractPrdDialogForm(Composite parent, int style, Object object) {
		super(parent, style, object, null);
	}
	
	public Point getDialogPoint() {
		int height = DPIUtil.autoScaleUpUsingNativeDPI(200);
		int width = DPIUtil.autoScaleUpUsingNativeDPI(400);
		return new Point(width, height);
	}
	
	abstract String getDialogTitle();
	abstract String getDialogMessage();
	
}
