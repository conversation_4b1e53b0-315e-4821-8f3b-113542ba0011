package com.glory.mes.prd.designer.policy;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.gef.Request;
import org.eclipse.gef.commands.Command;
import org.eclipse.gef.requests.CreateConnectionRequest;
import org.eclipse.gef.requests.ReconnectRequest;

import com.glory.framework.core.util.StringUtil;
import com.glory.mes.prd.designer.command.ConditionTransitionEdgeCreateCommand;
import com.glory.mes.prd.designer.command.EdgeCreateCommand;
import com.glory.mes.prd.designer.command.EdgeMoveCommand;
import com.glory.mes.prd.designer.command.ReworkTransitionEdgeCreateCommand;
import com.glory.mes.prd.designer.common.command.AbstractEdgeCreateCommand;
import com.glory.mes.prd.designer.common.command.AbstractEdgeMoveCommand;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.notation.Node;
import com.glory.mes.prd.designer.common.notation.NotationElement;
import com.glory.mes.prd.designer.common.policy.GraphicalNodeEditPolicy;
import com.glory.mes.prd.designer.model.AbstractNode;
import com.glory.mes.prd.designer.model.ConditionTransition;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.NodeElement;
import com.glory.mes.prd.designer.model.ProcessDefinition;
import com.glory.mes.prd.designer.model.RedirectEndState;
import com.glory.mes.prd.designer.model.ReworkState;
import com.glory.mes.prd.designer.model.ReworkTransition;
import com.glory.mes.prd.designer.model.StartState;
import com.glory.mes.prd.designer.model.StepState;
import com.glory.mes.prd.designer.model.Transition;
import com.glory.mes.prd.designer.notation.JpdlEdge;
import com.glory.mes.prd.designer.notation.JpdlNode;

public class NodeGraphicalNodeEditPolicy extends GraphicalNodeEditPolicy {
	
	IEventBroker eventBroker;
	
	public NodeGraphicalNodeEditPolicy(IEventBroker eventBroker) {
		super();
		this.eventBroker = eventBroker;
	}

	protected boolean canStart(Request request) {
		SemanticElement semanticElement = ((NotationElement)getNode()).getSemanticElement();
		if (semanticElement instanceof EndState || semanticElement instanceof RedirectEndState) {
			return false;
		}
		
		CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
		Object transitionObject = ((JpdlEdge)ccRequest.getNewObject()).getSemanticElement();
		Transition[] transitions = ((AbstractNode)semanticElement).getTransitions();
		List<Transition> normalTransitions = new ArrayList<Transition>();
		List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
		List<ConditionTransition> branchTransitions = new ArrayList<ConditionTransition>();
		for (Transition tran : transitions) {
			if (tran instanceof ReworkTransition) {
				reworkTransitions.add((ReworkTransition) tran);
			} else if (tran instanceof ConditionTransition) {
				branchTransitions.add((ConditionTransition) tran);
			} else {
				normalTransitions.add(tran);
			}
		}
		
		if (semanticElement instanceof AbstractNode) {
			if (transitionObject instanceof ReworkTransition) {
				//�����������reworkTransition
				if (semanticElement instanceof StepState 
						|| semanticElement instanceof ReworkState
						|| semanticElement instanceof IfState
						|| semanticElement instanceof ElseState) {
					// ����stepState��reworkState���Դ���reworkTransition���ᣬ�������͵�node��������
					//��stepState���Է������reworkTransition��reworkStateֻ����һ��
					if (semanticElement instanceof StepState
							|| semanticElement instanceof IfState
							|| semanticElement instanceof ElseState) {
						return true;
					} else {
						if (reworkTransitions.size() > 0) {
							return false;
						} else if (branchTransitions.size() > 0) {
							return false;
						} else {
							return true;
						}
					}
				} else {
					//���ڷ�stepState��reworkState��IfState��ElseState��������reworkTransition����
					return false;
				}
			} else if (transitionObject instanceof ConditionTransition) {
				
				//�����������BranchTransition
				if (semanticElement instanceof StepState || semanticElement instanceof ReworkState) {
					return true;
				} else {
					//���ڷ�stepState��������BranchTransition����
					return false;
				}
			} else {
				//�����������Transition,���Ӹ�node������transition�Ƿ��Ѿ�����
				if (normalTransitions.size() >= 1) {
					//����ýڵ��Ѿ���Ϊsource���ڹ��ˣ����Բ�������Ϊsource��
					return false;	
				} else {
					return true;
				}
			}
		} else {
			return false;
		}
	}
	
	/*
	 * ֧�������¼���һ���ǰ��ѽ��õ������϶���һ���������½�����
	 * @see com.glory.mes.prd.designer.common.policy.GraphicalNodeEditPolicy#canStop(org.eclipse.gef.Request)
	 */
	protected boolean canStop(Request request) {
		SemanticElement semanticElement = ((NotationElement)getNode()).getSemanticElement();
		NodeElement[] nodes = ((ProcessDefinition)((JpdlNode)getNode()).getContainer().getSemanticElement()).getNodeElements();
				
		Object transitionObject = null;
		if (request instanceof CreateConnectionRequest) {
			CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
			transitionObject = ((JpdlEdge)ccRequest.getNewObject()).getSemanticElement();
		} else if (request instanceof ReconnectRequest) {
			ReconnectRequest rcRequest = (ReconnectRequest) request;			
			transitionObject = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSemanticElement();
		}		
		
		if (semanticElement instanceof StartState) {
			return false;
		} else if (semanticElement instanceof AbstractNode) {
			AbstractNode targetNode = (AbstractNode)semanticElement;//�����target
			if (transitionObject instanceof ReworkTransition) {
				//�����������reworkTransition
				Node source = null;
				if (request instanceof CreateConnectionRequest) {
					CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
					source = (Node)((ReworkTransitionEdgeCreateCommand)ccRequest.getStartCommand()).getSource();
				} else if (request instanceof ReconnectRequest) {
					ReconnectRequest rcRequest = (ReconnectRequest) request;			
					source = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSource();
				}								
				SemanticElement sourceNode = source.getSemanticElement();//�����source
				if (sourceNode instanceof StepState) {
					if (targetNode instanceof ReworkState) {
						//��ΪstepState���������reworkTransition
						//��Ҫ�ж�һ���ɸ�stepState���������ᵽtarget��reworkTransition�Ƿ��Ѿ�����
						//��������������������
						boolean targetIsReworkState = (targetNode instanceof ReworkState);
						boolean transitionAlreadyExists = false;
						
						Transition[] transitions = ((StepState) sourceNode).getTransitions();
						List<Transition> normalTransitions = new ArrayList<Transition>();
						List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
						for (Transition tran : transitions) {
							if (tran instanceof ReworkTransition) {
								reworkTransitions.add((ReworkTransition) tran);
							} else {
								normalTransitions.add(tran);
							}
						}
						for (ReworkTransition rt : reworkTransitions) {
							String target = rt.getTo();
							if (!StringUtil.isEmpty(target) && target.equals(targetNode.getName())) {
								transitionAlreadyExists = true;
								break;
							}
						}
						return targetIsReworkState && !transitionAlreadyExists;
					} else {
						return false;
					}
				} 
//				else if (sourceNode instanceof IfState
//						|| sourceNode instanceof ElseState) {
//					if (targetNode instanceof ReworkState) {
//						//ֻ������һ��reworkTransitiond
//						Transition[] transitions = ((AbstractNode) sourceNode).getTransitions();
//						List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
//						for (Transition tran : transitions) {
//							if (tran instanceof ReworkTransition) {
//								reworkTransitions.add((ReworkTransition) tran);
//							} 
//						}
//						if (reworkTransitions.size() > 0) {
//							return false;
//						} else {
//							return true;
//						}
//					}
//				}
				else if (sourceNode instanceof ReworkState) {
//					if (targetNode instanceof StepState 
//							|| targetNode instanceof IfState 
//							|| targetNode instanceof ElseState 
//							|| targetNode instanceof EndIfState 
//							|| targetNode instanceof EndState) {
//						//ֻ������һ��reworkTransitiond
//						Transition[] transitions = ((ReworkState) sourceNode).getTransitions();
//						List<ReworkTransition> reworkTransitions = new ArrayList<ReworkTransition>();
//						for (Transition tran : transitions) {
//							if (tran instanceof ReworkTransition) {
//								reworkTransitions.add((ReworkTransition) tran);
//							} 
//						}
//						if (reworkTransitions.size() > 0) {
//							return false;
//						} else {
//							return true;
//						}
//					}
					return false;
				} 
				return true;
			} else if (transitionObject instanceof ConditionTransition) {
				//�����������reworkTransition
				Node source = null;
				if (request instanceof CreateConnectionRequest) {
					CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
					source = (Node)((ConditionTransitionEdgeCreateCommand)ccRequest.getStartCommand()).getSource();
				} else if (request instanceof ReconnectRequest) {
					ReconnectRequest rcRequest = (ReconnectRequest) request;			
					source = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSource();
				}								
				SemanticElement sourceNode = source.getSemanticElement();//�����source
				
				if (targetNode instanceof ReworkState 
						|| targetNode instanceof StepState
						|| targetNode instanceof EndState
						|| targetNode instanceof RedirectEndState) {
					// ��ΪstepState���������contitionTransition
					// ��Ҫ�ж�һ���ɸ�stepState���������ᵽtarget��contitionTransition�Ƿ��Ѿ�����
					// ��������������������
					boolean transitionAlreadyExists = false;
					
					Transition[] transitions = ((AbstractNode) sourceNode).getTransitions();
					List<Transition> exsitsTransitions = new ArrayList<Transition>();
					for (Transition tran : transitions) {
						if (tran instanceof ConditionTransition) {
							exsitsTransitions.add(tran);
						} else if (tran instanceof ReworkTransition) {
							// Rework���ڴ˴���
						} else {
							exsitsTransitions.add(tran);
						}
					}
					for (Transition rt : exsitsTransitions) {
						String target = rt.getTo();
						if (!StringUtil.isEmpty(target) && target.equals(targetNode.getName())) {
							transitionAlreadyExists = true;
							break;
						}
					}
					
					return !transitionAlreadyExists;
				}
				
				return false;
			} else {
				if (targetNode instanceof EndState || targetNode instanceof RedirectEndState) {
					return true;
				}
				
				if (targetNode instanceof ReworkState) {
					//reworkStateֻ����reworkTransition����
					return false;
				}
				
				Node source = null;
				if (request instanceof CreateConnectionRequest) {
					CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
					source = (Node)((EdgeCreateCommand)ccRequest.getStartCommand()).getSource();
				} else if (request instanceof ReconnectRequest) {
					ReconnectRequest rcRequest = (ReconnectRequest) request;			
					source = ((JpdlEdge)rcRequest.getConnectionEditPart().getModel()).getSource();
				}								
				SemanticElement sourceNode = source.getSemanticElement();//�����source
				if (sourceNode instanceof ReworkState && targetNode instanceof StepState) {
					return true;
				}
				
				for (NodeElement node : nodes) {
					if (node instanceof AbstractNode) {
						Transition[] transitions = ((AbstractNode)node).getTransitions();
						List<Transition> normalTransitions = new ArrayList<Transition>();
						for (Transition tran : transitions) {
							if (!(tran instanceof ReworkTransition)) {
								normalTransitions.add(tran);
							}
						}
						if (normalTransitions.size() >= 1) {
							if (targetNode.getName().equals(normalTransitions.get(0).getTo())) {
								//�����һ���ڵ��target������ڵ�,��ô������������Ϊtarget
								return false;
							}
						}
					}
				}
				return true;
			}
		} else {
			return false;
		}
	}
	
	protected AbstractEdgeCreateCommand createEdgeCreateCommand(Request request) {
		CreateConnectionRequest ccRequest = (CreateConnectionRequest) request;
		Object createObject = ((JpdlEdge)ccRequest.getNewObject()).getSemanticElement();
		if (createObject instanceof ReworkTransition) {
			return new ReworkTransitionEdgeCreateCommand();
		} else if (createObject instanceof ConditionTransition) {
			return new ConditionTransitionEdgeCreateCommand(eventBroker);
		} else {
			return new EdgeCreateCommand();
		}
	}

	protected AbstractEdgeMoveCommand createEdgeMoveCommand() {
		return new EdgeMoveCommand();
	}
	
	@Override
	public Command getCommand(Request request) {
		//copy code from supper
		if (REQ_CONNECTION_START.equals(request.getType())) {
			return getConnectionCreateCommand((CreateConnectionRequest) request);
		} else if (REQ_CONNECTION_END.equals(request.getType())) {
			return getConnectionCompleteCommand((CreateConnectionRequest) request);
		} else if (REQ_RECONNECT_TARGET.equals(request.getType())) {
			return getReconnectTargetCommand((ReconnectRequest) request);
		} else if (REQ_RECONNECT_SOURCE.equals(request.getType())) {
			return getReconnectSourceCommand((ReconnectRequest) request);
		}
		return null;
	}
}
