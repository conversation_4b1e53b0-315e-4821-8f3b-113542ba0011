package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;
import java.util.HashMap;
import java.util.Map;

import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.model.MoveToState;
import com.glory.mes.prd.designer.model.Transition;

public class MoveToStateDomAdapter extends XmlAdapter {

	private static HashMap NODE_TYPES = null;	
	private static String[] CHILD_ELEMENTS = {"transition", "rework-transition"};
	
	protected String[] getChildElements() {
		return CHILD_ELEMENTS;
	}
	
	public Map getNodeTypes() {
		if (NODE_TYPES == null) {
			NODE_TYPES = new HashMap();
			NODE_TYPES.put("transition", "transition");
			NODE_TYPES.put("rework-transition", "rework-transition");
		}
		return NODE_TYPES;
	}
	
	protected void initialize() {
		super.initialize();
		MoveToState moveToState = (MoveToState)getSemanticElement();
		setAttribute("name", moveToState.getName());
		setAttribute("target", moveToState.getTarget());
		setAttribute("isdynamic", moveToState.getIsDynamic());
		addElements(moveToState.getTransitions());
	}

	public void initialize(SemanticElement semanticElement) {
		super.initialize(semanticElement);
		MoveToState moveToLocState = (MoveToState)semanticElement;
		moveToLocState.setName(getAttribute("name"));
		moveToLocState.setTarget(getAttribute("target"));
		moveToLocState.setIsDynamic(getAttribute("isdynamic"));
		moveToLocState.addPropertyChangeListener(this);
	}
	
	@Override
	protected void doModelAdd(XmlAdapter child) {
		String type = child.getElementType();
		SemanticElement jpdlElement;
		jpdlElement = createSemanticElementFor(child);
		child.initialize(jpdlElement);
		MoveToState moveToLocState = (MoveToState)getSemanticElement();
		if ("transition".equals(type) || "rework-transition".equals(type)) {
			moveToLocState.addTransition((Transition)jpdlElement);
		}
	}

	@Override
	protected void doModelRemove(XmlAdapter child) {
		String type = child.getElementType();
		MoveToState moveToLocState = (MoveToState)getSemanticElement();
		if ("transition".equals(type) || "rework-transition".equals(type)) {
			moveToLocState.removeTransition((Transition)child.getSemanticElement());
		} 
	}

	@Override
	protected void doModelUpdate(String name, String newValue) {
//		MoveToState moveToLocState = (MoveToState)getSemanticElement();
//		if ("target".equals(name)) {
//			moveToLocState.setTarget(newValue);
//		}  else if ("isdynamic".equals(name)) {
//			moveToLocState.setIsDynamic(newValue);
//		}
	}
	
	@Override
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("transitionAdd".equals(evt.getPropertyName())) {
			addElement((Transition)evt.getNewValue());
		} else if ("transitionRemove".equals(evt.getPropertyName())) {
			removeElement((Transition)evt.getOldValue());
		} else if ("name".equals(evt.getPropertyName())) {
			setAttribute("name", (String)evt.getNewValue());
		} else if ("target".equals(evt.getPropertyName())) {
			setAttribute("target", (String)evt.getNewValue());
		} else if ("isdynamic".equals(evt.getPropertyName())) {
			setAttribute("isdynamic", (String)evt.getNewValue());
		}
	}

}
