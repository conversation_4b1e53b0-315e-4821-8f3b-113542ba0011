package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;
import java.util.HashMap;
import java.util.Map;

import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.IfState;
import com.glory.mes.prd.designer.model.Transition;

public class IfStateDomAdapter extends XmlAdapter {
	private static HashMap NODE_TYPES = null;
	private static String[] CHILD_ELEMENTS = {"if-state", "transition", "rework-transition"};
	
	protected String[] getChildElements() {
		return CHILD_ELEMENTS;
	}
	
	protected Map getNodeTypes() {
		if (NODE_TYPES == null) {
			NODE_TYPES = new HashMap();
			NODE_TYPES.put("if-state", "if-state");
			NODE_TYPES.put("transition", "transition");
			NODE_TYPES.put("rework-transition", "rework-transition");
		}
		return NODE_TYPES;
	}
	
	@Override
	protected void initialize() {
		super.initialize();
		IfState ifState = (IfState)getSemanticElement();
		if (ifState != null) {
			setAttribute("name", ifState.getName());
			setAttribute("objecttype", ifState.getObjectType());
			setAttribute("parameter", ifState.getIfParameter());
			setAttribute("comparison", ifState.getIfParameterComparison());
			setAttribute("parametervalue", ifState.getIfParameterValue());
			setAttribute("ifExpression", ifState.getIfExpression());
			setAttribute("elseName", ifState.getElseState().getName());
			setAttribute("endIfName", ifState.getEndIfState().getName());
			addElements(ifState.getTransitions());
		}
	}

	@Override
	public void initialize(SemanticElement semanticElement) {
		super.initialize(semanticElement);
		IfState ifState = (IfState)semanticElement;
		ifState.setName(getAttribute("name"));
		ifState.setObjectType(getAttribute("objecttype"));
		ifState.setIfParameter(getAttribute("parameter"));
		ifState.setIfParameterComparison(getAttribute("comparison"));
		ifState.setIfParameterValue(getAttribute("parametervalue"));
		ifState.setIfExpression(getAttribute("ifExpression"));
		String elseName = getAttribute("elseName");
		String endIfName = getAttribute("endIfName");
		EndIfState endIfState = new EndIfState();
		endIfState.setName(endIfName);
		ElseState elseState = new ElseState();
		elseState.setName(elseName);
		elseState.setEndIfState(endIfState);
		ifState.setElseState(elseState);
		ifState.addPropertyChangeListener(this);
	}
	
	@Override
	protected void doModelAdd(XmlAdapter child) {
		SemanticElement jpdlElement;
		jpdlElement = createSemanticElementFor(child);
		child.initialize(jpdlElement);
		IfState ifState = (IfState)getSemanticElement();
		ifState.addTransition((Transition)jpdlElement);
	}

	@Override
	protected void doModelRemove(XmlAdapter child) {
		// TODO Auto-generated method stub

	}

	@Override
	protected void doModelUpdate(String name, String newValue) {
	
	}

	@Override
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("transitionAdd".equals(evt.getPropertyName())) {
			addElement((Transition)evt.getNewValue());
		} else if ("transitionRemove".equals(evt.getPropertyName())) {
			removeElement((Transition)evt.getOldValue());
		} else if ("name".equals(evt.getPropertyName())) {
			setAttribute("name", (String)evt.getNewValue());
		} else if ("objecttype".equals(evt.getPropertyName())) {
			setAttribute("objecttype", (String)evt.getNewValue());
		} else if ("parameter".equals(evt.getPropertyName())) {
			setAttribute("parameter", (String)evt.getNewValue());
		} else if ("comparison".equals(evt.getPropertyName())) {
			setAttribute("comparison", (String)evt.getNewValue());
		} else if ("parametervalue".equals(evt.getPropertyName())) {
			setAttribute("parametervalue", (String)evt.getNewValue());
		}  else if ("ifExpression".equals(evt.getPropertyName())) {
			setAttribute("ifExpression", (String)evt.getNewValue());
		}
	}

}
