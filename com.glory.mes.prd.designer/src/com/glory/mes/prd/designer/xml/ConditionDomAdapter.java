package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;

import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.model.Condition;

public class ConditionDomAdapter extends XmlAdapter {
	
	protected void initialize() {
		super.initialize();
		Condition condition = (Condition)getSemanticElement();
		if (condition != null) {
			setTextContent(condition.getScript());
			setAttribute("script", condition.getScript());
			setAttribute("expression", condition.getExpression());
		}
	}
	
	public void initialize(SemanticElement jpdlElement) {
		super.initialize(jpdlElement);
		Condition condition = (Condition)jpdlElement;
		condition.setScript(getAttribute("script"));
		condition.setExpression(getAttribute("expression"));
		condition.addPropertyChangeListener(this);
	}
	
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("script".equals(evt.getPropertyName())) {
			setAttribute("script", (String)evt.getNewValue());
		} else if ("expression".equals(evt.getPropertyName())) {
			setAttribute("expression", (String)evt.getNewValue());
		}
	}
	
	protected void doModelUpdate(String name, String newValue) {
		Condition condition = (Condition)getSemanticElement();
		if ("script".equals(name)) {
			condition.setScript(newValue);
		} else if ("expression".equals(name)) {
			condition.setExpression(newValue);
		}
	}
	
	protected void doModelAdd(XmlAdapter child) {
		// a script cannot have any child nodes
	}
	
	protected void doModelRemove(XmlAdapter child) {
		// a script cannot have any child nodes
	}
	
}
