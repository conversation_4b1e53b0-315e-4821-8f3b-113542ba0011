package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;
import java.util.HashMap;
import java.util.Map;

import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.Transition;

public class ElseStateDomAdapter extends XmlAdapter {
	private static HashMap NODE_TYPES = null;
	private static String[] CHILD_ELEMENTS = {"else-state", "transition", "rework-transition"};
	
	protected String[] getChildElements() {
		return CHILD_ELEMENTS;
	}
	
	protected Map getNodeTypes() {
		if (NODE_TYPES == null) {
			NODE_TYPES = new HashMap();
			NODE_TYPES.put("else-state", "else-state");
			NODE_TYPES.put("transition", "transition");
			NODE_TYPES.put("rework-transition", "rework-transition");
		}
		return NODE_TYPES;
	}
	
	@Override
	protected void initialize() {
		super.initialize();
		ElseState elseState = (ElseState)getSemanticElement();
		if (elseState != null) {
			setAttribute("name", elseState.getName());
			setAttribute("endIfName", elseState.getEndIfState().getName());
			addElements(elseState.getTransitions());
		}
	}

	@Override
	public void initialize(SemanticElement semanticElement) {
		super.initialize(semanticElement);
		ElseState elseState = (ElseState)semanticElement;
		elseState.setName(getAttribute("name"));
		String endIfName = getAttribute("endIfName");
		EndIfState endIfState = new EndIfState();
		endIfState.setName(endIfName);
		elseState.setEndIfState(endIfState);
		elseState.addPropertyChangeListener(this);
	}
	
	@Override
	protected void doModelAdd(XmlAdapter child) {
		SemanticElement jpdlElement;
		jpdlElement = createSemanticElementFor(child);
		child.initialize(jpdlElement);
		ElseState elseState = (ElseState)getSemanticElement();
		elseState.addTransition((Transition)jpdlElement);
	}

	@Override
	protected void doModelRemove(XmlAdapter child) {
		// TODO Auto-generated method stub

	}

	@Override
	protected void doModelUpdate(String name, String newValue) {
		// TODO Auto-generated method stub

	}

	@Override
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("transitionAdd".equals(evt.getPropertyName())) {
			addElement((Transition)evt.getNewValue());
		} else if ("transitionRemove".equals(evt.getPropertyName())) {
			removeElement((Transition)evt.getOldValue());
		} else if ("name".equals(evt.getPropertyName())) {
			setAttribute("name", (String)evt.getNewValue());
		}
	}

}
