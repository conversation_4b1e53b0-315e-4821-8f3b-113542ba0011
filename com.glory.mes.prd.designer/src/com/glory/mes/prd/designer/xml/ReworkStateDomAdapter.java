package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;
import java.util.HashMap;
import java.util.Map;

import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.model.ConditionTransition;
import com.glory.mes.prd.designer.model.Description;
import com.glory.mes.prd.designer.model.ReworkState;
import com.glory.mes.prd.designer.model.SubProcedure;
import com.glory.mes.prd.designer.model.Transition;

public class ReworkStateDomAdapter extends XmlAdapter {

	private static HashMap NODE_TYPES = null;	
	private static String[] CHILD_ELEMENTS = {"procedure", "transition", "rework-transition", ConditionTransition.ELEMENT_TYPE_CONDITIONTRANSITION};
	
	protected String[] getChildElements() {
		return CHILD_ELEMENTS;
	}
	
	public Map getNodeTypes() {
		if (NODE_TYPES == null) {
			NODE_TYPES = new HashMap();
			NODE_TYPES.put("procedure", "procedure");
			NODE_TYPES.put("transition", "transition");
			NODE_TYPES.put("rework-transition", "rework-transition");
			NODE_TYPES.put(ConditionTransition.ELEMENT_TYPE_CONDITIONTRANSITION, ConditionTransition.ELEMENT_TYPE_CONDITIONTRANSITION);
		}
		return NODE_TYPES;
	}
	
	protected void initialize() {
		super.initialize();
		ReworkState reworkState = (ReworkState)getSemanticElement();
		if (reworkState != null) {
			setAttribute("name", reworkState.getName());
			setAttribute("isdynamic", reworkState.getIsDynamic());
			setAttribute("objectType", reworkState.getObjectType());
			setAttribute("target", reworkState.getTarget());
			addElement(reworkState.getProcedure());
			addElements(reworkState.getTransitions());
		}
	}

	public void initialize(SemanticElement semanticElement) {
		super.initialize(semanticElement);
		ReworkState reworkState = (ReworkState)semanticElement;
		reworkState.setName(getAttribute("name"));
		reworkState.setIsDynamic(getAttribute("isdynamic"));
		reworkState.setObjectType(getAttribute("objectType"));
		reworkState.setTarget(getAttribute("target"));
		reworkState.addPropertyChangeListener(this);
	}
	
	@Override
	protected void doModelAdd(XmlAdapter child) {
		String type = child.getElementType();
		SemanticElement jpdlElement;
		if ("procedure".equals(type)) {
			jpdlElement = child.getSemanticElement();
			if (jpdlElement == null) {
				jpdlElement = createSemanticElementFor(child);
				child.initialize(jpdlElement);
			}
		} else {
			jpdlElement = createSemanticElementFor(child);
			child.initialize(jpdlElement);
		}
		ReworkState reworkState = (ReworkState)getSemanticElement();
		if ("transition".equals(type) || "rework-transition".equals(type) || "condition-transition".equals(type)) {
			reworkState.addTransition((Transition)jpdlElement);
		} else if ("procedure".equals(type)) {
			reworkState.setProcedure((SubProcedure)jpdlElement);
		}
	}

	@Override
	protected void doModelRemove(XmlAdapter child) {
		String type = child.getElementType();
		ReworkState stepState = (ReworkState)getSemanticElement();
		if ("transition".equals(type)) {
			stepState.removeTransition((Transition)child.getSemanticElement());
		} else if ("procedure".equals(type)) {
			stepState.setProcedure(null);
		}
	}

	@Override
	protected void doModelUpdate(String name, String newValue) {
//		ReworkState reworkState = (ReworkState)getSemanticElement();
//		if ("name".equals(name)) {
//			reworkState.setName(newValue);
//		} else if ("isdynamic".equals(name)) {
//			reworkState.setIsDynamic(newValue);
//		}
	}
	
	@Override
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("transitionAdd".equals(evt.getPropertyName())) {
			addElement((Transition)evt.getNewValue());
		} else if ("transitionRemove".equals(evt.getPropertyName())) {
			removeElement((Transition)evt.getOldValue());
		} else if ("procedure".equals(evt.getPropertyName())) {
			setElement("procedure", (SemanticElement)evt.getOldValue(), (Description)evt.getNewValue());
		} else if ("name".equals(evt.getPropertyName())) {
			setAttribute("name", (String)evt.getNewValue());
		} else if ("isdynamic".equals(evt.getPropertyName())) {
			setAttribute("isdynamic", (String)evt.getNewValue());
		} else if ("objectType".equals(evt.getPropertyName())) {
			setAttribute("objectType", (String)evt.getNewValue());
		} else if ("target".equals(evt.getPropertyName())) {
			setAttribute("target", (String)evt.getNewValue());
		}  else if ("hiSuperInstruction".equals(evt.getPropertyName())) {
			setAttribute("hiSuperInstruction", (String)evt.getNewValue());
		}
	}

}
