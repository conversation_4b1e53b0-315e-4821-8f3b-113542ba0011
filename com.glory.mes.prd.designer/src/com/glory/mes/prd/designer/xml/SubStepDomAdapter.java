package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.model.SubStep;;

public class SubStepDomAdapter extends XmlAdapter {
	
	protected void initialize() {
		super.initialize();
		SubStep subStep = (SubStep)getSemanticElement();
		if (subStep != null) {
			setAttribute("name", subStep.getName());
			setAttribute("version", subStep.getVersion());
			setAttribute("stageid", subStep.getStageId());
			setAttribute("reworkname", subStep.getReworkName());
			setAttribute("reworkversion", subStep.getReworkVersion());
			setAttribute("isback", subStep.getIsBack());
		}
	}
	
	public void initialize(SemanticElement jpdlElement) {
		super.initialize(jpdlElement);
		SubStep subStep = (SubStep)jpdlElement;
		subStep.setName(getAttribute("name"));
		subStep.setVersion(getAttribute("version"));
		subStep.setStageId(getAttribute("stageid"));
		subStep.setReworkName(getAttribute("reworkname"));
		subStep.setReworkVersion(getAttribute("reworkversion"));
		subStep.setIsBack(getAttribute("isback"));
		subStep.addPropertyChangeListener(this);
	}
	
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("step".equals(evt.getPropertyName())) {
			SubStep subStep = (SubStep)evt.getNewValue();
			setAttribute("name", subStep.getName());
			setAttribute("version", subStep.getVersion());
		} else if ("name".equals(evt.getPropertyName())) {
			setAttribute("name", (String)evt.getNewValue());
		} else if ("version".equals(evt.getPropertyName())) {
			setAttribute("version", (String)evt.getNewValue());
		} else if ("stageid".equals(evt.getPropertyName())) {
			setAttribute("stageid", (String)evt.getNewValue());
		} else if ("reworkname".equals(evt.getPropertyName())) {
			setAttribute("reworkname", (String)evt.getNewValue());
		} else if ("reworkversion".equals(evt.getPropertyName())) {
			setAttribute("reworkversion", (String)evt.getNewValue());
		} else if ("isback".equals(evt.getPropertyName())) {
			setAttribute("isback", (String)evt.getNewValue());
		}
	}
	
	protected void doModelUpdate(String name, String newValue) {
		SubStep subStep = (SubStep)getSemanticElement();
		if ("name".equals(name)) {
			subStep.setName(newValue);
		} else if ("version".equals(name)) {
			subStep.setVersion(newValue);
		} else if ("stageid".equals(name)) {
			subStep.setStageId(newValue);
		} else if ("reworkname".equals(name)) {
			subStep.setReworkName(newValue);
		} else if ("reworkversion".equals(name)) {
			subStep.setReworkVersion(newValue);
		} else if ("isback".equals(name)) {
			subStep.setIsBack(newValue);
		}
	}
	
	protected void doModelAdd(XmlAdapter child) {
		// a subprocess cannot have any child nodes
	}
	
	protected void doModelRemove(XmlAdapter child) {
		// a subprocess cannot have any child nodes
	}
}
