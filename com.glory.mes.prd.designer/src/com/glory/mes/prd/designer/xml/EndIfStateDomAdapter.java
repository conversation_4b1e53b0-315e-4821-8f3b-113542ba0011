package com.glory.mes.prd.designer.xml;

import java.beans.PropertyChangeEvent;
import java.util.HashMap;
import java.util.Map;

import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.xml.XmlAdapter;
import com.glory.mes.prd.designer.model.ElseState;
import com.glory.mes.prd.designer.model.EndIfState;
import com.glory.mes.prd.designer.model.Transition;

public class EndIfStateDomAdapter extends XmlAdapter {
	private static HashMap NODE_TYPES = null;
	private static String[] CHILD_ELEMENTS = {"transition"};
	
	protected String[] getChildElements() {
		return CHILD_ELEMENTS;
	}
	
	protected Map getNodeTypes() {
		if (NODE_TYPES == null) {
			NODE_TYPES = new HashMap();
			NODE_TYPES.put("endif-state", "endif-state");
			NODE_TYPES.put("transition", "transition");
		}
		return NODE_TYPES;
	}
	
	@Override
	protected void initialize() {
		super.initialize();
		EndIfState endIfState = (EndIfState)getSemanticElement();
		if (endIfState != null) {
			setAttribute("name", endIfState.getName());
			addElements(endIfState.getTransitions());
		}
	}

	@Override
	public void initialize(SemanticElement semanticElement) {
		super.initialize(semanticElement);
		EndIfState endIfState = (EndIfState)semanticElement;
		endIfState.setName(getAttribute("name"));
		endIfState.addPropertyChangeListener(this);
	}
	
	@Override
	protected void doModelAdd(XmlAdapter child) {
		SemanticElement jpdlElement;
		jpdlElement = createSemanticElementFor(child);
		child.initialize(jpdlElement);
		EndIfState endIfState = (EndIfState)getSemanticElement();
		endIfState.addTransition((Transition)jpdlElement);
	}

	@Override
	protected void doModelRemove(XmlAdapter child) {
		// TODO Auto-generated method stub

	}

	@Override
	protected void doModelUpdate(String name, String newValue) {
		// TODO Auto-generated method stub

	}

	@Override
	protected void doPropertyChange(PropertyChangeEvent evt) {
		if ("transitionAdd".equals(evt.getPropertyName())) {
			addElement((Transition)evt.getNewValue());
		} else if ("transitionRemove".equals(evt.getPropertyName())) {
			removeElement((Transition)evt.getOldValue());
		} else if ("name".equals(evt.getPropertyName())) {
			setAttribute("name", (String)evt.getNewValue());
		}
	}
}
