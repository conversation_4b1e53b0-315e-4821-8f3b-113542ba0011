Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: <PERSON><PERSON> <PERSON>inger Plug-in
Bundle-SymbolicName: com.glory.mes.prd.designer;singleton:=true
Bundle-Version: 8.4.0
Bundle-Activator: com.glory.mes.prd.designer.Plugin
Bundle-Vendor: GlorySoft
Require-Bundle: org.eclipse.ui;bundle-version="3.107.0",
 org.eclipse.core.runtime;bundle-version="3.11.0",
 org.eclipse.core.resources;bundle-version="3.10.0",
 org.eclipse.ui.forms;bundle-version="3.6.200",
 com.glory.mes.prd.designer.common,
 com.glory.framework.base;bundle-version="8.4.0",
 com.glory.framework.runtime;bundle-version="8.4.0",
 com.glory.framework.lib;bundle-version="8.4.0",
 org.eclipse.wst.sse.core;bundle-version="1.1.900",
 org.eclipse.gef;bundle-version="3.10.0",
 org.eclipse.e4.core.services;bundle-version="2.3.200",
 org.eclipse.osgi.services;bundle-version="3.10.200",
 org.apache.log4j,
 org.apache.commons.lang
Bundle-ActivationPolicy: lazy
Export-Package: 
 com.glory.mes.prd.designer,
 com.glory.mes.prd.designer.command,
 com.glory.mes.prd.designer.dialog,
 com.glory.mes.prd.designer.editor,
 com.glory.mes.prd.designer.model,
 com.glory.mes.prd.designer.notation,
 com.glory.mes.prd.designer.part,
 com.glory.mes.prd.designer.policy,
 com.glory.mes.prd.designer.xml
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
