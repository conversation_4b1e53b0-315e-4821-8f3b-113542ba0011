<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ADFORM>
    <ISACTIVE>true</ISACTIVE>
    <OBJECTRRN>335419567994122242</OBJECTRRN>
    <ORGRRN>0</ORGRRN>
    <CREATED>2023/05/15 13:57:36</CREATED>
    <CREATEDBY>admin</CREATEDBY>
    <UPDATED>2023/05/16 16:41:26</UPDATED>
    <UPDATEDBY>admin</UPDATEDBY>
    <ADTABLE>
        <ISACTIVE>true</ISACTIVE>
        <OBJECTRRN>335419567994122241</OBJECTRRN>
        <ORGRRN>0</ORGRRN>
        <CREATED>2023/05/15 13:57:36</CREATED>
        <CREATEDBY>admin</CREATEDBY>
        <UPDATED>2023/05/16 16:41:26</UPDATED>
        <UPDATEDBY>admin</UPDATEDBY>
        <ADFIELDS>
            <ADFIELD>
                <ISACTIVE>true</ISACTIVE>
                <OBJECTRRN>335420021935255552</OBJECTRRN>
                <ORGRRN>0</ORGRRN>
                <CREATED>2023/05/15 13:59:25</CREATED>
                <CREATEDBY>admin</CREATEDBY>
                <UPDATED>2023/05/15 14:02:54</UPDATED>
                <UPDATEDBY>admin</UPDATEDBY>
                <ADBUTTONS/>
                <CUSTOMCOMPOSITE>VariableExpressionCustomComposite</CUSTOMCOMPOSITE>
                <DATATYPE>string</DATATYPE>
                <DESCRIPTION>表达式</DESCRIPTION>
                <DISPLAYTYPE>custom</DISPLAYTYPE>
                <ISATTRIBUTE>false</ISATTRIBUTE>
                <ISBASIC>false</ISBASIC>
                <ISDESIGNER>true</ISDESIGNER>
                <ISDISPLAY>true</ISDISPLAY>
                <ISEDITABLE>true</ISEDITABLE>
                <ISFROMPARENT>false</ISFROMPARENT>
                <ISMAIN>false</ISMAIN>
                <ISMANDATORY>false</ISMANDATORY>
                <ISPERSIST>true</ISPERSIST>
                <ISQUERY>false</ISQUERY>
                <ISQUERYADVANCE>false</ISQUERYADVANCE>
                <ISQUERYMANDATORY>false</ISQUERYMANDATORY>
                <ISREADONLY>false</ISREADONLY>
                <ISSAMELINE>false</ISSAMELINE>
                <ISUPPER>false</ISUPPER>
                <LABLE>Expression</LABLE>
                <LABLEZH>表达式</LABLEZH>
                <NAME>name</NAME>
                <SEQNO>10</SEQNO>
                <STYLE>0</STYLE>
                <TABRRN>335420836041273344</TABRRN>
                <TABLERRN>335419567994122241</TABLERRN>
                <VALIDATELEVEL>3</VALIDATELEVEL>
            </ADFIELD>
        </ADFIELDS>
        <ISDESIGNER>true</ISDESIGNER>
        <ISVERTICAL>false</ISVERTICAL>
        <ISVIEW>false</ISVIEW>
        <LABEL>Condition expression info#Please enter an expression for the condition</LABEL>
        <LABELZH>条件信息#请输入条件表达式</LABELZH>
        <MODELCLASS>com.glory.mes.prd.workflow.graph.def.TransitionCondition</MODELCLASS>
        <MODELNAME>TransitionCondition</MODELNAME>
        <NAME>ConditionTransitionDialog_Table01</NAME>
        <STYLE>0</STYLE>
        <ADTABS>
            <ADTAB>
                <ISACTIVE>true</ISACTIVE>
                <OBJECTRRN>335420836041273344</OBJECTRRN>
                <ORGRRN>0</ORGRRN>
                <CREATED>2023/05/15 14:02:39</CREATED>
                <CREATEDBY>admin</CREATEDBY>
                <UPDATED>2023/05/15 14:02:39</UPDATED>
                <UPDATEDBY>admin</UPDATEDBY>
                <ADBUTTONS/>
                <DESCRIPTION>表单</DESCRIPTION>
                <ADFIELDS>
                    <ADFIELD>
                        <ISACTIVE>true</ISACTIVE>
                        <OBJECTRRN>335420021935255552</OBJECTRRN>
                        <ORGRRN>0</ORGRRN>
                        <CREATED>2023/05/15 13:59:25</CREATED>
                        <CREATEDBY>admin</CREATEDBY>
                        <UPDATED>2023/05/15 14:02:54</UPDATED>
                        <UPDATEDBY>admin</UPDATEDBY>
                        <ADBUTTONS/>
                        <CUSTOMCOMPOSITE>VariableExpressionCustomComposite</CUSTOMCOMPOSITE>
                        <DATATYPE>string</DATATYPE>
                        <DESCRIPTION>表达式</DESCRIPTION>
                        <DISPLAYTYPE>custom</DISPLAYTYPE>
                        <ISATTRIBUTE>false</ISATTRIBUTE>
                        <ISBASIC>false</ISBASIC>
                        <ISDESIGNER>true</ISDESIGNER>
                        <ISDISPLAY>true</ISDISPLAY>
                        <ISEDITABLE>true</ISEDITABLE>
                        <ISFROMPARENT>false</ISFROMPARENT>
                        <ISMAIN>false</ISMAIN>
                        <ISMANDATORY>false</ISMANDATORY>
                        <ISPERSIST>true</ISPERSIST>
                        <ISQUERY>false</ISQUERY>
                        <ISQUERYADVANCE>false</ISQUERYADVANCE>
                        <ISQUERYMANDATORY>false</ISQUERYMANDATORY>
                        <ISREADONLY>false</ISREADONLY>
                        <ISSAMELINE>false</ISSAMELINE>
                        <ISUPPER>false</ISUPPER>
                        <LABLE>Expression</LABLE>
                        <LABLEZH>表达式</LABLEZH>
                        <NAME>name</NAME>
                        <SEQNO>10</SEQNO>
                        <STYLE>0</STYLE>
                        <TABRRN>335420836041273344</TABRRN>
                        <TABLERRN>335419567994122241</TABLERRN>
                        <VALIDATELEVEL>3</VALIDATELEVEL>
                    </ADFIELD>
                </ADFIELDS>
                <ISDESIGNER>true</ISDESIGNER>
                <NAME>Form</NAME>
                <STYLE>320</STYLE>
                <TABTYPE>Form</TABTYPE>
                <TABLERRN>335419567994122241</TABLERRN>
            </ADTAB>
        </ADTABS>
    </ADTABLE>
    <ADBUTTONS/>
    <DESCRIPTION>Condition Transition Dialog</DESCRIPTION>
    <ADFORMATTRIBUTES>
        <ADFORMATTRIBUTE>
            <ISACTIVE>true</ISACTIVE>
            <OBJECTRRN>335460217066213380</OBJECTRRN>
            <ORGRRN>0</ORGRRN>
            <CREATED>2023/05/15 16:39:08</CREATED>
            <CREATEDBY>admin</CREATEDBY>
            <UPDATED>2023/05/15 16:39:08</UPDATED>
            <UPDATEDBY>admin</UPDATEDBY>
            <ATTRIBUTENAME>FieldWidth</ATTRIBUTENAME>
            <DESCRIPTION>栏位长度(默认250)</DESCRIPTION>
            <FIELDNAME>name</FIELDNAME>
            <FORMRRN>335419567994122242</FORMRRN>
            <VALUE>100</VALUE>
        </ADFORMATTRIBUTE>
        <ADFORMATTRIBUTE>
            <ISACTIVE>true</ISACTIVE>
            <OBJECTRRN>335460217066213378</OBJECTRRN>
            <ORGRRN>0</ORGRRN>
            <CREATED>2023/05/15 16:39:08</CREATED>
            <CREATEDBY>admin</CREATEDBY>
            <UPDATED>2023/05/15 16:39:08</UPDATED>
            <UPDATEDBY>admin</UPDATEDBY>
            <ATTRIBUTENAME>FormulaHeight</ATTRIBUTENAME>
            <DESCRIPTION>表达式文本框高度(默认380)</DESCRIPTION>
            <FIELDNAME>name</FIELDNAME>
            <FORMRRN>335419567994122242</FORMRRN>
        </ADFORMATTRIBUTE>
        <ADFORMATTRIBUTE>
            <ISACTIVE>true</ISACTIVE>
            <OBJECTRRN>335460217066213379</OBJECTRRN>
            <ORGRRN>0</ORGRRN>
            <CREATED>2023/05/15 16:39:08</CREATED>
            <CREATEDBY>admin</CREATEDBY>
            <UPDATED>2023/05/15 16:39:08</UPDATED>
            <UPDATEDBY>admin</UPDATEDBY>
            <ATTRIBUTENAME>FormulaWidth</ATTRIBUTENAME>
            <DESCRIPTION>表达式文本框长度(默认20)</DESCRIPTION>
            <FIELDNAME>name</FIELDNAME>
            <FORMRRN>335419567994122242</FORMRRN>
        </ADFORMATTRIBUTE>
        <ADFORMATTRIBUTE>
            <ISACTIVE>true</ISACTIVE>
            <OBJECTRRN>335460217066213377</OBJECTRRN>
            <ORGRRN>0</ORGRRN>
            <CREATED>2023/05/15 16:39:08</CREATED>
            <CREATEDBY>admin</CREATEDBY>
            <UPDATED>2023/05/15 16:39:08</UPDATED>
            <UPDATEDBY>admin</UPDATEDBY>
            <ATTRIBUTENAME>VariableCategroy</ATTRIBUTENAME>
            <DESCRIPTION>变量类别</DESCRIPTION>
            <FIELDNAME>name</FIELDNAME>
            <FORMRRN>335419567994122242</FORMRRN>
            <VALUE>TransitionCondition</VALUE>
        </ADFORMATTRIBUTE>
        <ADFORMATTRIBUTE>
            <ISACTIVE>true</ISACTIVE>
            <OBJECTRRN>335460217066213376</OBJECTRRN>
            <ORGRRN>0</ORGRRN>
            <CREATED>2023/05/15 16:39:08</CREATED>
            <CREATEDBY>admin</CREATEDBY>
            <UPDATED>2023/05/15 16:39:08</UPDATED>
            <UPDATEDBY>admin</UPDATEDBY>
            <ATTRIBUTENAME>VariableTableName</ATTRIBUTENAME>
            <DESCRIPTION>动态栏位参考表</DESCRIPTION>
            <FIELDNAME>name</FIELDNAME>
            <FORMRRN>335419567994122242</FORMRRN>
        </ADFORMATTRIBUTE>
    </ADFORMATTRIBUTES>
    <FORMSIZE>100;200</FORMSIZE>
    <FORMTYPE>ENTITY</FORMTYPE>
    <ISDESIGNER>true</ISDESIGNER>
    <ISROOT>true</ISROOT>
    <NAME>ConditionTransitionDialog</NAME>
    <ORIENTATION>VERTICAL</ORIENTATION>
    <STYLE>0</STYLE>
    <TABLERRN>335419567994122241</TABLERRN>
</ADFORM>
