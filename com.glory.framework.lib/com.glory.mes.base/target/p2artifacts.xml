<?xml version='1.0' encoding='UTF-8'?>
<?artifactRepository version='1.1.0'?>
<artifacts size='1'>
  <artifact classifier='osgi.bundle' id='com.glory.mes.base' version='8.4.0'>
    <properties size='6'>
      <property name='artifact.size' value='257579'/>
      <property name='download.size' value='257579'/>
      <property name='maven-groupId' value='com.glory.mes'/>
      <property name='maven-artifactId' value='com.glory.mes.base'/>
      <property name='maven-version' value='8.4.0'/>
      <property name='maven-type' value='eclipse-plugin'/>
    </properties>
  </artifact>
</artifacts>
