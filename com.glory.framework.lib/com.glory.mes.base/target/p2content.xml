<?xml version='1.0' encoding='UTF-8'?>
<units size='1'>
  <unit id='com.glory.mes.base' version='8.4.0' generation='2'>
    <update id='com.glory.mes.base' range='[0.0.0,8.4.0)' severity='0'/>
    <properties size='6'>
      <property name='org.eclipse.equinox.p2.name' value='Base Plug-in'/>
      <property name='org.eclipse.equinox.p2.provider' value='GlorySoft'/>
      <property name='maven-groupId' value='com.glory.mes'/>
      <property name='maven-artifactId' value='com.glory.mes.base'/>
      <property name='maven-version' value='8.4.0'/>
      <property name='maven-type' value='eclipse-plugin'/>
    </properties>
    <provides size='17'>
      <provided namespace='org.eclipse.equinox.p2.iu' name='com.glory.mes.base' version='8.4.0'/>
      <provided namespace='osgi.bundle' name='com.glory.mes.base' version='8.4.0'/>
      <provided namespace='java.package' name='com.glory.mes.base' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.calendar.details' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.calendar.setup' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.custom' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.idgenerator' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.idquery' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.lable' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.line.action' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.line.user' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.shift' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.shifttime' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.teamuser' version='0.0.0'/>
      <provided namespace='java.package' name='com.glory.mes.base.util' version='0.0.0'/>
      <provided namespace='osgi.identity' name='com.glory.mes.base' version='8.4.0'>
        <properties size='1'>
          <property name='type' value='osgi.bundle'/>
        </properties>
      </provided>
      <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
    </provides>
    <requires size='25'>
      <required namespace='osgi.bundle' name='org.eclipse.ui' range='3.107.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.ui.forms' range='3.6.200'/>
      <required namespace='osgi.bundle' name='org.eclipse.core.runtime' range='3.11.0'/>
      <required namespace='osgi.bundle' name='com.glory.framework.base' range='8.4.0'/>
      <required namespace='osgi.bundle' name='com.glory.framework.lib' range='8.4.0'/>
      <required namespace='osgi.bundle' name='com.glory.framework.runtime' range='8.4.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.nebula.widgets.nattable.core' range='1.6.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.nebula.widgets.nattable.extension.glazedlists' range='1.6.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.nebula.widgets.nattable.extension.poi' range='1.5.1'/>
      <required namespace='osgi.bundle' name='org.apache.commons.logging' range='1.1.1'/>
      <required namespace='osgi.bundle' name='ca.odell.glazedlists' range='1.9.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.nebula.cwt' range='0.9.0'/>
      <required namespace='osgi.bundle' name='org.apache.commons.lang' range='2.6.0'/>
      <required namespace='osgi.bundle' name='org.apache.log4j' range='1.2.15'/>
      <required namespace='osgi.bundle' name='javax.annotation' range='1.3.5'/>
      <required namespace='osgi.bundle' name='javax.inject' range='1.0.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.e4.core.services' range='0.0.0'/>
      <required namespace='osgi.bundle' name='org.eclipse.osgi.services' range='0.0.0'/>
      <required namespace='java.package' name='org.eclipse.e4.core.services.events' range='0.0.0'/>
      <required namespace='java.package' name='org.eclipse.e4.ui.model.application' range='0.0.0'/>
      <required namespace='java.package' name='org.eclipse.e4.ui.model.application.ui' range='0.0.0'/>
      <required namespace='java.package' name='org.eclipse.e4.ui.model.application.ui.basic' range='0.0.0'/>
      <required namespace='java.package' name='org.eclipse.e4.ui.workbench.modeling' range='0.0.0'/>
      <required namespace='java.package' name='org.osgi.service.event' range='1.4.0'/>
      <requiredProperties namespace='osgi.ee' match='(&amp;(osgi.ee=JavaSE)(version=1.8))'>
        <description>
          com.glory.mes.base
        </description>
      </requiredProperties>
    </requires>
    <artifacts size='1'>
      <artifact classifier='osgi.bundle' id='com.glory.mes.base' version='8.4.0'/>
    </artifacts>
    <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
    <touchpointData size='1'>
      <instructions size='1'>
        <instruction key='manifest'>
          Bundle-SymbolicName: com.glory.mes.base;singleton:=true&#xA;Bundle-Version: 8.4.0&#xA;
        </instruction>
      </instructions>
    </touchpointData>
  </unit>
</units>
