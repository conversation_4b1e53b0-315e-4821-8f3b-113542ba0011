<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>
	<extension-point id="com.glory.mes.base.labelproviers" name="LabelProviers" schema="schema/com.glory.mes.base.labelproviders.exsd"/>
	
	<extension
         point="com.glory.framework.base.loginaction">
         <action
         	name="LineLoginAction"
         	class="com.glory.mes.base.line.action.LineLoginAction">
        </action>
   </extension>
   
	<extension
         point="com.glory.framework.runtime.service">   
  	 	 <service 
         	class="com.glory.mes.base.client.MBASManager"
  		 	locator="mesbase/MBASManagerBean!">
  	 	 </service> 
    </extension>
    
	<extension
	      point="org.eclipse.ui.editors">
      	<editor
            class="com.glory.mes.base.calendar.setup.CalendarDayEditor"
            icon="icons/cal.png"
            id="com.glory.mes.base.calendar.setup.CalendarDayEditor"
            name="CalendarDayEditor">
	    </editor>
	    <editor
            class="com.glory.mes.base.calendar.details.CalendarDetailsEditor"
            icon="icons/cal_set.png"
            id="com.glory.mes.base.calendar.details.CalendarDetailsEditor"
            name="CalendarDetailsEditor">
	    </editor>
	    
	    <editor
            class="com.glory.mes.base.line.user.LineUserEditor"
            id="com.glory.mes.base.line.user.LineUserEditor"
            name="LineUserEditor">
	    </editor>
    </extension>
    
  	<extension
        point="com.glory.framework.base.dialogs">
         <manager
         	object="com.glory.mes.base.orgsetting.OrgSettingDialog"
         	class="com.glory.mes.base.orgsetting.OrgSettingDialog">
         </manager>
  	</extension>
  	
  	<extension
         point="com.glory.framework.base.customfields">
         <composite 
         	name="EnterPressComposite"
         	class="com.glory.mes.base.custom.EnterPressComposite"
         	description="回车查询控件"
         	category="MBAS">
         	<attribute name="Label" description="显示名(ADMessage Key)" datatype="string" isMandatory="Y" default=""></attribute>
         	<attribute name="Class" description="对象类" datatype="string" isMandatory="Y" default=""></attribute>
         	<attribute name="IsCaseSensitive" description="自动转大写" datatype="boolean" default="Y"></attribute>
         	<attribute name="QueryProperty" description="查询属性名" datatype="string" isMandatory="Y" default=""></attribute>
         	<attribute name="IsSearchObject" description="查询对象" datatype="boolean" default="Y"></attribute>
         	<attribute name="Image" description="文本框图片" datatype="string" default="Y"></attribute>
         	<attribute name="IsClearText" description="扫描成功后是否要清掉文本" datatype="boolean" default="N"></attribute>
         </composite>  
   </extension>
   
   <extension
         point="com.glory.framework.base.customfields">
         <composite 
         	name="CombineExpressionCustomComposite"
         	class="com.glory.mes.base.custom.CombineExpressionCustomComposite"
         	description="组合规则表达式控件"
         	category="MBAS">
         	<attribute name="MergeRuleRefTableName" description="RuleLine参考表" datatype="string"></attribute>
         	<attribute name="FormulaHeight" description="表达式文本框高度(默认125)" datatype="int"></attribute>
         	<attribute name="FormulaWidth" description="表达式文本框长度(默认20)" datatype="int"></attribute>
         </composite>  
   </extension>
  
  	<extension
         point="com.glory.framework.base.tree">
         <manager 
         	category="LOCATION"
         	name = "Root"
         	class="com.glory.framework.base.entitymanager.tree.ParentChildRowEditorPage"
         	table="BASLocation">
         </manager> 
         <manager 
         	category="LOCATION"
         	name = "L1"
         	class="com.glory.framework.base.entitymanager.tree.ParentChildRowEditorPage"
         	table="BASLocation">
         </manager> 
         <manager 
         	category="LOCATION"
         	name = "L2"
         	class="com.glory.framework.base.entitymanager.tree.ParentChildRowEditorPage"
         	table="BASLocation">
         </manager>        
   </extension>
   
    <extension
         point="com.glory.framework.base.images">
      <image
            id="basetable"
            src="icons/basetable.gif">
      </image>
      <image
            id="bom"
            src="icons/bom.png">
      </image>
      <image
            id="product"
            src="icons/product.gif">
      </image>
      <image
            id="ras"
            src="icons/ras.gif">
      </image>
      <image
            id="spc"
            src="icons/spc.gif">
      </image>
      <image
            id="wip"
            src="icons/wip.png">
      </image>
      <image
            id="wipadv"
            src="icons/wipadv.png">
      </image>
            <image
            id="active"
            src="icons/active.gif">
      </image>
      <image
            id="frozen"
            src="icons/frozen.gif">
      </image>
      <image
            id="unfrozen"
            src="icons/unfrozen.gif">
      </image>
      <image
            id="inactive"
            src="icons/inactive.gif">
      </image>
    </extension>
   <extension
         point="com.glory.framework.base.images">
      	<image
            id="workspace"
            src="icons/workspace.png">
      	</image>
      	<image
            id="customer-manager"
            src="icons/customer-manager.png">
      	</image>
      	<image
            id="userreflist"
            src="icons/userreflist.png">
      	</image>
      	<image
            id="location"
            src="icons/location.gif">
      	</image>
      	<image
            id="default_location"
            src="icons/default_location.gif">
      	</image>
      	<image
            id="id_rule"
            src="icons/id_rule.png">
      	</image>
      	<image
            id="id_generator"
            src="icons/id_generator.png">
      	</image>
      	<image
            id="wip_code"
            src="icons/wip_code.png">
      	</image>
      	<image
            id="cal"
            src="icons/cal.png">
      	</image>
      	<image
            id="cal_set"
            src="icons/cal_set.png">
      	</image>
      	<image
            id="before"
            src="icons/before.png">
      	</image>
      	<image
            id="next"
            src="icons/next.png">
      	</image>
      	<image
            id="info"
            src="icons/info.gif">
      	</image>
      	<image
            id="labelprinter"
            src="icons/labelprinter.png">
      	</image>
   </extension>
</plugin>
 