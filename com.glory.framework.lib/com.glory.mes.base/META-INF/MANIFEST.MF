Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Base Plug-in
Bundle-SymbolicName: com.glory.mes.base;singleton:=true
Bundle-Version: 8.4.0
Bundle-Activator: com.glory.mes.base.Activator
Bundle-Vendor: GlorySoft
Require-Bundle: org.eclipse.ui;bundle-version="3.107.0",
 org.eclipse.ui.forms;bundle-version="3.6.200",
 org.eclipse.core.runtime;bundle-version="3.11.0",
 com.glory.framework.base;bundle-version="8.4.0",
 com.glory.framework.lib;bundle-version="8.4.0",
 com.glory.framework.runtime;bundle-version="8.4.0",
 org.eclipse.nebula.widgets.nattable.core;bundle-version="1.6.0",
 org.eclipse.nebula.widgets.nattable.extension.glazedlists;bundle-version="1.6.0",
 org.eclipse.nebula.widgets.nattable.extension.poi;bundle-version="1.5.1",
 org.apache.commons.logging;bundle-version="1.1.1",
 ca.odell.glazedlists;bundle-version="1.9.0",
 org.eclipse.nebula.cwt;bundle-version="0.9.0",
 org.apache.commons.lang;bundle-version="2.6.0",
 org.apache.log4j;bundle-version="1.2.15",
 javax.annotation;bundle-version="1.3.5",
 javax.inject;bundle-version="1.0.0",
 org.eclipse.e4.core.services,
 org.eclipse.osgi.services
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
Export-Package: com.glory.mes.base,
 com.glory.mes.base.calendar.details,
 com.glory.mes.base.calendar.setup,
 com.glory.mes.base.custom,
 com.glory.mes.base.idgenerator,
 com.glory.mes.base.idquery,
 com.glory.mes.base.lable,
 com.glory.mes.base.line.action,
 com.glory.mes.base.line.user,
 com.glory.mes.base.shift,
 com.glory.mes.base.shifttime,
 com.glory.mes.base.teamuser,
 com.glory.mes.base.util
Import-Package: org.eclipse.e4.core.services.events,
 org.eclipse.e4.ui.model.application,
 org.eclipse.e4.ui.model.application.ui,
 org.eclipse.e4.ui.model.application.ui.basic,
 org.eclipse.e4.ui.workbench.modeling,
 org.osgi.service.event;version="1.4.0"
