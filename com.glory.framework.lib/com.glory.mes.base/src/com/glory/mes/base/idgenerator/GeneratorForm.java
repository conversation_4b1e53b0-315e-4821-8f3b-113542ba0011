package com.glory.mes.base.idgenerator;

import java.util.List;

import org.apache.log4j.Logger;
import org.eclipse.jface.viewers.ISelectionChangedListener;
import org.eclipse.jface.viewers.SelectionChangedEvent;
import org.eclipse.jface.viewers.StructuredSelection;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StackLayout;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.internal.DPIUtil;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.model.VersionControl;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.StringUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.idgenerator.DateRuleLine;
import com.glory.mes.base.model.idgenerator.FixedStringRuleLine;
import com.glory.mes.base.model.idgenerator.GeneratorRule;
import com.glory.mes.base.model.idgenerator.GeneratorRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceCycleRuleLine;
import com.glory.mes.base.model.idgenerator.SequenceRuleLine;
import com.glory.mes.base.model.idgenerator.VariableRuleLine;
import com.glory.mes.base.model.idgenerator.XSequenceRuleLine;
import com.glory.framework.core.exception.ExceptionBundle;

public class GeneratorForm extends EntityForm {
	private Logger logger = Logger.getLogger(GeneratorForm.class);
	
	private static final String ADTABLE_GENERATORRULELINE = "BASIDGeneratorRuleLine";
	private static final String ADTABLE_FIXEDSTRINGRULELINE = "BASIDFixedStringRuleLine";
	private static final String ADTABLE_DATERULELINE = "BASIDDateRuleLine";
	private static final String ADTABLE_VARIABLERULELINE = "BASIDVariableRuleLine";
	private static final String ADTABLE_SEQUENCERULELINE = "BASIDSequenceRuleLine";
	private static final String ADTABLE_XSEQUENCERULELINE = "BASIDXSequenceRuleLine";
	private static final String ADTABLE_SEQUENCECYCLERULELINE = "BASIDSequenceCycleRuleLine";

	private ADTable fixedStringTable;
	private ADTable dateTimeTable;
	private ADTable variableTable;
	private ADTable sequenceTable;
	private ADTable xSequenceTable;
	private ADTable sequenceCycleTable;
	
	protected ListTableManager tableManager;
	
	protected DSTypeForm dsTypeForm;

	private IField dsTypeField;//����ѡ������е�����--F,D,V,S

	private FixedStringForm fixedStringForm;
	private DateTimeForm dateTimeForm;
	private VariableForm variableForm;
	private SequenceForm sequenceForm;
	private XSequenceForm xSequenceForm;
	private SequenceCycleForm sequenceCycleForm;

	private StackLayout stackLayout;

	private MBASManager basManager;

	private ADManager adManager;
	
	private EntityProperties mastPart;

	private Button addBtn,updateBtn,removeBtn,upBtn,downBtn;

	public GeneratorForm(Composite parent, int style, ADTab tab,
			IMessageManager mmng, EntityProperties mastPart) {
		super(parent, style, tab, mmng);
		this.mastPart = mastPart;
	}

	@Override
	protected void createContent() {
		mLeftPadding = -5;
		mTopPadding = -5;
//		mRightPadding = -5;
		mBottomPadding = -5;
		
		mHorizSpacing = 0;
		mVertSpacing = 5;
		
		toolkit = new FormToolkit(getDisplay());
		
		GridLayout layout = new GridLayout();
		layout.verticalSpacing = mVertSpacing;
		layout.horizontalSpacing = mHorizSpacing;
		layout.marginLeft = mLeftPadding;
//		layout.marginRight = mRightPadding;
		layout.marginTop = mTopPadding;
		layout.marginBottom = mBottomPadding;
		layout.numColumns = 1;
		setLayout(layout);
		setLayoutData(new GridData(GridData.FILL_BOTH));
		
		Composite topComp = toolkit.createComposite(this, SWT.NONE);
		GridLayout topLayout = new GridLayout();
		topLayout.verticalSpacing = mVertSpacing;
		topLayout.horizontalSpacing = mHorizSpacing;
		topLayout.marginLeft = mLeftPadding;
		topLayout.marginRight = mRightPadding;
		topLayout.marginTop = mTopPadding;
		topLayout.marginBottom = mBottomPadding;
		topLayout.numColumns = 3;
        topComp.setLayout(topLayout);
        topComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        Composite topCompLft = toolkit.createComposite(topComp, SWT.NONE);
        topCompLft.setLayout(layout);
        GridData gd = new GridData(GridData.FILL_VERTICAL);
        gd.widthHint = DPIUtil.autoScaleUpUsingNativeDPI(150);
        topCompLft.setLayoutData(gd);
        createViewer(topCompLft);
        
        Composite topCompCtn = toolkit.createComposite(topComp, SWT.NONE);
        topCompCtn.setLayout(layout);
        topCompCtn.setLayoutData(new GridData(GridData.VERTICAL_ALIGN_BEGINNING));
        addBtn = toolkit.createButton(topCompCtn, "��", SWT.PUSH);
        SelectionListener addListener = new SelectionListener(){

			@Override
			public void widgetDefaultSelected(SelectionEvent event) {
				
			}

			@Override
			public void widgetSelected(SelectionEvent event) {
				refreshAllDSTypeForm(true, false);
			}
        };
        SelectionListener updateListener = new SelectionListener(){
        	
        	@Override
        	public void widgetDefaultSelected(SelectionEvent event) {
        		
        	}
        	
        	@Override
        	public void widgetSelected(SelectionEvent event) {
        		refreshAllDSTypeForm(false, true);
        	}
        };
        SelectionListener delListener = new SelectionListener(){
        	
        	@Override
        	public void widgetDefaultSelected(SelectionEvent event) {
        		
        	}
        	
        	@Override
        	public void widgetSelected(SelectionEvent event) {
        		try {
        			Object selectElement = tableManager.getSelectedObject();
    				if (selectElement != null){        			
	        			GeneratorRuleLine line = (GeneratorRuleLine)selectElement;
	        			if(line != null && line.getObjectRrn() != null){
	        				boolean confirmFlag = UI.showConfirm(Message.getString(ExceptionBundle.bundle.CommonConfirmDelete()));
	        				if(confirmFlag){
	        					if(basManager == null){
	        						basManager = Framework.getService(MBASManager.class);
	        					}
	        					
	        					basManager.deleteRuleLine(line, Env.getSessionContext());
	        					
	        					if(adManager == null){
	            					adManager = Framework.getService(ADManager.class);
	            				}
	        					
	        					ADBase rule = adManager.getEntity((ADBase) getObject());
								setObject(rule);
	        					mastPart.setAdObject(rule);
	        					mastPart.refresh();
	        				}
	        			}
	        			dsTypeForm.setObject(null);
	        			dsTypeForm.refresh();
    				}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
        	}
        };
        SelectionListener upListener = new SelectionListener(){
        	
        	@Override
        	public void widgetDefaultSelected(SelectionEvent event) {
        		
        	}
        	
        	@Override
        	public void widgetSelected(SelectionEvent event) {
        		try {
        			Object selectElement = tableManager.getSelectedObject();
    				if (selectElement != null){        			
	        			GeneratorRuleLine line = (GeneratorRuleLine)selectElement;
	        			if(line != null && line.getObjectRrn() != null){
	    					if(basManager == null){
	    						basManager = Framework.getService(MBASManager.class);
	    					}
	        			}
	        			
	        			basManager.moveRuleLine(line, -1, Env.getSessionContext());
	        			
	        			if(adManager == null){
	    					adManager = Framework.getService(ADManager.class);
	    				}
	        			setObject(adManager.getEntity((ADBase) getObject()));
	        			refreshRuleLineViewer();
    				}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
        	}
        };
        SelectionListener downListener = new SelectionListener(){
        	
        	@Override
        	public void widgetDefaultSelected(SelectionEvent event) {
        		
        	}
        	
        	@Override
        	public void widgetSelected(SelectionEvent event) {
        		try {
        			Object selectElement = tableManager.getSelectedObject();
    				if (selectElement != null){        			
	        			GeneratorRuleLine line = (GeneratorRuleLine)selectElement;
	        			if(line != null && line.getObjectRrn() != null){
	    					if(basManager == null){
	    						basManager = Framework.getService(MBASManager.class);
	    					}
	        			}
	        			basManager.moveRuleLine(line, 1, Env.getSessionContext());
	        			
	        			if(adManager == null){
	    					adManager = Framework.getService(ADManager.class);
	    				}
	        			setObject(adManager.getEntity((ADBase) getObject()));
	        			refreshRuleLineViewer();
    				}
				} catch (Exception e) {
					ExceptionHandlerManager.asyncHandleException(e);
				}
        	}
        };
		addBtn.addSelectionListener(addListener);
        updateBtn = toolkit.createButton(topCompCtn, "��", SWT.PUSH);
        updateBtn.addSelectionListener(updateListener);
        removeBtn = toolkit.createButton(topCompCtn, "��", SWT.PUSH);
        removeBtn.addSelectionListener(delListener);
        upBtn = toolkit.createButton(topCompCtn, "��", SWT.PUSH);
        upBtn.addSelectionListener(upListener);
        downBtn = toolkit.createButton(topCompCtn, "��", SWT.PUSH);
        downBtn.addSelectionListener(downListener);
        
        Composite topCompRgt = toolkit.createComposite(topComp, SWT.NONE);
        topCompRgt.setLayout(layout);
        topCompRgt.setLayoutData(new GridData(GridData.FILL_BOTH | GridData.VERTICAL_ALIGN_BEGINNING));
        
        Composite topCompRgtTop = toolkit.createComposite(topCompRgt, SWT.NONE);
        topCompRgtTop.setLayout(new GridLayout(2, false));
        topCompRgtTop.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
        
        createDsTypeField(topCompRgtTop, toolkit);
        
        final Composite topCompRgtBtm = toolkit.createComposite(topCompRgt, SWT.NONE);
        stackLayout = new StackLayout();
        topCompRgtBtm.setLayout(stackLayout);
        GridData topCompRgtBtmGd = new GridData(GridData.FILL_BOTH);
        topCompRgtBtm.setLayoutData(topCompRgtBtmGd);
        
        final Composite fixedStringComp = new Composite(topCompRgtBtm, SWT.NONE);//FixedString�Ľ���
        fixedStringComp.setLayout(new GridLayout());
        fixedStringComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        fixedStringTable = getTableByName(ADTABLE_FIXEDSTRINGRULELINE);
        fixedStringForm = new FixedStringForm(fixedStringComp, SWT.NONE, new FixedStringRuleLine(), fixedStringTable, null);
        fixedStringForm.setLayout(new GridLayout());
        fixedStringForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        final Composite dateTimeComp = new Composite(topCompRgtBtm, SWT.NONE);//DateTime�Ľ���
        dateTimeComp.setLayout(new GridLayout());
        dateTimeComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        dateTimeTable = getTableByName(ADTABLE_DATERULELINE);
        dateTimeForm = new DateTimeForm(dateTimeComp, SWT.NONE, new DateRuleLine(), dateTimeTable, null);
        dateTimeForm.setLayout(new GridLayout());
        dateTimeForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        final Composite variableComp = new Composite(topCompRgtBtm, SWT.NONE);//Variable�Ľ���
        variableComp.setLayout(new GridLayout());
        variableComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        variableTable = getTableByName(ADTABLE_VARIABLERULELINE);
        variableForm = new VariableForm(variableComp, SWT.NONE, new VariableRuleLine(), variableTable, null);
        variableForm.setLayout(new GridLayout());
        variableForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        final Composite sequenceComp = new Composite(topCompRgtBtm, SWT.NONE);//Sequence�Ľ���
        sequenceComp.setLayout(new GridLayout());
        sequenceComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        sequenceTable = getTableByName(ADTABLE_SEQUENCERULELINE);
        sequenceForm = new SequenceForm(sequenceComp, SWT.NONE, new SequenceRuleLine(), sequenceTable, null);
        sequenceForm.setLayout(new GridLayout());
        sequenceForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        final Composite xSequenceComp = new Composite(topCompRgtBtm, SWT.NONE);//Sequence�Ľ���
        xSequenceComp.setLayout(new GridLayout());
        xSequenceComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        xSequenceTable = getTableByName(ADTABLE_XSEQUENCERULELINE);
        xSequenceForm = new XSequenceForm(xSequenceComp, SWT.NONE, new XSequenceRuleLine(), xSequenceTable, null);
        xSequenceForm.setLayout(new GridLayout());
        xSequenceForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        final Composite sequenceCycleComp = new Composite(topCompRgtBtm, SWT.NONE);//Sequence Cycle�Ľ���
        sequenceCycleComp.setLayout(new GridLayout());
        sequenceCycleComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        sequenceCycleTable = getTableByName(ADTABLE_SEQUENCECYCLERULELINE);
        sequenceCycleForm = new SequenceCycleForm(sequenceCycleComp, SWT.NONE, new SequenceCycleRuleLine(), sequenceCycleTable, null);
        sequenceCycleForm.setLayout(new GridLayout());
        sequenceCycleForm.setLayoutData(new GridData(GridData.FILL_BOTH));
        
        dsTypeField.addValueChangeListener(new IValueChangeListener(){

			@Override
			public void valueChanged(Object source, Object newValue) {
				if(newValue == null || "".equals(((String)newValue).trim())) return;
				
				char newValueChar = ((String)newValue).charAt(0);
				switch(newValueChar){
				case 'F':
					stackLayout.topControl = fixedStringComp;
					dsTypeForm = fixedStringForm;
					topCompRgtBtm.layout();
					break;
				case 'D':
					stackLayout.topControl = dateTimeComp;
					dsTypeForm = dateTimeForm;
					topCompRgtBtm.layout();
					break;
				case 'V':
					stackLayout.topControl = variableComp;
					dsTypeForm = variableForm;
					topCompRgtBtm.layout();
					break;
				case 'X':
					stackLayout.topControl = xSequenceComp;
					dsTypeForm = xSequenceForm;
					topCompRgtBtm.layout();
					break;
				case 'S':
					stackLayout.topControl = sequenceComp;
					sequenceForm.setRule((GeneratorRule) mastPart.getAdObject());
					dsTypeForm = sequenceForm;
					topCompRgtBtm.layout();
					break;
				case 'C':
					stackLayout.topControl = sequenceCycleComp;
					sequenceCycleForm.setRule((GeneratorRule) mastPart.getAdObject());
					dsTypeForm = sequenceCycleForm;
					topCompRgtBtm.layout();
					break;
				default:
					stackLayout.topControl = fixedStringComp;
					dsTypeForm = fixedStringForm;
					topCompRgtBtm.layout();
				}
				dsTypeForm.loadFromObject();
			}
        	
        });
        
        Composite bottomComp = toolkit.createComposite(this, SWT.NONE);
        bottomComp.setLayout(layout);
        bottomComp.setLayoutData(new GridData(GridData.FILL_BOTH));
        createFormContent(bottomComp);
	}
	
	//��ҳ����ײ�����ʣ������õ���λ
	private void createFormContent(Composite body){
        GridLayout layout = new GridLayout();
        layout.verticalSpacing = mVertSpacing;
        layout.horizontalSpacing = mHorizSpacing;
        layout.marginLeft = 0;
        layout.marginRight = 0;
        layout.marginTop = mTopPadding;
        layout.marginBottom = mBottomPadding;
        body.setLayout(layout);
        
        // first: create the children controls and compute the
        // number of columns to be used by the grid layout
        int cols = 1;
        for (IField f : fields.values()){
            f.createContent(body, toolkit);
            
            Control[] ctrls = f.getControls();
            int c = f.getColumnsCount();
            c = c > ctrls.length ? c : ctrls.length;
            if (cols < c) {
                cols = c;
            }
        }
        layout.numColumns = cols * this.getGridY();
        
        // second: place the created controls inside the grid layout cells
        int i = 0;
        for (IField f : fields.values()) {
            Control[] ctrls = f.getControls();
            if (ctrls.length == 0) {
            	continue;
            }
            i++;
            if (i % getGridY() == 0 && getGridY() != 1){
	            GridData gd = (GridData)ctrls[0].getLayoutData();
	            if (gd == null) {
	                gd = new GridData();
	                ctrls[0].setLayoutData(gd);
	            }
	            gd.horizontalIndent = 10;
            }
            // get the last r controls that should be spanned horizontally
            // to fit into the grid
            int r = ctrls.length % cols;
            if (r > 0) {
                GridData gd = (GridData)ctrls[ctrls.length-1].getLayoutData();
                if (gd == null) {
                    gd = new GridData();
                    ctrls[ctrls.length-1].setLayoutData(gd);
                }
                gd.horizontalSpan = cols - r + 1;
            }
            if (f.getADField() != null){
            	ADField field = (ADField)f.getADField();
            	if (field.getIsSameline()){
            		int num = (i - 1) % this.getGridY();
            		if (num != 0){
            			Label l = toolkit.createLabel(ctrls[0].getParent(), "");
            			GridData gd = new GridData();
            			gd.horizontalSpan = cols * (this.getGridY() - num);
            			l.setLayoutData(gd);
            			l.moveAbove(ctrls[0]);
            		}
                	GridData gd = (GridData)ctrls[ctrls.length-1].getLayoutData();
                	if (gd == null) {
                        gd = new GridData();
                        ctrls[ctrls.length-1].setLayoutData(gd);
                    }
            		gd.horizontalSpan = cols * this.getGridY() - (r == 0 ? 1 : r - 1);
            		gd = (GridData)ctrls[0].getLayoutData();
    	            if (gd == null) {
    	                gd = new GridData();
    	                ctrls[0].setLayoutData(gd);
    	            }
    	            gd.horizontalIndent = 0;
            		i = 0;
            	}
            }
        }
    }

	protected void createViewer(Composite parent){
		if (tableManager == null){
			tableManager = new ListTableManager(getTableByName(ADTABLE_GENERATORRULELINE));
		}
		tableManager.newViewer(parent);
		tableManager.addSelectionChangedListener(new ISelectionChangedListener(){

			@Override
			public void selectionChanged(SelectionChangedEvent event) {
				StructuredSelection ss = (StructuredSelection) event.getSelection();
				dsTypeField.setEnabled(false);
				if(!ss.isEmpty()){
					if(ss.getFirstElement() instanceof FixedStringRuleLine){
						dsTypeField.setValue(GeneratorRuleLine.DSTYPE_FIXEDSTRING);
						fixedStringForm.setObject(ss.getFirstElement());
						fixedStringForm.loadFromObject();
					} else if(ss.getFirstElement() instanceof DateRuleLine){
						dsTypeField.setValue(GeneratorRuleLine.DSTYPE_DATETIME);
						dateTimeForm.setObject(ss.getFirstElement());
						dateTimeForm.loadFromObject();
					} else if(ss.getFirstElement() instanceof VariableRuleLine){
						dsTypeField.setValue(GeneratorRuleLine.DSTYPE_VARIABLE);
						variableForm.setObject(ss.getFirstElement());
						variableForm.loadFromObject();
					} else if(ss.getFirstElement() instanceof SequenceCycleRuleLine){
						dsTypeField.setValue(GeneratorRuleLine.DSTYPE_SEQUENCECYCLE);
						sequenceCycleForm.setObject(ss.getFirstElement());
						sequenceCycleForm.setRule((GeneratorRule) mastPart.getAdObject());
						sequenceCycleForm.loadFromObject();
					} else if(ss.getFirstElement() instanceof SequenceRuleLine){
						dsTypeField.setValue(GeneratorRuleLine.DSTYPE_SEQUENCE);
						sequenceForm.setRule((GeneratorRule) mastPart.getAdObject());
						sequenceForm.setObject(ss.getFirstElement());
						sequenceForm.loadFromObject();
					}  else if(ss.getFirstElement() instanceof XSequenceRuleLine){
						dsTypeField.setValue(GeneratorRuleLine.DSTYPE_XSEQUENCE);
						xSequenceForm.setObject(ss.getFirstElement());
						xSequenceForm.loadFromObject();
					} 
				} else {
					dsTypeField.setValue("F-Fixed String");
					fixedStringForm.setObject(new FixedStringRuleLine());
					fixedStringForm.loadFromObject();
				}
				dsTypeField.refresh();
			}
		});
	}
	
	private ADTable getTableByName(String tableName){
		try {
			if(adManager == null){
				adManager = Framework.getService(ADManager.class);
			}
			return adManager.getADTable(Env.getOrgRrn(), tableName);
		}  catch (Exception e) {
			logger.error("GeneratorForm : getTableByName()", e);
		}
		return null;
	}
	
	private void createDsTypeField(Composite composite, FormToolkit toolkit){
		ADTable tble = getTableByName(ADTABLE_GENERATORRULELINE);
		List<ADField> afds = tble.getFields();
		for(ADField afd : afds){
			if("dsType".equals(afd.getName())){
				dsTypeField = this.getField(afd);
		        dsTypeField.createContent(composite, toolkit);
		        dsTypeField.setEnabled(false);
				fields.remove(afd.getName());
			}
		}
	}
	
	public GeneratorRuleLine getRuleLine() {
		if ( dsTypeForm == null || dsTypeForm.getObject() == null ) {
			return null;
		}
		boolean flag = dsTypeForm.saveToObject();
		if(flag){
			return (GeneratorRuleLine) dsTypeForm.getObject();
		}
		return null;

	}
	
	/*public void saveRuleLines(ADBase parentObject) {
		try {
			setObject(parentObject);
			GeneratorRule rule = (GeneratorRule) getObject();
			boolean flag = dsTypeForm.saveToObject();
			if(flag){
				GeneratorRuleLine line = (GeneratorRuleLine) dsTypeForm.getObject();
				line.setOrgRrn(Env.getOrgRrn());
				line.setRuleRrn(rule.getObjectRrn());
				if(basManager == null){
					basManager = Framework.getService(MBASManager.class);
				}
				basManager.addRuleLine(rule, line, Env.getSessionContext());
				if(adManager == null){
					adManager = Framework.getService(ADManager.class);
				}
				setObject(adManager.getEntity(rule));
			}
		} catch (Exception e) {
			logger.error("GeneratorForm : saveRuleLines()", e);
		}
	}*/
	
	@Override
	public void loadFromObject() {
		super.loadFromObject();
		dsTypeField.setEnabled(false);
		refreshRuleLineViewer();
		refreshAllDSTypeForm(false, false);
		setButtonEnabled();
	}
	
	public void setButtonEnabled() {
		GeneratorRule rule = (GeneratorRule) mastPart.getAdObject();
		if (!StringUtil.isEmpty(rule.getStatus()) && !VersionControl.STATUS_UNFROZNE.equals(rule.getStatus())) {
			addBtn.setEnabled(false);
			removeBtn.setEnabled(false);
			upBtn.setEnabled(false);
			downBtn.setEnabled(false);
			updateBtn.setEnabled(false);
		} else {
			addBtn.setEnabled(true);
			removeBtn.setEnabled(true);
			upBtn.setEnabled(true);
			downBtn.setEnabled(true);
			updateBtn.setEnabled(true);
		}
	}
	
	public void refreshRuleLineViewer(){
		GeneratorRule rule = (GeneratorRule) getObject();
		if(rule != null && rule.getObjectRrn() != null){
			List<GeneratorRuleLine> lines = rule.getRuleLines();
			tableManager.setInput(lines);
			tableManager.refresh();
		}else{
			tableManager.setInput(null);
			tableManager.refresh();
		}
	}
	
	public void refreshAllDSTypeForm(boolean isForNew, boolean isForUpdate){
		Object selectElement = tableManager.getSelectedObject();
		if (!isForNew) {
			if (selectElement != null) {
				if (selectElement instanceof FixedStringRuleLine) {
					fixedStringForm.setObject(selectElement);
					fixedStringForm.loadFromObject(isForUpdate);
				} else {
					fixedStringForm.setObject(new FixedStringRuleLine());
					fixedStringForm.loadFromObject(isForUpdate);
				}
		
				if (selectElement instanceof DateRuleLine) {
					dateTimeForm.setObject(selectElement);
					dateTimeForm.loadFromObject(isForUpdate);
				} else {
					dateTimeForm.setObject(new DateRuleLine());
					dateTimeForm.loadFromObject(isForUpdate);
				}
				
				if (selectElement instanceof VariableRuleLine) {
					variableForm.setObject(selectElement);
					variableForm.loadFromObject(isForUpdate);
				} else {
					variableForm.setObject(new VariableRuleLine());
					variableForm.loadFromObject(isForUpdate);
				}
				
				if (selectElement instanceof SequenceRuleLine) {
					sequenceForm.setRule((GeneratorRule) mastPart.getAdObject());
					sequenceForm.setObject(selectElement);
					sequenceForm.loadFromObject(isForUpdate);
				} else {
					sequenceForm.setRule((GeneratorRule) mastPart.getAdObject());
					sequenceForm.setObject(new SequenceRuleLine());
					sequenceForm.loadFromObject(isForUpdate);
				}
				
				if (selectElement instanceof SequenceCycleRuleLine) {
					sequenceCycleForm.setRule((GeneratorRule) mastPart.getAdObject());
					sequenceCycleForm.setObject(selectElement);
					sequenceCycleForm.loadFromObject(isForUpdate);
				} else {
					sequenceCycleForm.setRule((GeneratorRule) mastPart.getAdObject());
					sequenceCycleForm.setObject(new SequenceCycleRuleLine());
					sequenceCycleForm.loadFromObject(isForUpdate);
				}
				
				if (selectElement instanceof XSequenceRuleLine) {
					xSequenceForm.setObject(selectElement);
					xSequenceForm.loadFromObject(isForUpdate);
				} else {
					xSequenceForm.setObject(new XSequenceRuleLine());
					xSequenceForm.loadFromObject(isForUpdate);
				}
				dsTypeField.setEnabled(false);
			}
		} else {
			tableManager.setSelection(new StructuredSelection());
			
			fixedStringForm.setObject(new FixedStringRuleLine());
			fixedStringForm.loadFromObject();
			
			dateTimeForm.setObject(new DateRuleLine());
			dateTimeForm.loadFromObject();
			
			variableForm.setObject(new VariableRuleLine());
			variableForm.loadFromObject();
			
			xSequenceForm.setObject(new XSequenceRuleLine());
			xSequenceForm.loadFromObject();
			
			sequenceCycleForm.setObject(new SequenceCycleRuleLine());
			sequenceCycleForm.setRule((GeneratorRule) mastPart.getAdObject());
			sequenceCycleForm.loadFromObject(isForUpdate);
			
			sequenceForm.setObject(new SequenceRuleLine());
			sequenceForm.setRule((GeneratorRule) mastPart.getAdObject());
			sequenceForm.loadFromObject();
			
			dsTypeField.setEnabled(true);
		}
	}
	
	public DSTypeForm getTopDSTypeForm(){
		DSTypeForm topForm = null;
		if(stackLayout != null){
			Composite topControl = (Composite) stackLayout.topControl;
			topForm = (DSTypeForm) topControl.getChildren()[0];
		}
		return topForm;
	}
}
