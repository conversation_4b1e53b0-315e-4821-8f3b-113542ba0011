package com.glory.mes.base.idgenerator;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class IdGeneratorEditor extends EntityEditor {
	
	public static final String EDITOR_ID =  "bundleclass://com.glory.mes.base/com.glory.mes.base.idgenerator.IdGeneratorEditor";
	
	@Override
	protected void createBlock(ADTable adTable) {
		block = new IdGeneratorBlock(new ListTableManager(adTable));
	}
	
}
