package com.glory.mes.base.idgenerator;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.ui.forms.IMessageManager;
import org.eclipse.ui.forms.widgets.FormToolkit;

import com.glory.framework.activeentity.model.ADField;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.RadioField;
import com.glory.framework.base.ui.forms.field.listener.IValueChangeListener;

public class VariableForm extends DSTypeForm {
	private static final Logger logger = Logger.getLogger(VariableForm.class);
	
	private static final String FIELD_ID_PARAMETER = "parameter";
	private static final String FIELD_ID_TABLE = "tablee";
	private static final String FIELD_ID_COLUMN = "columnn";
	private static final String FIELD_ID_WHERECLAUSE = "whereClause";
	
	private static final String FIELD_ID_VARIABLETYPE = "variableType";
	
	private static final String VARIABLETYPE_VALUE_PARAMETER = "PARAMETER";
	private static final String VARIABLETYPE_VALUE_DBVALUE = "DBVALUE";
	
	public VariableForm(Composite parent, int style, Object object,
			ADTable table, IMessageManager mmng) {
		super(parent, style, object, table, mmng);
	}

	@Override
	protected void createContent() {
		setGridY(2);
		super.createContent();
		addFieldListener();
	}
	
	private void addFieldListener(){
		IField f = getIFieldByID(FIELD_ID_VARIABLETYPE);
		if(f != null && f instanceof RadioField){
			f.addValueChangeListener(new IValueChangeListener(){

				@Override
				public void valueChanged(Object sender, Object newValue) {
					String selectedValue = (String)newValue;
					IField f1 = getIFieldByID(FIELD_ID_PARAMETER);
					IField f2 = getIFieldByID(FIELD_ID_TABLE);
					IField f3 = getIFieldByID(FIELD_ID_COLUMN);
					IField f4 = getIFieldByID(FIELD_ID_WHERECLAUSE);
					if(VARIABLETYPE_VALUE_PARAMETER.equals(selectedValue)){
						if(f1 != null){
							f1.setEnabled(true);
						}
						if(f2 != null){
							f2.setEnabled(false);
						}
						if(f3 != null){
							f3.setEnabled(false);
						}
						if(f4 != null){
							f4.setEnabled(false);
						}
					}
					
					if(VARIABLETYPE_VALUE_DBVALUE.equals(selectedValue)){
						if(f1 != null){
							f1.setEnabled(false);
						}
						if(f2 != null){
							f2.setEnabled(true);
						}
						if(f3 != null){
							f3.setEnabled(true);
						}
						if(f4 != null){
							f4.setEnabled(true);
						}
					}
				}
				
			});
		}
	}
	
	private IField getIFieldByID(String id){
		if(!fields.isEmpty()){
			return fields.get(id);
		}
		return null;
	}

}
