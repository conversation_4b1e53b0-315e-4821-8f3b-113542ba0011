package com.glory.mes.base.idquery;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.apache.log4j.Logger;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Event;
import org.eclipse.swt.widgets.ImportToolItem;
import org.eclipse.swt.widgets.Listener;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.forms.QueryEntityListSection;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.google.common.collect.Lists;

/**
 * ֧�ֵ���Id,����Id��ѯ���ж��������ʾ
 */
public abstract class IdQueryEntityQueryListSection extends QueryEntityListSection {

	public static final Logger logger = Logger.getLogger(IdQueryEntityQueryListSection.class);

	protected ImportToolItem itemImport;

	public static final String TEMPLATE_FILE_NAME = "id_template.xlsx";
	public static final String UPLOAD_NAME = "WIPObjectIdQuery";
	
	/**
	 * ���ε��β�ѯ����
	 */
	protected int querySize = 20;

	public IdQueryEntityQueryListSection(ListTableManager tableManager) {
		super(tableManager);
	}

	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSearch(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemExport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemImport(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}
	
	protected void createToolItemImport(ToolBar tBar) {
		itemImport = new ImportToolItem(tBar, TEMPLATE_FILE_NAME, this.getClass());
		itemImport.addListener(ImportToolItem.EVENT_TYPE_IMPORT, new Listener() {
			@Override
			public void handleEvent(Event event) {
				importObjectByIds();
			}
		});
	}
	
	public void importObjectByIds() {
		IdQueryUpload upload = new IdQueryUpload(UPLOAD_NAME);
		if (upload.getUploadProgress().init()) {
			if (upload.run()) {
				List<String> ids = upload.getIds();
				List<Object> objects = queryObjectByIds(ids);
				tableManager.setInput(objects);
			}
		}
	}
	
	/**
	 * ����Id�Ų�ѯ������Ϣ
	 */
	public List<Object> queryObjectByIds(List<String> ids) {
		//��size���з���
		List<List<String>> idGroups = Lists.partition(ids, querySize);
		
		List<Object> results = new ArrayList<Object>();
		if (idGroups.size() > 3) {
			//���ò�����ʽ
			List<Callable> tasks = new ArrayList<Callable>();
			for (List<String> idGroup : idGroups) {
				QueryTask task = new QueryTask(idGroup);
				tasks.add(task);
			}
			
			ExecutorService executor = null;
			try { 
				executor = Executors.newFixedThreadPool(idGroups.size());
				//������ɷ���
				CompletionService completionService = new ExecutorCompletionService(executor);
				List<Future> futureList = new ArrayList<Future>();
				for (Callable task : tasks) {
					//���̳߳��ύ����
					futureList.add(completionService.submit(task));
				}
				
				for (int i = 0; i < tasks.size(); i++) {
					//����,�ȴ����ؽ��
					completionService.take();
				}
				
				//��Future˳�򷵻ؽ��
				for (Future future : futureList) {
					List<Object> result = (List<Object>)future.get();
					results.addAll(result);
				}
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			} finally { 
		        //�ر��̳߳�
				if (executor != null) {
					executor.shutdown(); 
				}
		    }
		} else {
			//����ѭ����ʽ
			for (List<String> idGroup : idGroups) {
				List<Object> result = getObjectsByInClause(idGroup);
				results.addAll(result);
			}
		}
		return results;
	}
	
	/**
	 * ����Ids��Ϊ��ѯ������ѯ���з��������Ķ���
	 * �˷�����Ҫ������
	 */
	public abstract List<Object> getObjectsByInClause(List<String> ids);
	
	class QueryTask implements Callable<List<Object>> {
		private List<String> ids;
		
		public QueryTask(List<String> ids) {
			this.ids = ids;
		}
		
		@Override
		public List<Object> call() throws Exception {
			List<Object> result = getObjectsByInClause(ids);
			return result;
		}
	}
}
