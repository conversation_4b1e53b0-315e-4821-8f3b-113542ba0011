package com.glory.mes.base.idquery;

import java.util.ArrayList;
import java.util.List;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.base.excel.Upload;
import com.glory.framework.core.util.StringUtil;
import com.glory.mes.base.model.IdObject;

public class IdQueryUpload extends Upload {

	private List<String> ids;

	public IdQueryUpload(String name) {
		super(name);
	}
	
	public IdQueryUpload(String authorityName, String buttonName, String tableName) {
		super(authorityName, buttonName, tableName);
	}
	
	@Override
	protected void cudEntityList() {
		try {
			ids = new ArrayList<String>();
			List<ADBase> uploadList = progress.getUploadList(); 
	        
	        if (uploadList != null && uploadList.size() > 0) {
	        	for (int i = 0; i < uploadList.size(); i++) {
	        		IdObject idObj = (IdObject)uploadList.get(i);
	        		if (!StringUtil.isEmpty(idObj.getId())) {
	        			ids.add(idObj.getId());
	        		}
				}
	        }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public List<String> getIds() {
		return ids;
	}

	public void setIds(List<String> ids) {
		this.ids = ids;
	}

}
