package com.glory.mes.base.mergerule;


import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.eclipse.swt.graphics.Point;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.GlcFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.forms.field.QueryFormField;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.framework.variable.client.VariableManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.merge.MergeRule;
import com.glory.mes.base.merge.MergeRuleLine;
import com.glory.mes.base.merge.MergeRuleResult;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.rule.RuleFactory;
import com.glory.mes.wip.rule.merge.LotMergeRuleContext;

public class BASMergeRuleLotMockDialog extends GlcBaseDialog { 
	
	private static int DIALOG_WIDTH = 810;
	private static int DIALOG_HEIGHT = 350;

	private static final String FIELD_LOTQUERY = "lotQuery";
	private static final String FIELD_MOCKLOTRESULT = "mockLotResult";
	private static final String FIELD_GLCFORM = "lotInfo";

	private static final String BUTTON_MOCK = "mock";

	protected QueryFormField lotQueryField;
	protected GlcFormField glcFormField;
	protected ListTableManagerField mockLotResultField;
	
	protected MergeRule mergeRule;

	public BASMergeRuleLotMockDialog(String adFormName, String authority, IEventBroker eventBroker) {
		super(adFormName, authority, eventBroker);
	}
	
	public BASMergeRuleLotMockDialog(String adFormName, String authority, IEventBroker eventBroker, MergeRule mergeRule) {
		super(adFormName, authority, eventBroker);
		this.mergeRule = mergeRule;
	}

	@Override
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);

		glcFormField = form.getFieldByControlId(FIELD_GLCFORM, GlcFormField.class);
		lotQueryField = glcFormField.getFieldByControlId(FIELD_LOTQUERY, QueryFormField.class);
		mockLotResultField = form.getFieldByControlId(FIELD_MOCKLOTRESULT, ListTableManagerField.class);

		subscribeAndExecute(eventBroker, form.getFullTopic(BUTTON_MOCK), this::mockAdapter);
	}

	private void mockAdapter(Object object) {
		try {
			if (lotQueryField.getCheckedObjects().size() < 2) {
				UI.showInfo(Message.getString("com.please_select_muilt_lot"));
				return;
			}
			List<Lot> lots = lotQueryField.getCheckedObjects().stream().map(o -> ((Lot)o)).collect(Collectors.toList());
			
			ADManager adManager = Framework.getService(ADManager.class);
			VariableManager variableManager = Framework.getService(VariableManager.class);
			MBASManager mbasManager = Framework.getService(MBASManager.class);
			LotManager lotManager = Framework.getService(LotManager.class);
			
			LotMergeRuleContext context = new LotMergeRuleContext();
			context.setObjectType(LotMergeRuleContext.OBJECT_TYPE_LOTLIST);
			context.setTransaction(LotMergeRuleContext.TRANSACTION_PACK);
			context.setCategory(mergeRule.getCategory());
			SessionContext sc = new SessionContext();
			sc.setOrgRrn(lots.get(0).getOrgRrn());
			context.setSessionContext(sc);
			context.setBaseObject(lots.get(0));
			lots.remove(0);
			List<Lot> childLots = lots;
			context.setCheckObject(childLots);
			context.setMergeRuleName(mergeRule.getName());
			context.setBasicCheck(true);
			context.setCheckAllRule(true);
			context.setAdManager(adManager);
			context.setVariableManager(variableManager);
			context.setMbasManager(mbasManager);	
			context.setLotManager(lotManager);
			
			MergeRuleResult result = RuleFactory.getMergeRule().checkMergeRule(context);
			if (CollectionUtils.isNotEmpty(result.getMergeRule().getMergeRuleLines())) {
				//����
				List<Lot> inputLots = new ArrayList<Lot>();
				List<MergeRuleLine> mergeRuleLines = result.getMergeRule().getMergeRuleLines().stream().sorted(Comparator.comparing(MergeRuleLine :: getSeqNo)).collect(Collectors.toList());
				mergeRuleLines = mergeRuleLines.stream().sorted(Comparator.comparing(MergeRuleLine :: getRuleType, Collections.reverseOrder())).collect(Collectors.toList());
				for (MergeRuleLine mergeRuleLine : mergeRuleLines) {
					Lot lot = new Lot();
					for (String mergeRuleName : result.getResultMap().keySet()) {
						if (mergeRuleLine.getRuleName().equals(mergeRuleName)) {
							lot.setAttribute1(mergeRuleName);
							lot.setAttribute2(mergeRuleLine.getRuleType());
							if (MergeRuleLine.EQUAL_OPERATOR_EQUAL.equals(mergeRuleLine.getOperator())) {
								lot.setAttribute3(MergeRuleLine.EQUAL_OPERATOR_EQUAL);
							} else {
								lot.setAttribute3(mergeRuleLine.getExpression());
							}
							lot.setAttribute4(result.getResultMap().get(mergeRuleName).toString());
						}
					}
					inputLots.add(lot);
				}
				mockLotResultField.getListTableManager().setInput(inputLots);
			}
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
	}
	
	@Override
	protected void okPressed() {
		super.okPressed();
	}
	
	@Override
	protected Point getInitialSize() {
		Point shellSize = super.getInitialSize();
		return new Point(Math.min(
			convertHorizontalDLUsToPixels(DIALOG_WIDTH), shellSize.x),
			Math.min(convertVerticalDLUsToPixels(DIALOG_HEIGHT),
					shellSize.y));
	}

}