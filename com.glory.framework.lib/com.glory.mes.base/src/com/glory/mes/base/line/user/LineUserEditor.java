package com.glory.mes.base.line.user;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class LineUserEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.base/com.glory.mes.base.line.user.LineUserEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new LineUserBlock(new ListTableManager(adTable));
	}
	
}
