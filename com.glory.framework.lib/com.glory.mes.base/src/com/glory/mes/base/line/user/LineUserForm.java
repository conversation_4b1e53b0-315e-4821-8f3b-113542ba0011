package com.glory.mes.base.line.user;

import org.apache.log4j.Logger;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.IMessageManager;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.activeentity.model.ADTab;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.ui.forms.field.IField;
import com.glory.framework.base.ui.forms.field.TableSelectField;
import com.glory.framework.base.ui.nattable.ListTableManager;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;

public class LineUserForm extends EntityForm {
	
	private static final Logger logger = Logger.getLogger(LineUserForm.class);
	public static final String FIELD_ID = "reserved1";
	
	public LineUserForm (Composite parent, int style, ADTab tab, IMessageManager mmng) {
		super(parent, style, tab, mmng);
	}
	
	@Override
	public void addFields() {
		super.addFields();
		
		TableSelectField field = null;
		try{
			ADManager entityManager = Framework.getService(ADManager.class);
			ADTable adTable = entityManager.getADTable(Env.getOrgRrn(), "ADUser");
			ListTableManager tableManager = new ListTableManager(adTable, true);
			field = createTableSelectField(FIELD_ID, "", tableManager, 20);
			addField(FIELD_ID, field);
		} catch (Exception e){
			logger.error("LineEqpForm : Init tablelist", e);
		}
	}
	
	@Override
	public boolean saveToObject() {
		if (object != null){
			IField f = fields.get(FIELD_ID);
			PropertyUtil.setProperty(object, f.getId(), f.getValue());
			return true;
		}
		return false;
	}
	
	@Override
	public void loadFromObject() {
		if (object != null) {
			IField f = fields.get(FIELD_ID);
			f.setValue(PropertyUtil.getPropertyForIField(object, f.getId()));
			refresh();
		}
	}
	
}