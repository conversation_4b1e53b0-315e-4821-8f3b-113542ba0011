package com.glory.mes.base.teamuser;

import org.apache.log4j.Logger;
import org.eclipse.ui.forms.DetailsPart;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityBlock;
import com.glory.framework.base.entitymanager.forms.EntityProperties;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class TeamUserBlock extends EntityBlock {

	private static final Logger logger = Logger.getLogger(TeamUserBlock.class);

	EntityProperties page;

	public TeamUserBlock(ListTableManager tableManager){
		super(tableManager);
	}
	
	@Override
	protected void registerPages(DetailsPart detailsPart) {
		try{
			ADTable adTable = getTableManager().getADTable();
			Class<?> klass = Class.forName(adTable.getModelClass());
			page = new TeamUserProperties();
			page.setTable(adTable);
			page.setMasterParent(this); 
			detailsPart.registerPage(klass, page);
			initRefresh();
		} catch (Exception e){
			logger.error("TeamUserBlock : registerPages ", e);
		}
	}
	
	public void setFocus() {
		((TeamUserProperties)page).setFocus();
	}
	
	@Override
	public void queryAdapter() {
		super.queryAdapter();
	}
}
