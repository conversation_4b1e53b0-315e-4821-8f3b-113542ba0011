package com.glory.mes.base.teamuser;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class TeamUserEditor extends EntityEditor {
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.base/com.glory.mes.base.teamuser.TeamUserEditor";

	@Override
	protected void createBlock(ADTable adTable) {
		block = new TeamUserBlock(new ListTableManager(adTable));
	}
	
}
