package com.glory.mes.base.shifttime;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

import org.eclipse.e4.ui.model.application.ui.basic.MPart;
import org.eclipse.e4.ui.workbench.modeling.ESelectionService;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.forms.ManagedForm;
import org.eclipse.ui.forms.widgets.FormToolkit;
import org.eclipse.ui.forms.widgets.ScrolledForm;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.application.command.CommandParameter;
import com.glory.framework.base.entitymanager.editor.EntityEditor;
import com.glory.framework.base.ui.forms.FFormToolKit;
import com.glory.framework.base.ui.nattable.ListTableManager;

public class ShiftTimeEditor extends EntityEditor{
	
	public static final String EDITOR_ID = "bundleclass://com.glory.mes.base/com.glory.mes.base.shifttime.ShiftTimeEditor";
	
	@Inject
	protected ESelectionService selectionService;

	@Inject
	protected MPart mPart;
	
	@PostConstruct
	public void postConstruct(Composite parent) {
		ADTable adTable = (ADTable)mPart.getTransientData().get(CommandParameter.PARAM_ADTABLE);
		configureBody(parent);
//		mPart.setLabel(Message.getString("bas.teamtime_editortitle"));
        createBlock(new ListTableManager(adTable));
        FormToolkit toolkit = new FFormToolKit(parent.getDisplay());
        ScrolledForm form = toolkit.createScrolledForm(parent);
        form.setLayout(new GridLayout(1, true));
        form.setLayoutData(new GridData(1808));
        ManagedForm mform = new ManagedForm(toolkit, form);
        block.createContent(mform);
        fireSelectionChanged(mform);
	}
	
	private void createBlock(ListTableManager tableManager){
		block = new ShiftTimeBlock(tableManager);
	}
	
	protected void configureBody(Composite body) {
		GridLayout layout = new GridLayout();
		layout.marginWidth = 0;
		layout.marginHeight = 0;
		body.setLayout(layout);
	}
}
