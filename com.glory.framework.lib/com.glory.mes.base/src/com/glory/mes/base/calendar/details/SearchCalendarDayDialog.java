package com.glory.mes.base.calendar.details;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Shell;
import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.dialog.EntityDialog;
import com.glory.framework.base.ui.forms.Form;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.swt.GrabCorner;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.mes.base.model.CalendarDay;

public class SearchCalendarDayDialog extends EntityDialog {
	
	private CalendarDay calendarDay;

	public SearchCalendarDayDialog(Shell parent, ADTable table, ADBase adObject) {
		super(table, adObject);
	}

	@Override
    protected void createFormContent(Composite parent) {
		Composite composite = new Composite(parent, SWT.NONE);
		GridLayout layout = new GridLayout();
		layout.marginHeight = 0;
		layout.marginWidth = 0;
		layout.verticalSpacing = 0;
		layout.horizontalSpacing = 0;
		composite.setLayout(layout);
		composite.setLayoutData(new GridData(GridData.FILL_BOTH));
		composite.setFont(parent.getFont());
		// Build the separator line
		Label titleBarSeparator = new Label(composite, SWT.HORIZONTAL| SWT.SEPARATOR);
		titleBarSeparator.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
        setTitleImage(SWTResourceCache.getImage("search-dialog"));
        setTitle(Message.getString("common.search_Title"));
        setMessage(Message.getString("common.keys"));
        super.createFormContent(composite);
    }
	
	@Override
	protected void buttonPressed(int buttonId) {
		if (IDialogConstants.OK_ID == buttonId) {
			if (getAdObject() != null) {
				boolean saveFlag = true;
				for (Form detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm.getObject(), detailForm.getCopyProperties());
					}
					Object obj = getAdObject();
					if (obj instanceof CalendarDay) {
						calendarDay = (CalendarDay) obj;
					}
					okPressed();
					refresh();
				}
			}
			
		} else if (IDialogConstants.CANCEL_ID == buttonId) {
			cancelPressed();
		}
	}
	
//	protected Point getInitialSize() {
//		Point p = super.getInitialSize();
//		p.x = 400;
//		p.y = 370;
//		return p;
//	}

	public CalendarDay getCalendarDay() {
		return calendarDay;
	}

	public void setCalendarDay(CalendarDay calendarDay) {
		this.calendarDay = calendarDay;
	}
}
