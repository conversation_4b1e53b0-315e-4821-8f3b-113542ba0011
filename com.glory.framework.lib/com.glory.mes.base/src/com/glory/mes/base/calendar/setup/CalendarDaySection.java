package com.glory.mes.base.calendar.setup;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.IManagedForm;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.activeentity.model.ADTable;
import com.glory.framework.base.entitymanager.forms.EntityForm;
import com.glory.framework.base.entitymanager.forms.GroupEntitySection;
import com.glory.framework.base.ui.forms.IForm;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.SWTResourceCache;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.PropertyUtil;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.base.client.MBASManager;
import com.glory.mes.base.model.CalendarSetup;

public class CalendarDaySection extends GroupEntitySection {
	
	protected ToolItem itemTerminate;

	public CalendarDaySection(ADTable table) {
		super.setTable(table);
		this.setTable(table);
		try {
			CalendarSetup c = new CalendarSetup();
			setAdObject(c);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void createContents(IManagedForm form, Composite parent) {
		super.createContents(form, parent);
		section.setText(Message.getString("bas.calendarday_sectiontitle"));
	}

	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);
		section.setTextClient(tBar);
	}

	protected void createToolItemSave(ToolBar tBar) {
		itemSave = new ToolItem(tBar, SWT.PUSH);
		itemSave.setText(Message.getString("bas.calendar_generate"));
		itemSave.setImage(SWTResourceCache.getImage("save"));
		itemSave.addSelectionListener(new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent event) {
				saveAdapter();
			}
		});
	}

	@Override
	protected void saveAdapter() {
		if (getAdObject() != null) {
			try {
				form.getMessageManager().removeAllMessages();
				boolean saveFlag = true;
				for (IForm detailForm : getDetailForms()) {
					if (!detailForm.saveToObject()) {
						saveFlag = false;
					}
				}
				Long firstDayOfWeek = 0l;
				if (saveFlag) {
					for (IForm detailForm : getDetailForms()) {
						PropertyUtil.copyProperties(getAdObject(), detailForm
								.getObject(), detailForm.getCopyProperties());
						EntityForm form = (EntityForm) detailForm;
						// �����������ΪPropertyUtil.copyProperties�ⷽ�����ܰ�firstDayOfWeek��getAdObject()��
						if (form.get("firstDayOfWeek") != null) {
							firstDayOfWeek = Long.valueOf((String)form.getFields().get("firstDayOfWeek").getValue());
						}
					}
					MBASManager mBasManager = Framework.getService(MBASManager.class);
					boolean flag = mBasManager.checkCalendarID(getAdObject());
					if (flag == true) {
						flag = UI.showConfirm(Message.getString("bas.calendar_check_prompt"));
						if (flag == true) {
							CalendarSetup cal = (CalendarSetup) getAdObject();
							cal.setFirstDayOfWeek(firstDayOfWeek);
							mBasManager.generateCalendar(cal, Env.getSessionContext());
							UI.showInfo(Message.getString("bas.calendar_generate_successed"));// ������ʾ��
						}
					} else {
						mBasManager.generateCalendar(getAdObject(), Env.getSessionContext());
						UI.showInfo(Message.getString("bas.calendar_generate_successed"));// ������ʾ��
					}
					// refresh();
				}
			} catch (Exception e) {
				ExceptionHandlerManager.asyncHandleException(e);
				e.printStackTrace();
				return;
			}
		}
	}

}
