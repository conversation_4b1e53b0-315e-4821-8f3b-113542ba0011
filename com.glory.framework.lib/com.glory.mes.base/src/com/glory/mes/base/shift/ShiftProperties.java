package com.glory.mes.base.shift;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;
import org.eclipse.ui.forms.widgets.Section;

import com.glory.framework.base.entitymanager.forms.EntityProperties;

public class ShiftProperties extends EntityProperties {
	
	public ShiftProperties() {
		super();
	}
	
	@Override
	public void createToolBar(Section section) {
		ToolBar tBar = new ToolBar(section, SWT.FLAT | SWT.HORIZONTAL);
		createToolItemNew(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemSave(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemDelete(tBar);
		new ToolItem(tBar, SWT.SEPARATOR);
		createToolItemRefresh(tBar);	
		section.setTextClient(tBar);
	}
	
	
	@Override
	public void newAdapter(){
		super.newAdapter();
		getMasterParent().refresh();
	}

}
