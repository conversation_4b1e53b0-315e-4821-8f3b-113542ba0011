#
# <PERSON><PERSON><PERSON>, Home of Professional Open Source
# Copyright 2014, Red Hat, Inc. and/or its affiliates, and individual
# contributors by the @authors tag. See the copyright.txt in the
# distribution for a full listing of individual contributors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
# http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
endpoint.name=client-endpoint
remote.connectionprovider.create.options.org.xnio.Options.SSL_ENABLED=false

remote.connections=mes01

remote.connection.mes01.host=127.0.0.1
remote.connection.mes01.port=8080
remote.connection.mes01.connect.options.org.xnio.Options.SASL_POLICY_NOANONYMOUS=false
remote.connection.mes01.connect.options.org.jboss.remoting3.RemotingOptions.HEARTBEAT_INTERVAL=60000
