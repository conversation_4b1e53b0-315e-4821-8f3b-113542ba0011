Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Lib Plug-in
Bundle-SymbolicName: com.glory.framework.lib;singleton:=true
Bundle-Version: 8.4.0
Bundle-Vendor: GlorySoft
Bundle-RequiredExecutionEnvironment: JavaSE-1.6
Eclipse-BundleShape: dir
Require-Bundle: org.eclipse.core.runtime;bundle-version="3.11.0",
 org.apache.log4j;bundle-version="1.2.19",
 org.dom4j;bundle-version="1.6.1",
 org.apache.commons.collections4;bundle-version="4.4.0"
Import-Package: org.eclipse.ui.plugin
Bundle-Activator: com.glory.framework.lib.Activator
Bundle-ActivationPolicy: lazy
Automatic-Module-Name: com.glory.framework.lib
Bundle-ClassPath: .,
 lib/activeentity-client.jar,
 lib/activeentitymodel.jar,
 lib/context-client.jar,
 lib/contextmodel.jar,
 lib/core.jar,
 lib/ecn-client.jar,
 lib/ecnmodel.jar,
 lib/edc-client.jar,
 lib/edcmodel.jar,
 lib/esb.jar,
 lib/excelmodel.jar,
 lib/math.jar,
 lib/mesbase-client.jar,
 lib/mesbasemodel.jar,
 lib/mesmsgmodel.jar,
 lib/mm-client.jar,
 lib/mmmodel.jar,
 lib/msg.jar,
 lib/msgmodel.jar,
 lib/ras-client.jar,
 lib/rasmodel.jar,
 lib/security-client.jar,
 lib/securitymodel.jar,
 lib/state-client.jar,
 lib/statemodel.jar,
 lib/system-client.jar,
 lib/tenant-client.jar,
 lib/wip-client.jar,
 lib/wipmodel.jar,
 runtime/antlr-2.7.7.jar,
 runtime/batik-all-1.14.jar,
 runtime/bsh-core-2.0b4.jar,
 runtime/commons-compress-1.18.jar,
 runtime/commons-io-2.11.0.jar,
 runtime/commons-net-3.6.jar,
 runtime/commons-pool2-2.8.0.jar,
 runtime/commons-vfs2-2.9.0.jar,
 runtime/guava-31.1-jre.jar,
 runtime/hibernate-core-5.3.24.Final.jar,
 runtime/hibernate-jpa-2.0-api-1.0.1.Final-redhat-2.jar,
 runtime/infinispan-commons-13.0.10.Final.jar,
 runtime/infinispan-core-13.0.10.Final.jar,
 runtime/javassist-3.27.0-GA.jar,
 runtime/jboss-client.jar,
 runtime/jboss-transaction-api_1.3_spec-2.0.0.Final.jar,
 runtime/opencsv-2.0.1.jar,
 runtime/poi-4.0.1.jar,
 runtime/poi-ooxml-4.0.1.jar,
 runtime/poi-ooxml-schemas-4.0.1.jar,
 runtime/reflections-0.9.12.jar,
 runtime/velocity-engine-core-2.3.jar,
 runtime/xml-apis-ext-1.3.04.jar,
 runtime/xmlbeans-3.0.2.jar,
 runtime/fel.jar,
 runtime/byte-buddy-1.11.12.jar,
 runtime/jboss-jaxb-api_2.3_spec-2.0.1.Final.jar,
 lib/label-client.jar,
 lib/labelmodel.jar,
 lib/excel-client.jar
Export-Package: .,
 antlr,
 antlr.ASdebug,
 antlr.actions.cpp,
 antlr.actions.csharp,
 antlr.actions.java,
 antlr.actions.python,
 antlr.build,
 antlr.collections,
 antlr.collections.impl,
 antlr.debug,
 antlr.debug.misc,
 antlr.preprocessor,
 au.com.bytecode.opencsv,
 au.com.bytecode.opencsv.bean,
 bsh,
 com.glory.common.context.client,
 com.glory.common.context.model,
 com.glory.common.doc.client,
 com.glory.common.doc.model,
 com.glory.common.ecn.client,
 com.glory.common.ecn.model,
 com.glory.common.esb,
 com.glory.common.esb.cli,
 com.glory.common.esb.direct,
 com.glory.common.esb.direct.config,
 com.glory.common.esb.dummy,
 com.glory.common.esb.file,
 com.glory.common.esb.file.config,
 com.glory.common.esb.ftp,
 com.glory.common.esb.ftp.config,
 com.glory.common.esb.interceptor,
 com.glory.common.esb.jms,
 com.glory.common.esb.jms.config,
 com.glory.common.esb.socket,
 com.glory.common.esb.socket.config,
 com.glory.common.excel,
 com.glory.common.excel.client,
 com.glory.common.excel.upload,
 com.glory.common.fel,
 com.glory.common.fel.common,
 com.glory.common.fel.compile,
 com.glory.common.fel.context,
 com.glory.common.fel.exception,
 com.glory.common.fel.function,
 com.glory.common.fel.function.operator,
 com.glory.common.fel.interpreter,
 com.glory.common.fel.optimizer,
 com.glory.common.fel.parser,
 com.glory.common.fel.security,
 com.glory.common.label,
 com.glory.common.label.client,
 com.glory.common.label.model,
 com.glory.common.math,
 com.glory.common.math.analysis,
 com.glory.common.math.analysis.solvers,
 com.glory.common.math.distribution,
 com.glory.common.math.spc,
 com.glory.common.math.special,
 com.glory.common.math.stat,
 com.glory.common.math.stat.descriptive,
 com.glory.common.math.stat.descriptive.moment,
 com.glory.common.math.stat.descriptive.rank,
 com.glory.common.math.stat.descriptive.summary,
 com.glory.common.math.util,
 com.glory.common.state.action,
 com.glory.common.state.client,
 com.glory.common.state.model,
 com.glory.edc.client,
 com.glory.edc.exception,
 com.glory.edc.model,
 com.glory.edc.model.auto,
 com.glory.edc.model.calculation,
 com.glory.edc.model.compattr,
 com.glory.edc.model.sampling,
 com.glory.edc.model.spc,
 com.glory.edc.model.subgroup,
 com.glory.framework.activeentity.client,
 com.glory.framework.activeentity.model,
 com.glory.framework.base.model,
 com.glory.framework.core.cache,
 com.glory.framework.core.cdi,
 com.glory.framework.core.chain,
 com.glory.framework.core.config,
 com.glory.framework.core.db,
 com.glory.framework.core.esb,
 com.glory.framework.core.exception,
 com.glory.framework.core.retry,
 com.glory.framework.core.service,
 com.glory.framework.core.statemachine,
 com.glory.framework.core.trace,
 com.glory.framework.core.util,
 com.glory.framework.core.xml,
 com.glory.framework.lib,
 com.glory.framework.security.client,
 com.glory.framework.security.model,
 com.glory.framework.tenant.client,
 com.glory.framework.tenant.model,
 com.glory.framework.variable.client,
 com.glory.framework.variable.model,
 com.glory.mes.auto.cdi,
 com.glory.mes.base.calendar,
 com.glory.mes.base.client,
 com.glory.mes.base.config,
 com.glory.mes.base.merge,
 com.glory.mes.base.model,
 com.glory.mes.base.model.idgenerator,
 com.glory.mes.base.util,
 com.glory.mes.mm.bom.model,
 com.glory.mes.mm.cdi,
 com.glory.mes.mm.client,
 com.glory.mes.mm.consumable.model,
 com.glory.mes.mm.durable.model,
 com.glory.mes.mm.exception,
 com.glory.mes.mm.his.model,
 com.glory.mes.mm.inv.model,
 com.glory.mes.mm.lot.model,
 com.glory.mes.mm.merge,
 com.glory.mes.mm.model,
 com.glory.mes.mm.state.model,
 com.glory.mes.msg,
 com.glory.mes.msg.model,
 com.glory.mes.msg.model.rtd,
 com.glory.mes.pp.client,
 com.glory.mes.pp.model,
 com.glory.mes.prd.client,
 com.glory.mes.prd.model,
 com.glory.mes.prd.model.his,
 com.glory.mes.prd.workflow,
 com.glory.mes.prd.workflow.action.chain,
 com.glory.mes.prd.workflow.action.def,
 com.glory.mes.prd.workflow.action.exe,
 com.glory.mes.prd.workflow.bsh,
 com.glory.mes.prd.workflow.bytes,
 com.glory.mes.prd.workflow.calendar,
 com.glory.mes.prd.workflow.configuration,
 com.glory.mes.prd.workflow.configuration.converter,
 com.glory.mes.prd.workflow.context.def,
 com.glory.mes.prd.workflow.context.exe,
 com.glory.mes.prd.workflow.context.exe.converter,
 com.glory.mes.prd.workflow.context.exe.matcher,
 com.glory.mes.prd.workflow.context.exe.variableinstance,
 com.glory.mes.prd.workflow.file.def,
 com.glory.mes.prd.workflow.graph.def,
 com.glory.mes.prd.workflow.graph.exe,
 com.glory.mes.prd.workflow.graph.node,
 com.glory.mes.prd.workflow.instantiation,
 com.glory.mes.prd.workflow.jaxb,
 com.glory.mes.prd.workflow.jaxb.content,
 com.glory.mes.prd.workflow.jaxb.document,
 com.glory.mes.prd.workflow.jpdl.xml,
 com.glory.mes.prd.workflow.module.def,
 com.glory.mes.prd.workflow.save,
 com.glory.mes.prd.workflow.stateseq,
 com.glory.mes.prd.workflow.util,
 com.glory.mes.ras.client,
 com.glory.mes.ras.config,
 com.glory.mes.ras.constraint,
 com.glory.mes.ras.eqp,
 com.glory.mes.ras.eqp.cdi,
 com.glory.mes.ras.eqp.his,
 com.glory.mes.ras.model,
 com.glory.mes.ras.model.state,
 com.glory.mes.ras.port,
 com.glory.mes.ras.port.his,
 com.glory.mes.wip.action,
 com.glory.mes.wip.cdi,
 com.glory.mes.wip.cdi.client,
 com.glory.mes.wip.cdi.impl,
 com.glory.mes.wip.cell,
 com.glory.mes.wip.client,
 com.glory.mes.wip.condition,
 com.glory.mes.wip.exception,
 com.glory.mes.wip.future,
 com.glory.mes.wip.his,
 com.glory.mes.wip.mm,
 com.glory.mes.wip.model,
 com.glory.mes.wip.pack,
 com.glory.mes.wip.rule,
 com.glory.mes.wip.rule.merge,
 com.glory.mes.wip.rule.merge.cdi,
 com.glory.mes.wip.rule.merge.cdi.impl,
 com.glory.mes.wip.service.out,
 com.glory.mes.wip.sort,
 com.glory.mes.wip.sort.comparator,
 com.glory.mes.wip.sorting,
 com.glory.mes.wip.track.model,
 com.glory.msg,
 com.glory.msg.activeentity.entitylist,
 com.glory.msg.activeentity.entitymapkeyrootlistbyquery,
 com.glory.msg.activeentity.entitymaplist,
 com.glory.msg.activeentity.entitymaplistbyquery,
 com.glory.msg.activeentity.getentity,
 com.glory.msg.activeentity.getentitydeep,
 com.glory.msg.activeentity.handlermessage,
 com.glory.msg.activeentity.messagemanage,
 com.glory.msg.adreflist,
 com.glory.msg.adureflist,
 com.glory.msg.getadowenrreflist,
 com.glory.msg.message,
 com.glory.msg.model,
 com.glory.msg.security.authority,
 com.glory.msg.security.authority.manage,
 com.glory.msg.security.changeuserinfo,
 com.glory.msg.security.login,
 com.glory.msg.security.user,
 com.glory.msg.security.usergroup,
 com.glory.msg.security.usergroup.authority,
 com.glory.msg.trans,
 com.glory.msg.utils,
 com.google.common.annotations,
 com.google.common.base,
 com.google.common.base.internal,
 com.google.common.cache,
 com.google.common.collect,
 com.google.common.escape,
 com.google.common.eventbus,
 com.google.common.graph,
 com.google.common.hash,
 com.google.common.html,
 com.google.common.io,
 com.google.common.math,
 com.google.common.net,
 com.google.common.primitives,
 com.google.common.reflect,
 com.google.common.util.concurrent,
 com.google.common.util.concurrent.internal,
 com.google.common.xml,
 com.google.thirdparty.publicsuffix,
 com.microsoft.schemas.compatibility,
 com.microsoft.schemas.compatibility.impl,
 com.microsoft.schemas.office.excel,
 com.microsoft.schemas.office.excel.impl,
 com.microsoft.schemas.office.office,
 com.microsoft.schemas.office.office.impl,
 com.microsoft.schemas.office.visio.x2012.main,
 com.microsoft.schemas.office.visio.x2012.main.impl,
 com.microsoft.schemas.office.x2006.digsig,
 com.microsoft.schemas.office.x2006.digsig.impl,
 com.microsoft.schemas.office.x2006.encryption,
 com.microsoft.schemas.office.x2006.encryption.impl,
 com.microsoft.schemas.office.x2006.keyEncryptor.certificate,
 com.microsoft.schemas.office.x2006.keyEncryptor.certificate.impl,
 com.microsoft.schemas.office.x2006.keyEncryptor.password,
 com.microsoft.schemas.office.x2006.keyEncryptor.password.impl,
 com.microsoft.schemas.vml,
 com.microsoft.schemas.vml.impl,
 io.netty.bootstrap,
 io.netty.buffer,
 io.netty.buffer.search,
 io.netty.channel,
 io.netty.channel.embedded,
 io.netty.channel.epoll,
 io.netty.channel.group,
 io.netty.channel.internal,
 io.netty.channel.kqueue,
 io.netty.channel.local,
 io.netty.channel.nio,
 io.netty.channel.oio,
 io.netty.channel.pool,
 io.netty.channel.socket,
 io.netty.channel.socket.nio,
 io.netty.channel.socket.oio,
 io.netty.channel.unix,
 io.netty.handler.address,
 io.netty.handler.codec,
 io.netty.handler.codec.base64,
 io.netty.handler.codec.bytes,
 io.netty.handler.codec.compression,
 io.netty.handler.codec.http,
 io.netty.handler.codec.http.cookie,
 io.netty.handler.codec.http.cors,
 io.netty.handler.codec.http.multipart,
 io.netty.handler.codec.http.websocketx,
 io.netty.handler.codec.http.websocketx.extensions,
 io.netty.handler.codec.http.websocketx.extensions.compression,
 io.netty.handler.codec.json,
 io.netty.handler.codec.marshalling,
 io.netty.handler.codec.protobuf,
 io.netty.handler.codec.rtsp,
 io.netty.handler.codec.serialization,
 io.netty.handler.codec.socks,
 io.netty.handler.codec.socksx,
 io.netty.handler.codec.socksx.v4,
 io.netty.handler.codec.socksx.v5,
 io.netty.handler.codec.spdy,
 io.netty.handler.codec.string,
 io.netty.handler.codec.xml,
 io.netty.handler.flow,
 io.netty.handler.flush,
 io.netty.handler.ipfilter,
 io.netty.handler.logging,
 io.netty.handler.pcap,
 io.netty.handler.proxy,
 io.netty.handler.ssl,
 io.netty.handler.ssl.ocsp,
 io.netty.handler.ssl.util,
 io.netty.handler.stream,
 io.netty.handler.timeout,
 io.netty.handler.traffic,
 io.netty.resolver,
 io.netty.util,
 io.netty.util.collection,
 io.netty.util.concurrent,
 io.netty.util.internal,
 io.netty.util.internal.logging,
 io.netty.util.internal.shaded.org.jctools.queues,
 io.netty.util.internal.shaded.org.jctools.queues.atomic,
 io.netty.util.internal.shaded.org.jctools.util,
 io.netty.util.internal.svm,
 io.undertow,
 io.undertow.attribute,
 io.undertow.channels,
 io.undertow.client,
 io.undertow.client.ajp,
 io.undertow.client.http,
 io.undertow.client.http2,
 io.undertow.conduits,
 io.undertow.connector,
 io.undertow.io,
 io.undertow.predicate,
 io.undertow.protocols.ajp,
 io.undertow.protocols.alpn,
 io.undertow.protocols.http2,
 io.undertow.protocols.ssl,
 io.undertow.security.api,
 io.undertow.security.handlers,
 io.undertow.security.idm,
 io.undertow.security.impl,
 io.undertow.server,
 io.undertow.server.handlers,
 io.undertow.server.handlers.accesslog,
 io.undertow.server.handlers.builder,
 io.undertow.server.handlers.cache,
 io.undertow.server.handlers.encoding,
 io.undertow.server.handlers.error,
 io.undertow.server.handlers.form,
 io.undertow.server.handlers.proxy,
 io.undertow.server.handlers.proxy.mod_cluster,
 io.undertow.server.handlers.resource,
 io.undertow.server.handlers.sse,
 io.undertow.server.protocol,
 io.undertow.server.protocol.ajp,
 io.undertow.server.protocol.framed,
 io.undertow.server.protocol.http,
 io.undertow.server.protocol.http2,
 io.undertow.server.protocol.proxy,
 io.undertow.server.session,
 io.undertow.util,
 io.undertow.websockets,
 io.undertow.websockets.client,
 io.undertow.websockets.core,
 io.undertow.websockets.core.function,
 io.undertow.websockets.core.protocol,
 io.undertow.websockets.core.protocol.version07,
 io.undertow.websockets.core.protocol.version08,
 io.undertow.websockets.core.protocol.version13,
 io.undertow.websockets.extensions,
 io.undertow.websockets.spi,
 javassist,
 javassist.bytecode,
 javassist.bytecode.analysis,
 javassist.bytecode.annotation,
 javassist.bytecode.stackmap,
 javassist.compiler,
 javassist.compiler.ast,
 javassist.convert,
 javassist.expr,
 javassist.runtime,
 javassist.scopedpool,
 javassist.tools,
 javassist.tools.reflect,
 javassist.tools.rmi,
 javassist.tools.web,
 javassist.util,
 javassist.util.proxy,
 javax.ejb,
 javax.ejb.embeddable,
 javax.ejb.spi,
 javax.jms,
 javax.json,
 javax.json.spi,
 javax.json.stream,
 javax.persistence,
 javax.persistence.criteria,
 javax.persistence.metamodel,
 javax.persistence.spi,
 javax.resource,
 javax.resource.cci,
 javax.resource.spi,
 javax.resource.spi.endpoint,
 javax.resource.spi.security,
 javax.resource.spi.work,
 javax.transaction,
 javax.xml.bind,
 javax.xml.bind.annotation,
 javax.xml.bind.annotation.adapters,
 javax.xml.bind.attachment,
 javax.xml.bind.helpers,
 javax.xml.bind.util,
 net.bytebuddy,
 net.bytebuddy.agent.builder,
 net.bytebuddy.asm,
 net.bytebuddy.build,
 net.bytebuddy.description,
 net.bytebuddy.description.annotation,
 net.bytebuddy.description.enumeration,
 net.bytebuddy.description.field,
 net.bytebuddy.description.method,
 net.bytebuddy.description.modifier,
 net.bytebuddy.description.type,
 net.bytebuddy.dynamic,
 net.bytebuddy.dynamic.loading,
 net.bytebuddy.dynamic.scaffold,
 net.bytebuddy.dynamic.scaffold.inline,
 net.bytebuddy.dynamic.scaffold.subclass,
 net.bytebuddy.implementation,
 net.bytebuddy.implementation.attribute,
 net.bytebuddy.implementation.auxiliary,
 net.bytebuddy.implementation.bind,
 net.bytebuddy.implementation.bind.annotation,
 net.bytebuddy.implementation.bytecode,
 net.bytebuddy.implementation.bytecode.assign,
 net.bytebuddy.implementation.bytecode.assign.primitive,
 net.bytebuddy.implementation.bytecode.assign.reference,
 net.bytebuddy.implementation.bytecode.collection,
 net.bytebuddy.implementation.bytecode.constant,
 net.bytebuddy.implementation.bytecode.member,
 net.bytebuddy.jar.asm,
 net.bytebuddy.jar.asm.commons,
 net.bytebuddy.jar.asm.signature,
 net.bytebuddy.matcher,
 net.bytebuddy.pool,
 net.bytebuddy.utility,
 net.bytebuddy.utility.dispatcher,
 net.bytebuddy.utility.privilege,
 net.bytebuddy.utility.visitor,
 org.apache.activemq.artemis,
 org.apache.activemq.artemis.api.config,
 org.apache.activemq.artemis.api.core,
 org.apache.activemq.artemis.api.core.client,
 org.apache.activemq.artemis.api.core.client.loadbalance,
 org.apache.activemq.artemis.api.core.jgroups,
 org.apache.activemq.artemis.api.core.management,
 org.apache.activemq.artemis.api.jms,
 org.apache.activemq.artemis.api.jms.management,
 org.apache.activemq.artemis.core,
 org.apache.activemq.artemis.core.buffers.impl,
 org.apache.activemq.artemis.core.client,
 org.apache.activemq.artemis.core.client.impl,
 org.apache.activemq.artemis.core.cluster,
 org.apache.activemq.artemis.core.config,
 org.apache.activemq.artemis.core.config.federation,
 org.apache.activemq.artemis.core.exception,
 org.apache.activemq.artemis.core.message,
 org.apache.activemq.artemis.core.message.impl,
 org.apache.activemq.artemis.core.message.openmbean,
 org.apache.activemq.artemis.core.persistence,
 org.apache.activemq.artemis.core.protocol,
 org.apache.activemq.artemis.core.protocol.core,
 org.apache.activemq.artemis.core.protocol.core.impl,
 org.apache.activemq.artemis.core.protocol.core.impl.wireformat,
 org.apache.activemq.artemis.core.protocol.hornetq,
 org.apache.activemq.artemis.core.protocol.hornetq.client,
 org.apache.activemq.artemis.core.protocol.hornetq.util,
 org.apache.activemq.artemis.core.remoting,
 org.apache.activemq.artemis.core.remoting.impl,
 org.apache.activemq.artemis.core.remoting.impl.netty,
 org.apache.activemq.artemis.core.remoting.impl.ssl,
 org.apache.activemq.artemis.core.security,
 org.apache.activemq.artemis.core.server,
 org.apache.activemq.artemis.core.server.management,
 org.apache.activemq.artemis.core.settings.impl,
 org.apache.activemq.artemis.core.transaction.impl,
 org.apache.activemq.artemis.core.version,
 org.apache.activemq.artemis.core.version.impl,
 org.apache.activemq.artemis.jms.client,
 org.apache.activemq.artemis.jms.client.compatible1X,
 org.apache.activemq.artemis.jndi,
 org.apache.activemq.artemis.logs,
 org.apache.activemq.artemis.reader,
 org.apache.activemq.artemis.selector.filter,
 org.apache.activemq.artemis.selector.hyphenated,
 org.apache.activemq.artemis.selector.impl,
 org.apache.activemq.artemis.selector.strict,
 org.apache.activemq.artemis.spi.core.protocol,
 org.apache.activemq.artemis.spi.core.remoting,
 org.apache.activemq.artemis.spi.core.remoting.ssl,
 org.apache.activemq.artemis.uri,
 org.apache.activemq.artemis.uri.schema.connector,
 org.apache.activemq.artemis.uri.schema.serverLocator,
 org.apache.activemq.artemis.utils,
 org.apache.activemq.artemis.utils.actors,
 org.apache.activemq.artemis.utils.collections,
 org.apache.activemq.artemis.utils.critical,
 org.apache.activemq.artemis.utils.pools,
 org.apache.activemq.artemis.utils.runnables,
 org.apache.activemq.artemis.utils.uri,
 org.apache.batik,
 org.apache.batik.anim,
 org.apache.batik.anim.dom,
 org.apache.batik.anim.timing,
 org.apache.batik.anim.values,
 org.apache.batik.apps.rasterizer,
 org.apache.batik.apps.slideshow,
 org.apache.batik.apps.svgbrowser,
 org.apache.batik.apps.svgpp,
 org.apache.batik.apps.ttf2svg,
 org.apache.batik.bridge,
 org.apache.batik.bridge.svg12,
 org.apache.batik.constants,
 org.apache.batik.css.dom,
 org.apache.batik.css.engine,
 org.apache.batik.css.engine.sac,
 org.apache.batik.css.engine.value,
 org.apache.batik.css.engine.value.css2,
 org.apache.batik.css.engine.value.svg,
 org.apache.batik.css.engine.value.svg12,
 org.apache.batik.css.parser,
 org.apache.batik.dom,
 org.apache.batik.dom.events,
 org.apache.batik.dom.svg,
 org.apache.batik.dom.svg12,
 org.apache.batik.dom.traversal,
 org.apache.batik.dom.util,
 org.apache.batik.dom.xbl,
 org.apache.batik.ext.awt,
 org.apache.batik.ext.awt.color,
 org.apache.batik.ext.awt.font,
 org.apache.batik.ext.awt.g2d,
 org.apache.batik.ext.awt.geom,
 org.apache.batik.ext.awt.image,
 org.apache.batik.ext.awt.image.codec.imageio,
 org.apache.batik.ext.awt.image.codec.png,
 org.apache.batik.ext.awt.image.codec.util,
 org.apache.batik.ext.awt.image.renderable,
 org.apache.batik.ext.awt.image.rendered,
 org.apache.batik.ext.awt.image.spi,
 org.apache.batik.ext.swing,
 org.apache.batik.extension,
 org.apache.batik.extension.svg,
 org.apache.batik.gvt,
 org.apache.batik.gvt.event,
 org.apache.batik.gvt.filter,
 org.apache.batik.gvt.flow,
 org.apache.batik.gvt.font,
 org.apache.batik.gvt.renderer,
 org.apache.batik.gvt.text,
 org.apache.batik.i18n,
 org.apache.batik.parser,
 org.apache.batik.script,
 org.apache.batik.script.jpython,
 org.apache.batik.script.rhino,
 org.apache.batik.svggen,
 org.apache.batik.svggen.font,
 org.apache.batik.svggen.font.table,
 org.apache.batik.swing,
 org.apache.batik.swing.gvt,
 org.apache.batik.swing.svg,
 org.apache.batik.transcoder,
 org.apache.batik.transcoder.image,
 org.apache.batik.transcoder.image.resources,
 org.apache.batik.transcoder.keys,
 org.apache.batik.transcoder.print,
 org.apache.batik.transcoder.svg2svg,
 org.apache.batik.transcoder.wmf,
 org.apache.batik.transcoder.wmf.tosvg,
 org.apache.batik.util,
 org.apache.batik.util.gui,
 org.apache.batik.util.gui.resource,
 org.apache.batik.util.gui.xmleditor,
 org.apache.batik.util.io,
 org.apache.batik.util.resources,
 org.apache.batik.w3c.dom,
 org.apache.batik.w3c.dom.events,
 org.apache.batik.xml,
 org.apache.commons.beanutils,
 org.apache.commons.beanutils.converters,
 org.apache.commons.beanutils.expression,
 org.apache.commons.beanutils.locale,
 org.apache.commons.beanutils.locale.converters,
 org.apache.commons.collections,
 org.apache.commons.collections.bag,
 org.apache.commons.collections.bidimap,
 org.apache.commons.collections.buffer,
 org.apache.commons.collections.collection,
 org.apache.commons.collections.comparators,
 org.apache.commons.collections.functors,
 org.apache.commons.collections.iterators,
 org.apache.commons.collections.keyvalue,
 org.apache.commons.collections.list,
 org.apache.commons.collections.map,
 org.apache.commons.collections.set,
 org.apache.commons.compress,
 org.apache.commons.compress.archivers,
 org.apache.commons.compress.archivers.ar,
 org.apache.commons.compress.archivers.arj,
 org.apache.commons.compress.archivers.cpio,
 org.apache.commons.compress.archivers.dump,
 org.apache.commons.compress.archivers.examples,
 org.apache.commons.compress.archivers.jar,
 org.apache.commons.compress.archivers.sevenz,
 org.apache.commons.compress.archivers.tar,
 org.apache.commons.compress.archivers.zip,
 org.apache.commons.compress.changes,
 org.apache.commons.compress.compressors,
 org.apache.commons.compress.compressors.brotli,
 org.apache.commons.compress.compressors.bzip2,
 org.apache.commons.compress.compressors.deflate,
 org.apache.commons.compress.compressors.deflate64,
 org.apache.commons.compress.compressors.gzip,
 org.apache.commons.compress.compressors.lz4,
 org.apache.commons.compress.compressors.lz77support,
 org.apache.commons.compress.compressors.lzma,
 org.apache.commons.compress.compressors.lzw,
 org.apache.commons.compress.compressors.pack200,
 org.apache.commons.compress.compressors.snappy,
 org.apache.commons.compress.compressors.xz,
 org.apache.commons.compress.compressors.z,
 org.apache.commons.compress.compressors.zstandard,
 org.apache.commons.compress.parallel,
 org.apache.commons.compress.utils,
 org.apache.commons.io,
 org.apache.commons.io.comparator,
 org.apache.commons.io.file,
 org.apache.commons.io.file.spi,
 org.apache.commons.io.filefilter,
 org.apache.commons.io.function,
 org.apache.commons.io.input,
 org.apache.commons.io.input.buffer,
 org.apache.commons.io.monitor,
 org.apache.commons.io.output,
 org.apache.commons.io.serialization,
 org.apache.commons.logging,
 org.apache.commons.logging.impl,
 org.apache.commons.net,
 org.apache.commons.net.bsd,
 org.apache.commons.net.chargen,
 org.apache.commons.net.daytime,
 org.apache.commons.net.discard,
 org.apache.commons.net.echo,
 org.apache.commons.net.finger,
 org.apache.commons.net.ftp,
 org.apache.commons.net.ftp.parser,
 org.apache.commons.net.imap,
 org.apache.commons.net.io,
 org.apache.commons.net.nntp,
 org.apache.commons.net.ntp,
 org.apache.commons.net.pop3,
 org.apache.commons.net.smtp,
 org.apache.commons.net.telnet,
 org.apache.commons.net.tftp,
 org.apache.commons.net.time,
 org.apache.commons.net.util,
 org.apache.commons.net.whois,
 org.apache.commons.pool2,
 org.apache.commons.pool2.impl,
 org.apache.commons.pool2.proxy,
 org.apache.commons.vfs2,
 org.apache.commons.vfs2.auth,
 org.apache.commons.vfs2.cache,
 org.apache.commons.vfs2.events,
 org.apache.commons.vfs2.filter,
 org.apache.commons.vfs2.function,
 org.apache.commons.vfs2.impl,
 org.apache.commons.vfs2.operations,
 org.apache.commons.vfs2.operations.vcs,
 org.apache.commons.vfs2.provider,
 org.apache.commons.vfs2.provider.bzip2,
 org.apache.commons.vfs2.provider.compressed,
 org.apache.commons.vfs2.provider.ftp,
 org.apache.commons.vfs2.provider.ftps,
 org.apache.commons.vfs2.provider.gzip,
 org.apache.commons.vfs2.provider.hdfs,
 org.apache.commons.vfs2.provider.http,
 org.apache.commons.vfs2.provider.http4,
 org.apache.commons.vfs2.provider.http4s,
 org.apache.commons.vfs2.provider.http5,
 org.apache.commons.vfs2.provider.http5s,
 org.apache.commons.vfs2.provider.https,
 org.apache.commons.vfs2.provider.jar,
 org.apache.commons.vfs2.provider.local,
 org.apache.commons.vfs2.provider.ram,
 org.apache.commons.vfs2.provider.res,
 org.apache.commons.vfs2.provider.sftp,
 org.apache.commons.vfs2.provider.tar,
 org.apache.commons.vfs2.provider.temp,
 org.apache.commons.vfs2.provider.url,
 org.apache.commons.vfs2.provider.zip,
 org.apache.commons.vfs2.tasks,
 org.apache.commons.vfs2.util,
 org.apache.poi,
 org.apache.poi.common.usermodel,
 org.apache.poi.common.usermodel.fonts,
 org.apache.poi.ddf,
 org.apache.poi.extractor,
 org.apache.poi.hpsf,
 org.apache.poi.hpsf.extractor,
 org.apache.poi.hpsf.wellknown,
 org.apache.poi.hssf,
 org.apache.poi.hssf.dev,
 org.apache.poi.hssf.eventmodel,
 org.apache.poi.hssf.eventusermodel,
 org.apache.poi.hssf.eventusermodel.dummyrecord,
 org.apache.poi.hssf.extractor,
 org.apache.poi.hssf.model,
 org.apache.poi.hssf.record,
 org.apache.poi.hssf.record.aggregates,
 org.apache.poi.hssf.record.cf,
 org.apache.poi.hssf.record.chart,
 org.apache.poi.hssf.record.common,
 org.apache.poi.hssf.record.cont,
 org.apache.poi.hssf.record.crypto,
 org.apache.poi.hssf.record.pivottable,
 org.apache.poi.hssf.usermodel,
 org.apache.poi.hssf.usermodel.helpers,
 org.apache.poi.hssf.util,
 org.apache.poi.ooxml,
 org.apache.poi.ooxml.extractor,
 org.apache.poi.ooxml.util,
 org.apache.poi.openxml4j.exceptions,
 org.apache.poi.openxml4j.opc,
 org.apache.poi.openxml4j.opc.internal,
 org.apache.poi.openxml4j.opc.internal.marshallers,
 org.apache.poi.openxml4j.opc.internal.unmarshallers,
 org.apache.poi.openxml4j.util,
 org.apache.poi.poifs,
 org.apache.poi.poifs.common,
 org.apache.poi.poifs.crypt,
 org.apache.poi.poifs.crypt.agile,
 org.apache.poi.poifs.crypt.binaryrc4,
 org.apache.poi.poifs.crypt.cryptoapi,
 org.apache.poi.poifs.crypt.dsig,
 org.apache.poi.poifs.crypt.dsig.facets,
 org.apache.poi.poifs.crypt.dsig.services,
 org.apache.poi.poifs.crypt.standard,
 org.apache.poi.poifs.crypt.temp,
 org.apache.poi.poifs.crypt.xor,
 org.apache.poi.poifs.dev,
 org.apache.poi.poifs.eventfilesystem,
 org.apache.poi.poifs.filesystem,
 org.apache.poi.poifs.macros,
 org.apache.poi.poifs.nio,
 org.apache.poi.poifs.property,
 org.apache.poi.poifs.storage,
 org.apache.poi.sl.draw,
 org.apache.poi.sl.draw.binding,
 org.apache.poi.sl.draw.geom,
 org.apache.poi.sl.extractor,
 org.apache.poi.sl.image,
 org.apache.poi.sl.usermodel,
 org.apache.poi.ss,
 org.apache.poi.ss.extractor,
 org.apache.poi.ss.format,
 org.apache.poi.ss.formula,
 org.apache.poi.ss.formula.atp,
 org.apache.poi.ss.formula.constant,
 org.apache.poi.ss.formula.eval,
 org.apache.poi.ss.formula.eval.forked,
 org.apache.poi.ss.formula.function,
 org.apache.poi.ss.formula.functions,
 org.apache.poi.ss.formula.ptg,
 org.apache.poi.ss.formula.udf,
 org.apache.poi.ss.usermodel,
 org.apache.poi.ss.usermodel.charts,
 org.apache.poi.ss.usermodel.helpers,
 org.apache.poi.ss.util,
 org.apache.poi.ss.util.cellwalk,
 org.apache.poi.util,
 org.apache.poi.wp.usermodel,
 org.apache.poi.xddf.usermodel,
 org.apache.poi.xddf.usermodel.chart,
 org.apache.poi.xddf.usermodel.text,
 org.apache.poi.xdgf.exceptions,
 org.apache.poi.xdgf.extractor,
 org.apache.poi.xdgf.geom,
 org.apache.poi.xdgf.usermodel,
 org.apache.poi.xdgf.usermodel.section,
 org.apache.poi.xdgf.usermodel.section.geometry,
 org.apache.poi.xdgf.usermodel.shape,
 org.apache.poi.xdgf.usermodel.shape.exceptions,
 org.apache.poi.xdgf.util,
 org.apache.poi.xdgf.xml,
 org.apache.poi.xslf.extractor,
 org.apache.poi.xslf.model,
 org.apache.poi.xslf.usermodel,
 org.apache.poi.xslf.util,
 org.apache.poi.xssf,
 org.apache.poi.xssf.binary,
 org.apache.poi.xssf.eventusermodel,
 org.apache.poi.xssf.extractor,
 org.apache.poi.xssf.model,
 org.apache.poi.xssf.streaming,
 org.apache.poi.xssf.usermodel,
 org.apache.poi.xssf.usermodel.charts,
 org.apache.poi.xssf.usermodel.extensions,
 org.apache.poi.xssf.usermodel.helpers,
 org.apache.poi.xssf.util,
 org.apache.poi.xwpf.extractor,
 org.apache.poi.xwpf.model,
 org.apache.poi.xwpf.usermodel,
 org.apache.sshd.client.auth,
 org.apache.sshd.client.auth.hostbased,
 org.apache.sshd.client.auth.password,
 org.apache.sshd.client.auth.pubkey,
 org.apache.sshd.client.config.hosts,
 org.apache.sshd.client.config.keys,
 org.apache.sshd.common,
 org.apache.sshd.common.auth,
 org.apache.sshd.common.channel,
 org.apache.sshd.common.cipher,
 org.apache.sshd.common.compression,
 org.apache.sshd.common.config,
 org.apache.sshd.common.config.keys,
 org.apache.sshd.common.config.keys.impl,
 org.apache.sshd.common.config.keys.loader,
 org.apache.sshd.common.config.keys.loader.openssh,
 org.apache.sshd.common.config.keys.loader.openssh.kdf,
 org.apache.sshd.common.config.keys.loader.pem,
 org.apache.sshd.common.config.keys.loader.ssh2,
 org.apache.sshd.common.config.keys.u2f,
 org.apache.sshd.common.config.keys.writer,
 org.apache.sshd.common.config.keys.writer.openssh,
 org.apache.sshd.common.digest,
 org.apache.sshd.common.file,
 org.apache.sshd.common.file.nativefs,
 org.apache.sshd.common.file.nonefs,
 org.apache.sshd.common.file.root,
 org.apache.sshd.common.file.util,
 org.apache.sshd.common.file.virtualfs,
 org.apache.sshd.common.future,
 org.apache.sshd.common.io,
 org.apache.sshd.common.kex,
 org.apache.sshd.common.kex.extension,
 org.apache.sshd.common.kex.extension.parser,
 org.apache.sshd.common.keyprovider,
 org.apache.sshd.common.mac,
 org.apache.sshd.common.random,
 org.apache.sshd.common.session,
 org.apache.sshd.common.session.helpers,
 org.apache.sshd.common.signature,
 org.apache.sshd.common.util,
 org.apache.sshd.common.util.buffer,
 org.apache.sshd.common.util.buffer.keys,
 org.apache.sshd.common.util.closeable,
 org.apache.sshd.common.util.functors,
 org.apache.sshd.common.util.helper,
 org.apache.sshd.common.util.io,
 org.apache.sshd.common.util.io.der,
 org.apache.sshd.common.util.io.functors,
 org.apache.sshd.common.util.io.resource,
 org.apache.sshd.common.util.logging,
 org.apache.sshd.common.util.net,
 org.apache.sshd.common.util.security,
 org.apache.sshd.common.util.security.bouncycastle,
 org.apache.sshd.common.util.security.eddsa,
 org.apache.sshd.common.util.threads,
 org.apache.sshd.server,
 org.apache.sshd.server.keyprovider,
 org.apache.sshd.server.shell,
 org.apache.velocity,
 org.apache.velocity.app,
 org.apache.velocity.app.event,
 org.apache.velocity.app.event.implement,
 org.apache.velocity.context,
 org.apache.velocity.exception,
 org.apache.velocity.io,
 org.apache.velocity.runtime,
 org.apache.velocity.runtime.directive,
 org.apache.velocity.runtime.directive.contrib,
 org.apache.velocity.runtime.parser,
 org.apache.velocity.runtime.parser.node,
 org.apache.velocity.runtime.resource,
 org.apache.velocity.runtime.resource.loader,
 org.apache.velocity.runtime.resource.util,
 org.apache.velocity.runtime.visitor,
 org.apache.velocity.shaded.commons.io,
 org.apache.velocity.util,
 org.apache.velocity.util.introspection,
 org.apache.xmlbeans,
 org.apache.xmlbeans.impl.common,
 org.apache.xmlbeans.impl.config,
 org.apache.xmlbeans.impl.inst2xsd,
 org.apache.xmlbeans.impl.inst2xsd.util,
 org.apache.xmlbeans.impl.jam,
 org.apache.xmlbeans.impl.jam.annotation,
 org.apache.xmlbeans.impl.jam.internal,
 org.apache.xmlbeans.impl.jam.internal.classrefs,
 org.apache.xmlbeans.impl.jam.internal.elements,
 org.apache.xmlbeans.impl.jam.internal.javadoc,
 org.apache.xmlbeans.impl.jam.internal.parser,
 org.apache.xmlbeans.impl.jam.internal.reflect,
 org.apache.xmlbeans.impl.jam.mutable,
 org.apache.xmlbeans.impl.jam.provider,
 org.apache.xmlbeans.impl.jam.visitor,
 org.apache.xmlbeans.impl.jam.xml,
 org.apache.xmlbeans.impl.regex,
 org.apache.xmlbeans.impl.richParser,
 org.apache.xmlbeans.impl.schema,
 org.apache.xmlbeans.impl.soap,
 org.apache.xmlbeans.impl.store,
 org.apache.xmlbeans.impl.tool,
 org.apache.xmlbeans.impl.util,
 org.apache.xmlbeans.impl.validator,
 org.apache.xmlbeans.impl.values,
 org.apache.xmlbeans.impl.xb.ltgfmt,
 org.apache.xmlbeans.impl.xb.ltgfmt.impl,
 org.apache.xmlbeans.impl.xb.substwsdl,
 org.apache.xmlbeans.impl.xb.substwsdl.impl,
 org.apache.xmlbeans.impl.xb.xmlconfig,
 org.apache.xmlbeans.impl.xb.xmlconfig.impl,
 org.apache.xmlbeans.impl.xb.xmlschema,
 org.apache.xmlbeans.impl.xb.xmlschema.impl,
 org.apache.xmlbeans.impl.xb.xsdownload,
 org.apache.xmlbeans.impl.xb.xsdownload.impl,
 org.apache.xmlbeans.impl.xb.xsdschema,
 org.apache.xmlbeans.impl.xb.xsdschema.impl,
 org.apache.xmlbeans.impl.xpathgen,
 org.apache.xmlbeans.impl.xsd2inst,
 org.apache.xmlbeans.soap,
 org.apache.xmlbeans.xml.stream,
 org.apache.xmlbeans.xml.stream.events,
 org.apache.xmlbeans.xml.stream.utils,
 org.etsi.uri.x01903.v13,
 org.etsi.uri.x01903.v13.impl,
 org.glassfish.json,
 org.glassfish.json.api,
 org.hibernate,
 org.hibernate.action.internal,
 org.hibernate.action.spi,
 org.hibernate.annotations,
 org.hibernate.boot,
 org.hibernate.boot.archive.internal,
 org.hibernate.boot.archive.scan.internal,
 org.hibernate.boot.archive.scan.spi,
 org.hibernate.boot.archive.spi,
 org.hibernate.boot.cfgxml.internal,
 org.hibernate.boot.cfgxml.spi,
 org.hibernate.boot.internal,
 org.hibernate.boot.jaxb,
 org.hibernate.boot.jaxb.cfg.spi,
 org.hibernate.boot.jaxb.hbm.internal,
 org.hibernate.boot.jaxb.hbm.spi,
 org.hibernate.boot.jaxb.internal,
 org.hibernate.boot.jaxb.internal.stax,
 org.hibernate.boot.jaxb.spi,
 org.hibernate.boot.model,
 org.hibernate.boot.model.convert.internal,
 org.hibernate.boot.model.convert.spi,
 org.hibernate.boot.model.naming,
 org.hibernate.boot.model.process.internal,
 org.hibernate.boot.model.process.spi,
 org.hibernate.boot.model.relational,
 org.hibernate.boot.model.source.internal,
 org.hibernate.boot.model.source.internal.annotations,
 org.hibernate.boot.model.source.internal.hbm,
 org.hibernate.boot.model.source.spi,
 org.hibernate.boot.registry,
 org.hibernate.boot.registry.classloading.internal,
 org.hibernate.boot.registry.classloading.spi,
 org.hibernate.boot.registry.internal,
 org.hibernate.boot.registry.selector,
 org.hibernate.boot.registry.selector.internal,
 org.hibernate.boot.registry.selector.spi,
 org.hibernate.boot.spi,
 org.hibernate.boot.xsd,
 org.hibernate.bytecode,
 org.hibernate.bytecode.enhance.internal.bytebuddy,
 org.hibernate.bytecode.enhance.internal.javassist,
 org.hibernate.bytecode.enhance.internal.tracker,
 org.hibernate.bytecode.enhance.spi,
 org.hibernate.bytecode.enhance.spi.interceptor,
 org.hibernate.bytecode.internal.bytebuddy,
 org.hibernate.bytecode.internal.javassist,
 org.hibernate.bytecode.spi,
 org.hibernate.cache,
 org.hibernate.cache.cfg.internal,
 org.hibernate.cache.cfg.spi,
 org.hibernate.cache.internal,
 org.hibernate.cache.spi,
 org.hibernate.cache.spi.access,
 org.hibernate.cache.spi.entry,
 org.hibernate.cache.spi.support,
 org.hibernate.cfg,
 org.hibernate.cfg.annotations,
 org.hibernate.cfg.annotations.reflection,
 org.hibernate.cfg.beanvalidation,
 org.hibernate.classic,
 org.hibernate.collection.internal,
 org.hibernate.collection.spi,
 org.hibernate.context,
 org.hibernate.context.internal,
 org.hibernate.context.spi,
 org.hibernate.criterion,
 org.hibernate.dialect,
 org.hibernate.dialect.function,
 org.hibernate.dialect.hint,
 org.hibernate.dialect.identity,
 org.hibernate.dialect.lock,
 org.hibernate.dialect.pagination,
 org.hibernate.dialect.unique,
 org.hibernate.ejb,
 org.hibernate.engine,
 org.hibernate.engine.config.internal,
 org.hibernate.engine.config.spi,
 org.hibernate.engine.internal,
 org.hibernate.engine.jdbc,
 org.hibernate.engine.jdbc.batch.internal,
 org.hibernate.engine.jdbc.batch.spi,
 org.hibernate.engine.jdbc.connections.internal,
 org.hibernate.engine.jdbc.connections.spi,
 org.hibernate.engine.jdbc.cursor.internal,
 org.hibernate.engine.jdbc.cursor.spi,
 org.hibernate.engine.jdbc.dialect.internal,
 org.hibernate.engine.jdbc.dialect.spi,
 org.hibernate.engine.jdbc.env.internal,
 org.hibernate.engine.jdbc.env.spi,
 org.hibernate.engine.jdbc.internal,
 org.hibernate.engine.jdbc.spi,
 org.hibernate.engine.jndi,
 org.hibernate.engine.jndi.internal,
 org.hibernate.engine.jndi.spi,
 org.hibernate.engine.loading.internal,
 org.hibernate.engine.profile,
 org.hibernate.engine.query,
 org.hibernate.engine.query.internal,
 org.hibernate.engine.query.spi,
 org.hibernate.engine.query.spi.sql,
 org.hibernate.engine.spi,
 org.hibernate.engine.transaction.internal,
 org.hibernate.engine.transaction.internal.jta,
 org.hibernate.engine.transaction.jta.platform.internal,
 org.hibernate.engine.transaction.jta.platform.spi,
 org.hibernate.engine.transaction.spi,
 org.hibernate.event.internal,
 org.hibernate.event.service.internal,
 org.hibernate.event.service.spi,
 org.hibernate.event.spi,
 org.hibernate.exception,
 org.hibernate.exception.internal,
 org.hibernate.exception.spi,
 org.hibernate.graph.spi,
 org.hibernate.hql.internal,
 org.hibernate.hql.internal.antlr,
 org.hibernate.hql.internal.ast,
 org.hibernate.hql.internal.ast.exec,
 org.hibernate.hql.internal.ast.tree,
 org.hibernate.hql.internal.ast.util,
 org.hibernate.hql.internal.classic,
 org.hibernate.hql.spi,
 org.hibernate.hql.spi.id,
 org.hibernate.hql.spi.id.cte,
 org.hibernate.hql.spi.id.global,
 org.hibernate.hql.spi.id.inline,
 org.hibernate.hql.spi.id.local,
 org.hibernate.hql.spi.id.persistent,
 org.hibernate.id,
 org.hibernate.id.enhanced,
 org.hibernate.id.factory,
 org.hibernate.id.factory.internal,
 org.hibernate.id.factory.spi,
 org.hibernate.id.insert,
 org.hibernate.id.uuid,
 org.hibernate.integrator.internal,
 org.hibernate.integrator.spi,
 org.hibernate.internal,
 org.hibernate.internal.build,
 org.hibernate.internal.log,
 org.hibernate.internal.util,
 org.hibernate.internal.util.beans,
 org.hibernate.internal.util.collections,
 org.hibernate.internal.util.compare,
 org.hibernate.internal.util.config,
 org.hibernate.internal.util.io,
 org.hibernate.internal.util.jndi,
 org.hibernate.internal.util.type,
 org.hibernate.internal.util.xml,
 org.hibernate.jdbc,
 org.hibernate.jmx.internal,
 org.hibernate.jmx.spi,
 org.hibernate.jpa,
 org.hibernate.jpa.boot.internal,
 org.hibernate.jpa.boot.spi,
 org.hibernate.jpa.event.internal,
 org.hibernate.jpa.event.spi,
 org.hibernate.jpa.event.spi.jpa,
 org.hibernate.jpa.graph.internal,
 org.hibernate.jpa.internal,
 org.hibernate.jpa.internal.enhance,
 org.hibernate.jpa.internal.util,
 org.hibernate.jpa.spi,
 org.hibernate.loader,
 org.hibernate.loader.collection,
 org.hibernate.loader.collection.plan,
 org.hibernate.loader.criteria,
 org.hibernate.loader.custom,
 org.hibernate.loader.custom.sql,
 org.hibernate.loader.entity,
 org.hibernate.loader.entity.plan,
 org.hibernate.loader.hql,
 org.hibernate.loader.internal,
 org.hibernate.loader.plan.build.internal,
 org.hibernate.loader.plan.build.internal.returns,
 org.hibernate.loader.plan.build.internal.spaces,
 org.hibernate.loader.plan.build.spi,
 org.hibernate.loader.plan.exec.internal,
 org.hibernate.loader.plan.exec.process.internal,
 org.hibernate.loader.plan.exec.process.spi,
 org.hibernate.loader.plan.exec.query.internal,
 org.hibernate.loader.plan.exec.query.spi,
 org.hibernate.loader.plan.exec.spi,
 org.hibernate.loader.plan.spi,
 org.hibernate.loader.spi,
 org.hibernate.lob,
 org.hibernate.mapping,
 org.hibernate.metadata,
 org.hibernate.metamodel.internal,
 org.hibernate.metamodel.model.convert.internal,
 org.hibernate.metamodel.model.convert.spi,
 org.hibernate.metamodel.model.domain,
 org.hibernate.metamodel.spi,
 org.hibernate.param,
 org.hibernate.persister.collection,
 org.hibernate.persister.entity,
 org.hibernate.persister.internal,
 org.hibernate.persister.spi,
 org.hibernate.persister.walking.internal,
 org.hibernate.persister.walking.spi,
 org.hibernate.pretty,
 org.hibernate.procedure,
 org.hibernate.procedure.internal,
 org.hibernate.procedure.spi,
 org.hibernate.property.access.internal,
 org.hibernate.property.access.spi,
 org.hibernate.proxy,
 org.hibernate.proxy.map,
 org.hibernate.proxy.pojo,
 org.hibernate.proxy.pojo.bytebuddy,
 org.hibernate.proxy.pojo.javassist,
 org.hibernate.query,
 org.hibernate.query.criteria,
 org.hibernate.query.criteria.internal,
 org.hibernate.query.criteria.internal.compile,
 org.hibernate.query.criteria.internal.expression,
 org.hibernate.query.criteria.internal.expression.function,
 org.hibernate.query.criteria.internal.path,
 org.hibernate.query.criteria.internal.predicate,
 org.hibernate.query.internal,
 org.hibernate.query.procedure,
 org.hibernate.query.procedure.internal,
 org.hibernate.query.procedure.spi,
 org.hibernate.query.spi,
 org.hibernate.resource.beans.container.internal,
 org.hibernate.resource.beans.container.spi,
 org.hibernate.resource.beans.internal,
 org.hibernate.resource.beans.spi,
 org.hibernate.resource.jdbc,
 org.hibernate.resource.jdbc.internal,
 org.hibernate.resource.jdbc.spi,
 org.hibernate.resource.transaction,
 org.hibernate.resource.transaction.backend.jdbc.internal,
 org.hibernate.resource.transaction.backend.jdbc.spi,
 org.hibernate.resource.transaction.backend.jta.internal,
 org.hibernate.resource.transaction.backend.jta.internal.synchronization,
 org.hibernate.resource.transaction.internal,
 org.hibernate.resource.transaction.spi,
 org.hibernate.result,
 org.hibernate.result.internal,
 org.hibernate.result.spi,
 org.hibernate.secure.internal,
 org.hibernate.secure.spi,
 org.hibernate.service,
 org.hibernate.service.internal,
 org.hibernate.service.spi,
 org.hibernate.sql,
 org.hibernate.sql.ordering.antlr,
 org.hibernate.stat,
 org.hibernate.stat.internal,
 org.hibernate.stat.spi,
 org.hibernate.tool.enhance,
 org.hibernate.tool.hbm2ddl,
 org.hibernate.tool.instrument.javassist,
 org.hibernate.tool.schema,
 org.hibernate.tool.schema.extract.internal,
 org.hibernate.tool.schema.extract.spi,
 org.hibernate.tool.schema.internal,
 org.hibernate.tool.schema.internal.exec,
 org.hibernate.tool.schema.spi,
 org.hibernate.transform,
 org.hibernate.tuple,
 org.hibernate.tuple.component,
 org.hibernate.tuple.entity,
 org.hibernate.type,
 org.hibernate.type.descriptor,
 org.hibernate.type.descriptor.converter,
 org.hibernate.type.descriptor.java,
 org.hibernate.type.descriptor.java.spi,
 org.hibernate.type.descriptor.spi,
 org.hibernate.type.descriptor.sql,
 org.hibernate.type.descriptor.sql.spi,
 org.hibernate.type.internal,
 org.hibernate.type.spi,
 org.hibernate.usertype,
 org.infinispan,
 org.infinispan.affinity,
 org.infinispan.affinity.impl,
 org.infinispan.batch,
 org.infinispan.cache.impl,
 org.infinispan.commands,
 org.infinispan.commands.control,
 org.infinispan.commands.functional,
 org.infinispan.commands.functional.functions,
 org.infinispan.commands.irac,
 org.infinispan.commands.module,
 org.infinispan.commands.read,
 org.infinispan.commands.remote,
 org.infinispan.commands.remote.recovery,
 org.infinispan.commands.statetransfer,
 org.infinispan.commands.topology,
 org.infinispan.commands.triangle,
 org.infinispan.commands.tx,
 org.infinispan.commands.write,
 org.infinispan.commons,
 org.infinispan.commons.api,
 org.infinispan.commons.configuration,
 org.infinispan.commons.configuration.attributes,
 org.infinispan.commons.configuration.io,
 org.infinispan.commons.configuration.io.json,
 org.infinispan.commons.configuration.io.xml,
 org.infinispan.commons.configuration.io.yaml,
 org.infinispan.commons.dataconversion,
 org.infinispan.commons.dataconversion.internal,
 org.infinispan.commons.executors,
 org.infinispan.commons.hash,
 org.infinispan.commons.internal,
 org.infinispan.commons.io,
 org.infinispan.commons.jdkspecific,
 org.infinispan.commons.jmx,
 org.infinispan.commons.logging,
 org.infinispan.commons.marshall,
 org.infinispan.commons.marshall.exts,
 org.infinispan.commons.persistence,
 org.infinispan.commons.reactive,
 org.infinispan.commons.stat,
 org.infinispan.commons.time,
 org.infinispan.commons.tx,
 org.infinispan.commons.tx.lookup,
 org.infinispan.commons.util,
 org.infinispan.commons.util.concurrent,
 org.infinispan.commons.util.logging,
 org.infinispan.configuration,
 org.infinispan.configuration.cache,
 org.infinispan.configuration.format,
 org.infinispan.configuration.global,
 org.infinispan.configuration.internal,
 org.infinispan.configuration.parsing,
 org.infinispan.configuration.serializing,
 org.infinispan.conflict,
 org.infinispan.conflict.impl,
 org.infinispan.container,
 org.infinispan.container.entries,
 org.infinispan.container.entries.metadata,
 org.infinispan.container.entries.versioned,
 org.infinispan.container.impl,
 org.infinispan.container.offheap,
 org.infinispan.container.versioning,
 org.infinispan.container.versioning.irac,
 org.infinispan.context,
 org.infinispan.context.impl,
 org.infinispan.counter.api,
 org.infinispan.counter.exception,
 org.infinispan.counter.util,
 org.infinispan.distribution,
 org.infinispan.distribution.ch,
 org.infinispan.distribution.ch.impl,
 org.infinispan.distribution.group,
 org.infinispan.distribution.group.impl,
 org.infinispan.distribution.impl,
 org.infinispan.distribution.topologyaware,
 org.infinispan.distribution.util,
 org.infinispan.encoding,
 org.infinispan.encoding.impl,
 org.infinispan.eviction,
 org.infinispan.eviction.impl,
 org.infinispan.executors,
 org.infinispan.expiration,
 org.infinispan.expiration.impl,
 org.infinispan.factories,
 org.infinispan.factories.impl,
 org.infinispan.factories.threads,
 org.infinispan.filter,
 org.infinispan.functional,
 org.infinispan.functional.impl,
 org.infinispan.globalstate,
 org.infinispan.globalstate.impl,
 org.infinispan.health,
 org.infinispan.health.impl,
 org.infinispan.health.impl.jmx,
 org.infinispan.health.jmx,
 org.infinispan.interceptors,
 org.infinispan.interceptors.distribution,
 org.infinispan.interceptors.impl,
 org.infinispan.interceptors.locking,
 org.infinispan.interceptors.xsite,
 org.infinispan.io,
 org.infinispan.jmx,
 org.infinispan.lifecycle,
 org.infinispan.lock.api,
 org.infinispan.lock.exception,
 org.infinispan.manager,
 org.infinispan.manager.impl,
 org.infinispan.marshall.core,
 org.infinispan.marshall.core.impl,
 org.infinispan.marshall.exts,
 org.infinispan.marshall.persistence,
 org.infinispan.marshall.persistence.impl,
 org.infinispan.marshall.protostream.impl,
 org.infinispan.metadata,
 org.infinispan.metadata.impl,
 org.infinispan.metrics,
 org.infinispan.metrics.impl,
 org.infinispan.multimap.api,
 org.infinispan.notifications,
 org.infinispan.notifications.cachelistener,
 org.infinispan.notifications.cachelistener.annotation,
 org.infinispan.notifications.cachelistener.cluster,
 org.infinispan.notifications.cachelistener.cluster.impl,
 org.infinispan.notifications.cachelistener.event,
 org.infinispan.notifications.cachelistener.event.impl,
 org.infinispan.notifications.cachelistener.filter,
 org.infinispan.notifications.cachemanagerlistener,
 org.infinispan.notifications.cachemanagerlistener.annotation,
 org.infinispan.notifications.cachemanagerlistener.event,
 org.infinispan.notifications.cachemanagerlistener.event.impl,
 org.infinispan.notifications.impl,
 org.infinispan.partitionhandling,
 org.infinispan.partitionhandling.impl,
 org.infinispan.persistence,
 org.infinispan.persistence.async,
 org.infinispan.persistence.cluster,
 org.infinispan.persistence.file,
 org.infinispan.persistence.internal,
 org.infinispan.persistence.keymappers,
 org.infinispan.persistence.manager,
 org.infinispan.persistence.modifications,
 org.infinispan.persistence.sifs,
 org.infinispan.persistence.sifs.configuration,
 org.infinispan.persistence.sifs.pmem,
 org.infinispan.persistence.spi,
 org.infinispan.persistence.support,
 org.infinispan.persistence.util,
 org.infinispan.reactive,
 org.infinispan.reactive.publisher,
 org.infinispan.reactive.publisher.impl,
 org.infinispan.reactive.publisher.impl.commands.batch,
 org.infinispan.reactive.publisher.impl.commands.reduction,
 org.infinispan.registry,
 org.infinispan.registry.impl,
 org.infinispan.remoting,
 org.infinispan.remoting.inboundhandler,
 org.infinispan.remoting.inboundhandler.action,
 org.infinispan.remoting.responses,
 org.infinispan.remoting.rpc,
 org.infinispan.remoting.transport,
 org.infinispan.remoting.transport.impl,
 org.infinispan.remoting.transport.jgroups,
 org.infinispan.scattered,
 org.infinispan.scattered.impl,
 org.infinispan.security,
 org.infinispan.security.actions,
 org.infinispan.security.audit,
 org.infinispan.security.impl,
 org.infinispan.security.mappers,
 org.infinispan.statetransfer,
 org.infinispan.stats,
 org.infinispan.stats.impl,
 org.infinispan.stream,
 org.infinispan.stream.impl,
 org.infinispan.stream.impl.interceptor,
 org.infinispan.stream.impl.intops,
 org.infinispan.stream.impl.intops.object,
 org.infinispan.stream.impl.intops.primitive.d,
 org.infinispan.stream.impl.intops.primitive.i,
 org.infinispan.stream.impl.intops.primitive.l,
 org.infinispan.stream.impl.local,
 org.infinispan.stream.impl.spliterators,
 org.infinispan.topology,
 org.infinispan.transaction,
 org.infinispan.transaction.impl,
 org.infinispan.transaction.lookup,
 org.infinispan.transaction.synchronization,
 org.infinispan.transaction.tm,
 org.infinispan.transaction.xa,
 org.infinispan.transaction.xa.recovery,
 org.infinispan.upgrade,
 org.infinispan.util,
 org.infinispan.util.concurrent,
 org.infinispan.util.concurrent.locks,
 org.infinispan.util.concurrent.locks.impl,
 org.infinispan.util.function,
 org.infinispan.util.logging,
 org.infinispan.util.logging.events,
 org.infinispan.util.logging.events.impl,
 org.infinispan.util.stream,
 org.infinispan.xsite,
 org.infinispan.xsite.commands,
 org.infinispan.xsite.irac,
 org.infinispan.xsite.metrics,
 org.infinispan.xsite.notification,
 org.infinispan.xsite.response,
 org.infinispan.xsite.spi,
 org.infinispan.xsite.statetransfer,
 org.infinispan.xsite.status,
 org.jboss.ejb._private,
 org.jboss.ejb.client,
 org.jboss.ejb.client.annotation,
 org.jboss.ejb.client.legacy,
 org.jboss.ejb.client.naming.ejb,
 org.jboss.ejb.protocol.remote,
 org.jboss.ejb.server,
 org.jboss.logging,
 org.jboss.marshalling,
 org.jboss.marshalling._private,
 org.jboss.marshalling.cloner,
 org.jboss.marshalling.reflect,
 org.jboss.marshalling.river,
 org.jboss.marshalling.util,
 org.jboss.naming.remote.client,
 org.jboss.remoting3,
 org.jboss.remoting3._private,
 org.jboss.remoting3.remote,
 org.jboss.remoting3.security,
 org.jboss.remoting3.spi,
 org.jboss.remoting3.util,
 org.jboss.remotingjmx,
 org.jboss.remotingjmx.protocol,
 org.jboss.remotingjmx.protocol.v1,
 org.jboss.remotingjmx.protocol.v2,
 org.jboss.threads,
 org.jboss.threads.management,
 org.jgroups,
 org.jgroups.annotations,
 org.jgroups.auth,
 org.jgroups.auth.sasl,
 org.jgroups.blocks,
 org.jgroups.blocks.atomic,
 org.jgroups.blocks.cs,
 org.jgroups.blocks.executor,
 org.jgroups.blocks.locking,
 org.jgroups.client,
 org.jgroups.conf,
 org.jgroups.demos,
 org.jgroups.demos.applets,
 org.jgroups.demos.wb,
 org.jgroups.fork,
 org.jgroups.jmx,
 org.jgroups.logging,
 org.jgroups.nio,
 org.jgroups.protocols,
 org.jgroups.protocols.dns,
 org.jgroups.protocols.pbcast,
 org.jgroups.protocols.relay,
 org.jgroups.protocols.relay.config,
 org.jgroups.protocols.rules,
 org.jgroups.protocols.tom,
 org.jgroups.stack,
 org.jgroups.tests,
 org.jgroups.tests.perf,
 org.jgroups.tests.rt,
 org.jgroups.tests.rt.transports,
 org.jgroups.util,
 org.openxmlformats.schemas.drawingml.x2006.chart,
 org.openxmlformats.schemas.drawingml.x2006.chart.impl,
 org.openxmlformats.schemas.drawingml.x2006.main,
 org.openxmlformats.schemas.drawingml.x2006.main.impl,
 org.openxmlformats.schemas.drawingml.x2006.picture,
 org.openxmlformats.schemas.drawingml.x2006.picture.impl,
 org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing,
 org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.impl,
 org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing,
 org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing.impl,
 org.openxmlformats.schemas.officeDocument.x2006.customProperties,
 org.openxmlformats.schemas.officeDocument.x2006.customProperties.impl,
 org.openxmlformats.schemas.officeDocument.x2006.docPropsVTypes,
 org.openxmlformats.schemas.officeDocument.x2006.docPropsVTypes.impl,
 org.openxmlformats.schemas.officeDocument.x2006.extendedProperties,
 org.openxmlformats.schemas.officeDocument.x2006.extendedProperties.impl,
 org.openxmlformats.schemas.officeDocument.x2006.relationships,
 org.openxmlformats.schemas.officeDocument.x2006.relationships.impl,
 org.openxmlformats.schemas.presentationml.x2006.main,
 org.openxmlformats.schemas.presentationml.x2006.main.impl,
 org.openxmlformats.schemas.spreadsheetml.x2006.main,
 org.openxmlformats.schemas.spreadsheetml.x2006.main.impl,
 org.openxmlformats.schemas.wordprocessingml.x2006.main,
 org.openxmlformats.schemas.wordprocessingml.x2006.main.impl,
 org.openxmlformats.schemas.xpackage.x2006.digitalSignature,
 org.openxmlformats.schemas.xpackage.x2006.digitalSignature.impl,
 org.reflections,
 org.reflections.adapters,
 org.reflections.scanners,
 org.reflections.serializers,
 org.reflections.util,
 org.reflections.vfs,
 org.slf4j,
 org.slf4j.event,
 org.slf4j.helpers,
 org.slf4j.spi,
 org.w3.x2000.x09.xmldsig,
 org.w3.x2000.x09.xmldsig.impl,
 org.w3c.css.sac,
 org.w3c.css.sac.helpers,
 org.w3c.dom.smil,
 org.w3c.dom.svg,
 org.wildfly.client.config,
 org.wildfly.client.config._private,
 org.wildfly.common,
 org.wildfly.common._private,
 org.wildfly.common.annotation,
 org.wildfly.common.archive,
 org.wildfly.common.array,
 org.wildfly.common.bytes,
 org.wildfly.common.codec,
 org.wildfly.common.context,
 org.wildfly.common.cpu,
 org.wildfly.common.expression,
 org.wildfly.common.flags,
 org.wildfly.common.function,
 org.wildfly.common.iteration,
 org.wildfly.common.lock,
 org.wildfly.common.math,
 org.wildfly.common.net,
 org.wildfly.common.os,
 org.wildfly.common.ref,
 org.wildfly.common.rpc,
 org.wildfly.common.selector,
 org.wildfly.common.string,
 org.wildfly.common.xml,
 org.wildfly.discovery,
 org.wildfly.discovery.impl,
 org.wildfly.discovery.spi,
 org.wildfly.httpclient.common,
 org.wildfly.httpclient.ejb,
 org.wildfly.httpclient.naming,
 org.wildfly.httpclient.transaction,
 org.wildfly.naming.client,
 org.wildfly.naming.client._private,
 org.wildfly.naming.client.remote,
 org.wildfly.naming.client.store,
 org.wildfly.naming.client.util,
 org.wildfly.naming.security,
 org.wildfly.security,
 org.wildfly.security.asn1,
 org.wildfly.security.auth,
 org.wildfly.security.auth.callback,
 org.wildfly.security.auth.client,
 org.wildfly.security.auth.client._private,
 org.wildfly.security.auth.client.spi,
 org.wildfly.security.auth.permission,
 org.wildfly.security.auth.principal,
 org.wildfly.security.auth.realm,
 org.wildfly.security.auth.server,
 org.wildfly.security.auth.server._private,
 org.wildfly.security.auth.server.event,
 org.wildfly.security.auth.server.http,
 org.wildfly.security.auth.util,
 org.wildfly.security.authz,
 org.wildfly.security.cache,
 org.wildfly.security.credential,
 org.wildfly.security.credential._private,
 org.wildfly.security.credential.source,
 org.wildfly.security.credential.source.impl,
 org.wildfly.security.credential.store,
 org.wildfly.security.credential.store._private,
 org.wildfly.security.credential.store.impl,
 org.wildfly.security.encryption,
 org.wildfly.security.evidence,
 org.wildfly.security.http,
 org.wildfly.security.http.basic,
 org.wildfly.security.http.cert,
 org.wildfly.security.http.digest,
 org.wildfly.security.http.impl,
 org.wildfly.security.http.util,
 org.wildfly.security.http.util.sso,
 org.wildfly.security.key,
 org.wildfly.security.keystore,
 org.wildfly.security.manager,
 org.wildfly.security.manager._private,
 org.wildfly.security.manager.action,
 org.wildfly.security.mechanism,
 org.wildfly.security.mechanism._private,
 org.wildfly.security.mechanism.digest,
 org.wildfly.security.mechanism.gssapi,
 org.wildfly.security.mechanism.http,
 org.wildfly.security.password,
 org.wildfly.security.password.impl,
 org.wildfly.security.password.interfaces,
 org.wildfly.security.password.spec,
 org.wildfly.security.password.util,
 org.wildfly.security.pem,
 org.wildfly.security.permission,
 org.wildfly.security.provider.util,
 org.wildfly.security.provider.util._private,
 org.wildfly.security.sasl,
 org.wildfly.security.sasl._private,
 org.wildfly.security.sasl.anonymous,
 org.wildfly.security.sasl.auth.util,
 org.wildfly.security.sasl.localuser,
 org.wildfly.security.sasl.util,
 org.wildfly.security.ssl,
 org.wildfly.security.ssl._private,
 org.wildfly.security.util,
 org.wildfly.security.x500,
 org.wildfly.security.x500._private,
 org.wildfly.security.x500.cert,
 org.wildfly.security.x500.cert._private,
 org.wildfly.security.x500.cert.util,
 org.wildfly.security.x500.util,
 org.wildfly.transaction,
 org.wildfly.transaction.client,
 org.wildfly.transaction.client._private,
 org.wildfly.transaction.client.naming.txn,
 org.wildfly.transaction.client.provider.jboss,
 org.wildfly.transaction.client.provider.remoting,
 org.wildfly.transaction.client.spi,
 org.xnio,
 org.xnio._private,
 org.xnio.channels,
 org.xnio.conduits,
 org.xnio.http,
 org.xnio.management,
 org.xnio.nio,
 org.xnio.sasl,
 org.xnio.ssl,
 org.xnio.streams,
 repackage,
 schemaorg_apache_xmlbeans.system.s8C3F193EE11A2F798ACF65489B9E6078,
 schemaorg_apache_xmlbeans.system.sD023D6490046BA0250A839A9AD24C443,
 schemaorg_apache_xmlbeans.system.sXMLCONFIG,
 schemaorg_apache_xmlbeans.system.sXMLLANG,
 schemaorg_apache_xmlbeans.system.sXMLSCHEMA,
 schemaorg_apache_xmlbeans.system.sXMLTOOLS
