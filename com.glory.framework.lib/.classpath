<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry exported="true" kind="lib" path="lib/excel-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/label-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/labelmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/jboss-jaxb-api_2.3_spec-2.0.1.Final.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/byte-buddy-1.11.12.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/fel.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/antlr-2.7.7.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/batik-all-1.14.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/bsh-core-2.0b4.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/commons-compress-1.18.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/commons-io-2.11.0.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/commons-net-3.6.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/commons-pool2-2.8.0.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/commons-vfs2-2.9.0.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/guava-31.1-jre.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/hibernate-core-5.3.24.Final.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/hibernate-jpa-2.0-api-1.0.1.Final-redhat-2.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/infinispan-commons-13.0.10.Final.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/infinispan-core-13.0.10.Final.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/javassist-3.27.0-GA.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/jboss-client.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/jboss-transaction-api_1.3_spec-2.0.0.Final.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/opencsv-2.0.1.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/poi-4.0.1.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/poi-ooxml-4.0.1.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/poi-ooxml-schemas-4.0.1.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/reflections-0.9.12.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/velocity-engine-core-2.3.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/xml-apis-ext-1.3.04.jar"/>
	<classpathentry exported="true" kind="lib" path="runtime/xmlbeans-3.0.2.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/activeentity-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/activeentitymodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/context-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/contextmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/core.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/ecn-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/ecnmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/edc-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/edcmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/esb.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/excelmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/math.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/mesbase-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/mesbasemodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/mesmsgmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/mm-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/mmmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/msg.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/msgmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/ras-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/rasmodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/security-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/securitymodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/state-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/statemodel.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/system-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/tenant-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/wip-client.jar"/>
	<classpathentry exported="true" kind="lib" path="lib/wipmodel.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="con" path="org.eclipse.pde.core.requiredPlugins"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
