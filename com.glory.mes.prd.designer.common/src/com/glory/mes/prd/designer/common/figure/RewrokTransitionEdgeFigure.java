package com.glory.mes.prd.designer.common.figure;

import org.eclipse.draw2d.ColorConstants;
import org.eclipse.draw2d.PolygonDecoration;

public class RewrokTransitionEdgeFigure extends EdgeFigure {
	
	public RewrokTransitionEdgeFigure() {
		super();
		this.lineForegroundColor = ColorConstants.darkGreen;
	}
	
	protected void setTargetDecoration() {
		PolygonDecoration arrow = new PolygonDecoration();
		arrow.setTemplate(PolygonDecoration.TRIANGLE_TIP);
		arrow.setBackgroundColor(ColorConstants.darkGreen);
		arrow.setForegroundColor(ColorConstants.darkGreen);
		arrow.setOpaque(true);
		arrow.setScale(10, 5);
		setTargetDecoration(arrow);
	}
}
