package com.glory.mes.prd.designer.common.figure;

import org.eclipse.draw2d.ColorConstants;
import org.eclipse.draw2d.Graphics;
import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.jface.resource.ImageDescriptor;
import org.eclipse.swt.graphics.Pattern;

import com.glory.mes.prd.designer.common.Constants;

public class ReworkStateFigure extends NodeFigure {

	public ReworkStateFigure(String nodeType, boolean hideName, ImageDescriptor imageDescriptor) {
		header = new NodeHeaderFigure(nodeType, nodeType, hideName, imageDescriptor, false);
		initialize();
	}
	
	@Override
	protected void initialize() {
		super.initialize();
		setOpaque(false);
	}
	
//	protected void paintBorder(Graphics graphics) {
//		//cover super method
//	}
//	
//	@Override
//	protected void paintClientArea(Graphics graphics) {
//		drawNodeBorder(graphics);
//		super.paintClientArea(graphics);
//	}
//
	protected void drawNodeBorder(Graphics graphics) {
		graphics.pushState();
		Rectangle bounds = getBounds();
		
		int offset = 1;
		Rectangle ovalRect = Rectangle.SINGLETON.setBounds(bounds);
		ovalRect.x += offset;
		ovalRect.y += offset;
		ovalRect.width -= offset * 2;
		ovalRect.height -= offset * 2;
		graphics.setBackgroundColor(ColorConstants.darkGreen);
		graphics.setForegroundColor(Constants.veryLightGray);
		graphics.fillGradient(ovalRect, true);
		graphics.drawRectangle(ovalRect);
/*		int offset = 1;
		Rectangle ovalRect = Rectangle.SINGLETON.setBounds(bounds);
		ovalRect.x += offset;
		ovalRect.y += offset;
		ovalRect.width -= offset * 2;
		ovalRect.height -= offset * 2;
		
		graphics.setBackgroundPattern(new Pattern( null, ovalRect.x, ovalRect.y,
				ovalRect.x, ovalRect.y + ovalRect.height, ColorConstants.lightGreen,
				ColorConstants.lightGray ));
		graphics.fillOval(ovalRect);
		
		Rectangle r = Rectangle.SINGLETON.setBounds(bounds);
		r.x += offset;
		r.y += offset;
		r.width -= offset * 2;
		r.height -= offset * 2;
		
		graphics.setForegroundColor(Constants.veryLightGray);
		graphics.drawOval(r);*/
		
		graphics.popState();
		graphics.restoreState();
	}

}
