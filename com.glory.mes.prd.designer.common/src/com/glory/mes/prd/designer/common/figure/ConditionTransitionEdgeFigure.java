package com.glory.mes.prd.designer.common.figure;

import org.eclipse.draw2d.ColorConstants;
import org.eclipse.draw2d.PolygonDecoration;

public class ConditionTransitionEdgeFigure extends EdgeFigure {
	
	public ConditionTransitionEdgeFigure() {
		super();
		this.lineForegroundColor = ColorConstants.orange;
	}
	
	protected void setTargetDecoration() {
		PolygonDecoration arrow = new PolygonDecoration();
		arrow.setTemplate(PolygonDecoration.TRIANGLE_TIP);
		arrow.setBackgroundColor(ColorConstants.orange);
		arrow.setForegroundColor(ColorConstants.orange);
		arrow.setOpaque(true);
		arrow.setScale(10, 5);
		setTargetDecoration(arrow);
	}
}
