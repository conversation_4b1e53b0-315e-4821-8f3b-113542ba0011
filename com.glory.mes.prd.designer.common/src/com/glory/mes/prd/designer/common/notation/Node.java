package com.glory.mes.prd.designer.common.notation;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.draw2d.geometry.Dimension;
import org.eclipse.draw2d.geometry.Point;
import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.swt.internal.DPIUtil;

public class Node extends AbstractNotationElement {
	
	public static int NODE_WIDTH = 132;
	public static int NODE_HIGHT = 36;
	
	private NodeContainer container;
	
	private Rectangle constraint = new Rectangle(new Point(0, 0), new Dimension(
			DPIUtil.autoScaleUpUsingNativeDPI(NODE_WIDTH), 
			DPIUtil.autoScaleUpUsingNativeDPI(NODE_HIGHT)));
	private List<Edge> leavingEdges = new ArrayList<Edge>();
	private List<Edge> arrivingEdges = new ArrayList<Edge>();
	
	public void setConstraint(Rectangle newConstraint) {
		Rectangle oldConstraint = constraint;
		constraint = newConstraint;
		firePropertyChange("constraint", oldConstraint, newConstraint);
	}
	
	public Rectangle getConstraint() {
		return constraint;
	}
	
	public void addLeavingEdge(Edge edge) {
		leavingEdges.add(edge);
		edge.setSource(this);
		firePropertyChange("leavingEdgeAdd", null, edge);
	}
	
	public void removeLeavingEdge(Edge edge) {
		leavingEdges.remove(edge);
		edge.setSource(null);
		firePropertyChange("leavingEdgeRemove", null, edge);
	}
	
	public List<Edge> getLeavingEdges() {
		return leavingEdges;
	}

	public void addArrivingEdge(Edge edge) {
		arrivingEdges.add(edge);
		edge.setTarget(this);
		firePropertyChange("arrivingEdgeAdd", null, edge);
	}
	
	public void removeArrivingEdge(Edge edge) {
		arrivingEdges.remove(edge);
		edge.setTarget(null);
		firePropertyChange("arrivingEdgeRemove", null, edge);
	}
	
	public List<Edge> getArrivingEdges() {
		return arrivingEdges;
	}
	
	public void setContainer(NodeContainer notationElement) {
		this.container = notationElement;
	}
	
	public  NodeContainer getContainer() {
		return container;
	}

}
