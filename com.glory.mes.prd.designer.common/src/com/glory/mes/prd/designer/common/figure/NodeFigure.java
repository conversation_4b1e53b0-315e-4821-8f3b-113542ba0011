package com.glory.mes.prd.designer.common.figure;

import org.eclipse.draw2d.ChopboxAnchor;
import org.eclipse.draw2d.ColorConstants;
import org.eclipse.draw2d.ConnectionAnchor;
import org.eclipse.draw2d.Figure;
import org.eclipse.draw2d.Graphics;
import org.eclipse.draw2d.IFigure;
import org.eclipse.draw2d.Label;
import org.eclipse.draw2d.ToolbarLayout;
import org.eclipse.draw2d.geometry.Point;
import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.jface.resource.ImageDescriptor;
import com.glory.mes.prd.designer.common.Constants;

public class NodeFigure extends AbstractNodeFigure {

	private FixedAnchor selfReferencingAnchor;
	protected NodeHeaderFigure header;
	private IFigure body;

	public NodeFigure(){
		
	}
	
	public NodeFigure(String nodeType) {
		this(nodeType, nodeType);
	}

	public NodeFigure(String nodeType, ImageDescriptor imageDescriptor) {
		this(nodeType, nodeType, imageDescriptor);
	}

	public NodeFigure(String nodeType, boolean hideName) {
		this(nodeType, nodeType, hideName);
	}

	public NodeFigure(String nodeType, boolean hideName, ImageDescriptor imageDescriptor) {
		this(nodeType, nodeType, hideName, imageDescriptor);
	}

	public NodeFigure(String nodeType, String iconName) {
		this(nodeType, iconName, false);
	}
	
	public NodeFigure(String nodeType, String iconName, ImageDescriptor imageDescriptor) {
		this(nodeType, iconName, false, imageDescriptor);
	}

	public NodeFigure(String nodeType, String iconName, boolean hideName) {
		header = new NodeHeaderFigure(nodeType, iconName, hideName);
		initialize();
	}

	public NodeFigure(String nodeType, String iconName, boolean hideName, ImageDescriptor imageDescriptor) {
		header = new NodeHeaderFigure(nodeType, iconName, hideName, imageDescriptor, false);
		initialize();
	}

	public NodeFigure(String nodeType, ImageDescriptor iconDescriptor,
			boolean hideName) {
		header = new NodeHeaderFigure(nodeType, iconDescriptor, hideName);
		initialize();
	}

	protected void initialize() {
		ToolbarLayout layout = new ToolbarLayout();
		layout.setSpacing(2);
		layout.setStretchMinorAxis(true);
		setLayoutManager(layout);
		setOpaque(false);
		add(header);
		connectionAnchor = new ChopboxAnchor(this);
		selfReferencingAnchor = new FixedAnchor(this);
		body = new Figure();
		body.setLayoutManager(new ToolbarLayout());
		add(body);
		setOpaque(false);
	}
	
	public IFigure getContentPane() {
		return body;
	}

	public void setName(String name) {
		header.setNodeName(name);
		getLayoutManager().layout(this);
	}

	public Label getNameLabel() {
		return header.getNameLabel();
	}

	public ConnectionAnchor getSelfReferencingAnchor() {
		return selfReferencingAnchor;
	}

	protected void paintBorder(Graphics graphics) {
//		Rectangle bounds = getBounds().getCopy();
//		Point origin = bounds.getLocation();
//		int height = bounds.height;
//		int width = bounds.width;
//		graphics.translate(origin);
//		graphics.setForegroundColor(ColorConstants.lightGray);
//		graphics.drawLine(0, 0, width - 2, 0);
//		graphics.drawLine(width - 2, 0, width - 2, height - 2);
//		graphics.drawLine(width - 2, height - 2, 0, height - 2);
//		graphics.drawLine(0, height - 2, 0, 0);
//		graphics.setForegroundColor(Constants.veryLightGray);
//		graphics.drawLine(width - 1, 1, width - 1, height - 1);
//		graphics.drawLine(width - 1, height - 1, 1, height - 1);
	}

	
	@Override
	protected void paintClientArea(Graphics graphics) {
		drawNodeBorder(graphics);
		super.paintClientArea(graphics);
	}
	
	protected void drawNodeBorder(Graphics graphics) {
		graphics.pushState();
		Rectangle bounds = getBounds();
		
		int offset = 1;
		Rectangle ovalRect = Rectangle.SINGLETON.setBounds(bounds);
		ovalRect.x += offset;
		ovalRect.y += offset;
		ovalRect.width -= offset * 2;
		ovalRect.height -= offset * 2;
		graphics.setForegroundColor(ColorConstants.lightGray);
		graphics.fillGradient(ovalRect, true);
		graphics.drawRectangle(ovalRect);
/*		int offset = 1;
		Rectangle ovalRect = Rectangle.SINGLETON.setBounds(bounds);
		ovalRect.x += offset;
		ovalRect.y += offset;
		ovalRect.width -= offset * 2;
		ovalRect.height -= offset * 2;
		
		graphics.setBackgroundPattern(new Pattern( null, ovalRect.x, ovalRect.y,
				ovalRect.x, ovalRect.y + ovalRect.height, ColorConstants.lightGreen,
				ColorConstants.lightGray ));
		graphics.fillOval(ovalRect);
		
		Rectangle r = Rectangle.SINGLETON.setBounds(bounds);
		r.x += offset;
		r.y += offset;
		r.width -= offset * 2;
		r.height -= offset * 2;
		
		graphics.setForegroundColor(Constants.veryLightGray);
		graphics.drawOval(r);*/
		
		graphics.popState();
		graphics.restoreState();
	}

}
