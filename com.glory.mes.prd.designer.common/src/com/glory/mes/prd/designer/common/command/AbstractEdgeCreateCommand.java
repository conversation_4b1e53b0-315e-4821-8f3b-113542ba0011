package com.glory.mes.prd.designer.common.command;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.draw2d.geometry.Dimension;
import org.eclipse.draw2d.geometry.Rectangle;
import org.eclipse.gef.commands.Command;
import com.glory.mes.prd.designer.common.notation.BendPoint;
import com.glory.mes.prd.designer.common.notation.Edge;
import com.glory.mes.prd.designer.common.notation.Node;

public abstract class AbstractEdgeCreateCommand extends Command {
	
	protected Node source;
	protected Node target;
	protected Edge edge;
	
	public void execute() {
		List<BendPoint> thisPoints = edge.getBendPoints();
		if (source == target && thisPoints.isEmpty()) {
			addBendPoints();
		}
		
		/**
		 * �ж��Ƿ����ص���edge
		 */
		List<Edge> arrivingEdges = target.getLeavingEdges();
		for (Edge arriveEdge : arrivingEdges){
			if (arriveEdge.getTarget().equals(source)) {
				addBendPoints();
			}
		}
	}
	
	protected void addBendPoints() {
		Rectangle constraint = source.getConstraint();
		int horizontal = - (constraint.width / 2 + 25);
		int vertical = horizontal * constraint.height / constraint.width;
		BendPoint first = new BendPoint();
		first.setRelativeDimensions(new Dimension(horizontal, 0), new Dimension(horizontal, 0));
		BendPoint second = new BendPoint();
		second.setRelativeDimensions(new Dimension(horizontal, vertical), new Dimension(horizontal, vertical));
		edge.addBendPoint(first);
		edge.addBendPoint(second);
	}
	
	
	public boolean canExecute() {
		if (source == null || target == null) {
			return false;
		} else {
			return true;
		}
	}
	
	public void setSource(Node newSource) {
		source = newSource;
	}
	
	public void setEdge(Edge newEdge) {
		edge = newEdge;
	}
	
	public void setTarget(Node newTarget) {
		target = newTarget;
	}

	public Node getSource() {
		return source;
	}

	public Node getTarget() {
		return target;
	}

	public Edge getEdge() {
		return edge;
	}
	
}
