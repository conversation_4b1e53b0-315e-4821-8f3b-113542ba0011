package com.glory.mes.prd.designer.common.figure;

import org.eclipse.draw2d.IFigure;
import org.eclipse.jface.resource.ImageDescriptor;

import com.glory.mes.prd.designer.common.model.AbstractSemanticElement;
import com.glory.mes.prd.designer.common.model.SemanticElement;
import com.glory.mes.prd.designer.common.notation.NodeContainer;
import com.glory.mes.prd.designer.common.notation.NotationElement;
import com.glory.mes.prd.designer.common.notation.NotationMapping;

public class NodeFigureFactory {
	
	public static NodeFigureFactory INSTANCE = new NodeFigureFactory();
	
	private NodeFigureFactory() {
	}
	
	public IFigure createFigure(NotationElement notationElement) {
		if (notationElement instanceof NodeContainer) {
			return new NodeContainerFigure();
		}
		SemanticElement semanticElement = notationElement.getSemanticElement();
		String label = null;
		ImageDescriptor imageDescriptor = null;
		if (semanticElement instanceof SemanticElement) {
			imageDescriptor = ((SemanticElement)semanticElement).getIconDescriptor();
			label = ((SemanticElement)semanticElement).getLabel();
		}
		if (imageDescriptor == null) return null;
		
		if(semanticElement instanceof AbstractSemanticElement && AbstractSemanticElement.ELEMENT_TYPE_REWORKSTATE.equals(((AbstractSemanticElement)semanticElement).getElementType())){
			return new ReworkStateFigure(label, NotationMapping.hideName(semanticElement.getElementId()), imageDescriptor);
		} else if (semanticElement instanceof AbstractSemanticElement && AbstractSemanticElement.ELEMENT_TYPE_REDIRECT_END_STAET.equals(((AbstractSemanticElement)semanticElement).getElementType())) {
			return new RedirectEndStateFigure(label, NotationMapping.hideName(semanticElement.getElementId()), imageDescriptor);
		}
		
		if (label != null) {
			return new NodeFigure(label, NotationMapping.hideName(semanticElement.getElementId()), imageDescriptor);
		} else {
			return null;
		}
	}

}
