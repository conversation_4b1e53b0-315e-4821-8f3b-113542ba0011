Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: Prd Desinger Common Plug-in
Bundle-SymbolicName: com.glory.mes.prd.designer.common;singleton:=true
Bundle-Version: 8.4.0
Bundle-Activator: com.glory.mes.prd.designer.common.Activator
Bundle-Vendor: GlorySoft
Require-Bundle: org.eclipse.ui;bundle-version="3.107.0",
 org.eclipse.core.runtime;bundle-version="3.11.0",
 org.eclipse.wst.sse.core;bundle-version="1.1.900",
 org.eclipse.core.resources;bundle-version="3.10.0",
 org.eclipse.gef;bundle-version="3.10.0",
 com.glory.framework.lib;bundle-version="8.4.0",
 com.glory.framework.base;bundle-version="8.4.0"
Bundle-ActivationPolicy: lazy
Export-Package: com.glory.mes.prd.designer.common,
 com.glory.mes.prd.designer.common.command,
 com.glory.mes.prd.designer.common.editor,
 com.glory.mes.prd.designer.common.figure,
 com.glory.mes.prd.designer.common.model,
 com.glory.mes.prd.designer.common.notation,
 com.glory.mes.prd.designer.common.part,
 com.glory.mes.prd.designer.common.policy,
 com.glory.mes.prd.designer.common.registry,
 com.glory.mes.prd.designer.common.util,
 com.glory.mes.prd.designer.common.xml
Bundle-RequiredExecutionEnvironment: JavaSE-1.8
