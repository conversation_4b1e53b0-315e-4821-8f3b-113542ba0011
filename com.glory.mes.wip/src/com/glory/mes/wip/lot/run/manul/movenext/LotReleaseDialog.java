package com.glory.mes.wip.lot.run.manul.movenext;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.eclipse.e4.core.services.events.IEventBroker;
import org.osgi.service.event.Event;

import com.glory.framework.activeentity.client.ADManager;
import com.glory.framework.base.entitymanager.glc.GlcEvent;
import com.glory.framework.base.entitymanager.glc.GlcForm;
import com.glory.framework.base.entitymanager.glc.dialog.GlcBaseDialog;
import com.glory.framework.base.ui.forms.field.EntityFormField;
import com.glory.framework.base.ui.forms.field.ListTableManagerField;
import com.glory.framework.base.ui.util.Env;
import com.glory.framework.base.ui.util.Message;
import com.glory.framework.base.ui.util.UI;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.runtime.Framework;
import com.glory.framework.runtime.exceptionhandler.ExceptionHandlerManager;
import com.glory.mes.wip.action.LotAction;
import com.glory.mes.wip.client.LotManager;
import com.glory.mes.wip.model.Lot;
import com.glory.mes.wip.model.LotHold;
import com.glory.framework.core.exception.ExceptionBundle;

public class LotReleaseDialog extends GlcBaseDialog{

    private static final Logger     logger       = Logger.getLogger(LotReleaseDialog.class);

    private static final String FIELD_LOT    = "LotInfo";
    private static final String FIELD_HOLD   = "LotHoldInfo";
    private static final String FIELD_ACTION = "LotReleaseAction";
    protected EntityFormField   lotInfoFormField, lotReleaseActionFormField;
    protected ListTableManagerField lotHoldListTableManagerField;
    protected Lot lot;
	protected Event event;
    
    public LotReleaseDialog(String adFormName, String authority, IEventBroker eventBroker, Lot lot, Event event){
		super(adFormName, authority, eventBroker);
        this.lot = lot;
        this.event = event;
	}
	
	protected void createFormAction(GlcForm form) {
		super.createFormAction(form);		
        try {
            lotInfoFormField = form.getFieldByControlId(FIELD_LOT, EntityFormField.class);
            lotInfoFormField.setValue(lot);
            lotInfoFormField.refresh();

            lotHoldListTableManagerField = form.getFieldByControlId(FIELD_HOLD, ListTableManagerField.class);

            ADManager adManager = Framework.getService(ADManager.class);
            List<LotHold> lotHolds = adManager.getEntityList(Env.getOrgRrn(), LotHold.class, Integer.MAX_VALUE,
                                                             " lotRrn = " + lot.getObjectRrn() + " and lotRrn is not null",
                                                             " holdTime ");
            lotHoldListTableManagerField.getListTableManager().setInput(lotHolds);

            lotReleaseActionFormField = form.getFieldByControlId(FIELD_ACTION, EntityFormField.class);
            lotReleaseActionFormField.setValue(new LotAction());
            lotReleaseActionFormField.refresh();
            
        } catch (Exception e) {
            logger.error("LotReleaseDialog : Init tablelist", e);
            e.printStackTrace();
        }
	}
	
	@Override
	protected void okPressed() {	
		try {
            if (!lotReleaseActionFormField.validate()) {
                return;
            }

            List<Object> selectObjs = lotHoldListTableManagerField.getListTableManager().getCheckedObject();
            if (CollectionUtils.isEmpty(selectObjs)) {
                UI.showInfo(Message.getString(ExceptionBundle.bundle.CommonSelectObject()));
                return;
            }

            LotManager lotManager = Framework.getService(LotManager.class);

            List<LotHold> lotHolds = selectObjs.stream().map(selectObj -> (LotHold) selectObj).collect(Collectors.toList());

            LotAction lotAction = (LotAction) lotReleaseActionFormField.getValue();

            SessionContext sc = Env.getSessionContext();
			if (event.getProperty(GlcEvent.PROPERTY_OPERATOR1) != null) {
				sc.setUserName((String) event.getProperty(GlcEvent.PROPERTY_OPERATOR1));
			}
			
            lotManager.releaseLots(lotHolds, lotAction, sc);
            UI.showError(Message.getString("wip.release_successed"));

			super.okPressed();
		} catch (Exception e) {
			ExceptionHandlerManager.asyncHandleException(e);
		}
		
	}
	
	protected void cancelPressed() {
		super.cancelPressed();
	}

}
